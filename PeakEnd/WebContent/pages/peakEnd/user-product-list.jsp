<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>营销单结果查询</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="userFrom" class="form-inline" id="userFrom" onsubmit="return false" data-toggle="">
       	<input class="hidden" name="mid" value="${param.mid }">
             	 <table class="table table-auto table-bordered table-hover table-condensed text-c" id="" data-mars="Marketing.user_list">
             	 
                             <thead>
	                         	 <tr>
	                         	      <th class="text-c">产品品牌</th>
								      <th class="text-c">产品品类</th>
								      <th class="text-c">产品型号</th>
								      <!-- <th class="text-c">产品描述</th>
	                         	      <th class="text-c">购买日期</th>
	                         	      <th class="text-c">安装日期</th> -->
	                         	      <th class="text-c">用户电话</th>
	                         	      <th class="text-c">用户住址</th>
	                         	      <th class="text-c">营销状态</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                                
                             </tbody>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:BRAND_NAME}}</td>
											<td>{{:PROD_NAME}}</td>
											<td>{{:PRODUCT_MODEL}}</td>
											<td>{{:CUST_PHONE}}</td>
											<td>{{:CUST_ADDR}}</td>
											<td>{{dictFUN:RESULT_STATUS "MARKETING_VISIT_RESULT"}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
		                 </table>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     			<jsp:param value="25" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">

	   $(function(){
		   debugger
			$("#userFrom").render({success:function(req){
				debugger
				} });
	   });
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>