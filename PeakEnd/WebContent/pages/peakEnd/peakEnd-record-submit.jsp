<%@ page language="java" contentType="text/html;charset=UTF-8" %> <%@ include
file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
  <title>话术指引</title>
  <style>
    #menuContent {
      display: none;
      position: absolute;
      border: 1px solid rgb(170, 170, 170);
      max-width: 220px;
      max-height: 350px;
      z-index: 10;
      overflow: auto;
      background-color: #f4f4f4;
    }
  </style>
</EasyTag:override>
<EasyTag:override name="content">
  <form id="editForm" method="post" autocomplete="off">
    <!-- <input class="hidden" name="compensateOrderNo" id="compensateOrderNo" value="${param.compensateOrderNo }">
        <input class="hidden" name="cancelOperatePerson" id="cancelOperatePerson" value="${param.cancelOperatePerson }">
        <input class="hidden" name="compensateCash" id="compensateCash" value="${param.compensateCash }">
        <input class="hidden" name="clientCode" id="clientCode" value="${param.clientCode }">
        <input class="hidden" name="customerPhoneOld" id="customerPhoneOld" value="${param.customerPhone }">
        <input class="hidden" name="applyPersonOld" id="applyPersonOld" value="${param.applyPerson }">
        <input class="hidden" name="command" id="command" value="compensateApplyModify"> -->
    <%--表单展示--%>
    <table class="table table-edit table-vzebra mt-10">
      <tbody>
        <tr>
          <td width="40px">接入单号</td>
          <td>
            <input name="contactOrderNo" id="contactOrderNo"
            data-rules="required" placeholder="请输入接入单号"
            class="form-control input-sm">
          </td>
        </tr>
      </tbody>
    </table>
    <%--操作--%>
    <div class="layer-foot text-c">
      <button
        class="btn btn-sm btn-primary"
        type="button"
        onclick="edit.ajaxSubmitForm()"
      >
        提交
      </button>
      <button
        class="btn btn-sm btn-default ml-20"
        type="button"
        onclick="popup.layerClose()"
      >
        取消
      </button>
    </div>
  </form>
</EasyTag:override>

<EasyTag:override name="script">
  <link
    type="text/css"
    rel="stylesheet"
    href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css"
  />
  <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
  <script
    type="text/javascript"
    src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"
  ></script>
  <script type="text/javascript">
    jQuery.namespace("edit");
    var queryParams = new URLSearchParams(window.location.search);
    var params = {};
    queryParams.forEach(function(value, key) {
        params[key] = value;
    });
    $(function () {
      $("#editForm").custRender(); //加载下拉框
      $("#editForm").render();
      console.log("更新页面 compensateOrderNo", $("#compensateOrderNo").val());
      console.log(
        "更新页面 cancelOperatePerson",
        $("#cancelOperatePerson").val()
      );
    });
    edit.ajaxSubmitForm = function () {
      var data = form.getJSONObject("editForm");
      console.log(data, params, "recordData");
        var data = {
            ...params,
            contactOrderNo: data.contactOrderNo
        }
        console.log(data, "data");
        ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=SubmitErrorMessage",data,function(result) {
            if(result.state == 1){
                layer.msg(result.msg,{icon: 1});
            }else{
                layer.alert(result.msg,{icon: 5});
            }
        })
      //   data.compensateItems = "CASH";
      //   console.log("data:", data);
      //   //手机号必须符合11位数
      //   if (isValidMobileNumber($("#customerPhone").val())) {
      //     var cashCompensationConfig = getCashCompensationConfig(
      //       $("#clientCode").val()
      //     );
      //     if (
      //       getNowUpperLimit(
      //         $("#customerPhone").val(),
      //         cashCompensationConfig,
      //         $("#clientCode").val()
      //       )
      //     ) {
      //       ajax.remoteCall(
      //         "/PeakEnd/servlet/peakEnd?action=DoCompensateService",
      //         data,
      //         function (result) {
      //           console.log("结果：" + result);
      //           console.log("结果str：" + result.toString());
      //           if (result.state == 1) {
      //             window.parent.layer.closeAll();
      //             window.parent.layer.msg(result.msg, { icon: 1 });
      //             window.parent.cash.reload();
      //           } else {
      //             layer.alert(result.msg, { icon: 5 });
      //           }
      //         }
      //       );
      //     }
      //   } else {
      //     layer.alert("号码不规范,请重新修改", { icon: 5 });
      //   }
    };
  </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
