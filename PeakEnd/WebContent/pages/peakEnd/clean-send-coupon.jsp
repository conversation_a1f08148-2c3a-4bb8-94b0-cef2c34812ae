<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>优惠券发送</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars="" method="post"  autocomplete="off" data-mars-prefix="" >
				  <input name="MARKET_ID" type="hidden" value='${param.mid}'>
				  <input name="skuCode" type="hidden" value='${param.skuCode}'>
				  <input id="cardId" type="hidden" value='${param.cardid}'>
				  <input id="cardName" type="hidden" value='${param.cardName}'>
				  <input id="orderId" type="hidden" value='${param.orderId}'>
				  <input id="cardFaceMoney" type="hidden" value='${param.cardFaceMoney}'>
				  <table class="table  table-edit table-vzebra mt-10" >
	                    <tbody>
		                    <%--  <tr>
		                        <td width="40px">用户号码</td>
		                        <td width="260px"><input data-rules="required" id="phone" class="form-control input-sm" value="${param.userPhone}" ></td>
		                     </tr> --%>
			                     <tr>
			                        <td colspan="3">
			                           <div>短信内容</div>
			                           <div><textarea id="sms_content" name="" data-mars="peakEnd.getSMSContent" class="form-control input-sm" rows="6"></textarea></div> 
			                        </td>
		                     </tr>
	                   </tbody>
	              </table>
				  <div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="sendCoupon.sendMsg()">发送</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button"  onclick="layer.closeAll();">取消</button>
				  </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	    jQuery.namespace("sendCoupon");
	    requreLib.setplugs("wdate");
	    var skuCodeList='${param.skuCode}';
        var totalPrice='${param.totalPrice}';
        var skuCnt='${param.skuCnt}';
	  
	    $(function() {
	    	$("#editForm").render();
	    	initCard(skuCodeList,totalPrice,skuCnt);
	    })
	    
	    
	    //营销处理-营销资料查询-处理-短信发送
	    sendCoupon.sendMsg = function(){
	    	if(!form.validate("#editForm")){
				return;
			};
			let arr = [];	
			arr.push("4");
		 	let obj = {
				 	'cardId':$("#cardId").val(),
				 	'cardName':$("#cardName").val(),
				 	'orderId':$("#orderId").val(),
				 	'cardFaceMoney':$("#cardFaceMoney").val(),
				 	'smsContent':$("#sms_content").val(),
					'type': arr,
					'customerName': $('#CUSTOMER_NAME').val(),
					'phone': $("#PHONE").val(),
					'areaName': $('#areaName').val(),
					'areaCode': $('#areaCode').val(),
					'customerAddress': $('#customerAddress').val(),
					'startTime':$("#serviceTime").val(),
					'userAcc':$("#serviceName").val(),
					
					'orgCode':$("#orgCode").val(),
					'brandName':$("#brandName").val(),
					'brandCode':$("#brandCode").val(),
					'prodName':$("#prodName").val(),
					'prodCode':$("#prodCode").val(),
					
					'orderServTypeCode':$("#ORDER_SERV_TYPE_CODE").val(),
					'orderServTypeName':$("#ORDER_SERV_TYPE_NAME").val(),
					'orderSerItem1Name':$("#ORDER_SER_ITEM1_NAME").val(),
					'orderSerItem1Code':$("#ORDER_SER_ITEM1_CODE").val(),
					'orderSerItem2Name':$("#ORDER_SER_ITEM2_NAME").val(),
					'orderSerItem2Code':$("#ORDER_SER_ITEM2_CODE").val(),
					
					'remarks': $('#remarks').val(),//备注
					'result': $('#result').val(),//结果
					'responsible': $('#responsible').val(),//责任方
					'compensateType': $('#compensateType').val(),//补偿类型
					'id': $('#id').val(),
					'uids': uids,//录音id（多个）
					'coefficientId': $('#coefficientId').val(),//折算类型
					'coefficient': $('#coefficient').val(),//折算值
					'serviceOrderNo': $('#serviceOrderNo').val(),//服务单
					'areaNum': $('#areaNum').val(),//
				}
		 	
		 		ajax.remoteCall("/PeakEnd/servlet/peakEnd?action=CleanCompensate",obj,function(result) { 
						//数据校验
						if(result.state == 1){
							if('${param.mid}'==""){
								layer.closeAll();
								parent.layer.closeAll();
								parent.layer.msg("发送成功",{icon: 1});
							}else{
								$("#opSendMess").hide();
								$("#result").val(9).change();
								orderResult='9';
								orderStatus='0';
								layer.closeAll();
								parent.marketingResource.searchData();
								parent.layer.closeAll();
								parent.layer.msg(result.msg,{icon: 1});
							}
							
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					}
				);	
	    }
	    function getCardInfo(data){
	    	var cardId=$(data).val();
	    	ajax.remoteCall("/marketing/servlet/marketing?action=getCardInfo",{cardId:cardId},function(result) { 
				//数据校验
				if(result.state == 1){
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
	    }
	    sendCoupon.changeContent=function(data){
			$("#sms_content").html( $(data).val());
	    }
	    
	    
        var cardList=new Array();
	    //渲染优惠券 商品列表，总价，数量
	    function initCard(skuCodeList,totalPrice,skuCnt){
	    	var data={}
	    	data.skuCodeList=skuCodeList;
	    	data.totalPrice=totalPrice*100;//单位是分
	    	data.skuCnt=skuCnt;
	    	ajax.remoteCall("/marketing/servlet/marketing?action=getCardList",data,function(result) { 
				//数据校验
				if(result.state == 1){
					cardList=result.data.data.data;
					selectOptimalCard();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			})
	    }
	    //渲染并选则最佳的优惠劵
	    function selectOptimalCard(){
		   var maxCardFaceMoney=0;
		   var maxCardFaceMoneyId="";
		   for(var key in cardList){
			   if((cardList[key].minReachAmountLimit==null||cardList[key].minReachAmountLimit<totalPrice)
					   &&(cardList[key].maxCardFaceMoneyId==null||cardList[key].maxCardFaceMoneyId>totalPrice)){//满足限制
				   $("#card_Id").append('<option label="'+cardList[key].cardName+'" value="'+cardList[key].cardId+'">'+cardList[key].cardName+'</option>');
				   if(cardList[key].cardFaceMoney>maxCardFaceMoney){//如果有更加优惠的优惠券
					   maxCardFaceMoney=cardList[key].cardFaceMoney;
					   maxCardFaceMoneyId=cardList[key].cardId;
				   }
			   }
				
			}
		   $("#card_Id").val(maxCardFaceMoneyId).change();
	    }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>