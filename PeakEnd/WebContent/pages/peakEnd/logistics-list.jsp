<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>物流信息列表</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form name="logisticsListForm" class="form-inline" id="logisticsListForm" method="post" onsubmit="return false" autocomplete="off">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 物流信息查询</h5>
	             		 </div>
	             		 <hr style="margin:5px -15px">
	             		 <div class="form-group">
	             		 	   <div class="input-group width-36">
		          		              <span class="input-group-addon">创建时间</span>	
									  <input type="text" name="createTimeStar" id="createTimeStar" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',startDate:'%y-%M-%d 00:00:00',maxDate:'#F{$dp.$D(\'createTimeEnd\')}'})">
									  <span class="input-group-addon">-</span>	
									  <input type="text" name="createTimeEnd" id="createTimeEnd"  class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',startDate:'%y-%M-%d 23:59:59',minDate:'#F{$dp.$D(\'createTimeStar\')}'})">									  
							   </div>  
							  <div class="input-group width-36">
							    	  <span class="input-group-addon">美的后台订单号</span>	
			                          <input type="text" name="mideaOrderNo" class="form-control input-sm" >
							   </div>
							   <div class="input-group width-36">
							    	  <span class="input-group-addon">邮寄姓名</span>	
			                          <input type="text" name="postName" class="form-control input-sm" >
							   </div>
							   <div class="input-group width-36">
							    	  <span class="input-group-addon">邮寄电话</span>	
			                          <input type="text" name="postTel" class="form-control input-sm" >
							   </div>
							   <div class="input-group width-36">
							    	  <span class="input-group-addon">单号</span>	
			                          <input type="text" name="deliveryNo" class="form-control input-sm" >
							   </div>
						  </div>
						  <div class="form-group">
							   <div class="input-group input-group-sm pull-right">
									  <button type="button" class="btn btn-sm btn-default mr-10" onclick="logisticsList.reset()">重置</button>
									  <button type="button" class="btn btn-sm btn-default mr-10" onclick="logisticsList.search()">搜索</button>
									  <EasyTag:res resId="peak_end_logistics_import">
									  <button type="button" class="btn btn-sm btn-success mr-10" onclick="logisticsList.importData()"><span class="glyphicon glyphicon-import"></span>导入</button>
									  </EasyTag:res>
									  
							   </div>
						  </div>
             	    </div> 
	              	<div class="ibox-content ">
	              		<div class="row table-responsive" ><!-- style="max-height:700px;" -->
	              		  <table style="border: 0px solid #fff;" class="table table-bordered" id="logisticsListData" data-mars="logistics.list">
	              		  		<thead>
	                         	 <tr>
	                         	      <th class="text-c" >序号</th>
	                         	      <th class="text-c" >美的后台订单号</th>
	                         	      <th class="text-c" >商品名称</th>
	                         	      <th class="text-c" >数量</th>
								      <th class="text-c" >邮寄姓名</th>
								      <th class="text-c" >邮寄电话</th>
								      <th class="text-c" >邮寄地址</th>
									 <th class="text-c" style="width:50px;max-width:50px;min-width:50px">查看</th>
								      <th class="text-c" >备注</th>
								      <th class="text-c" >快递</th>
								      <th class="text-c" >单号</th>
								      <th class="text-c" >创建账号</th>
								      <th class="text-c" >创建时间</th>
								      <th class="text-c" >修改账号</th>
								      <th class="text-c" >修改时间</th>
								      <th class="text-c" >操作</th>
		   						 </tr>
                             </thead>
							 <tbody id="dataList">
							 
							 </tbody>
	              		  </table>
						 <script id="list-template" type="text/x-jsrender">
												   {{for list}}
														<tr>
			                             					 <td>{{:#index+1}}</td>
															 <td class="text-c">{{:MIDEA_ORDER_NO}}</td>
															 <td class="text-c">{{:PRODUCT_NAME}}</td>
															 <td class="text-c">{{:PRODUCT_NUM}}</td>
															 <td class="text-c">{{call:POST_NAME POST_NAME_data #index+1 'name' fn='getData2'}}</td>
															 <td class="text-c">{{call:POST_TEL POST_TEL_data #index+1 'phone' fn='getData2'}}</td>
															 <td class="text-c">{{call:POST_ADDRESS POST_ADDRESS_data #index+1 'addr' fn='getData2'}}</td>
															 <td title="査看" class="text-c">
												<span onclick="showDetail('{{:#index+1}}','{{:POST_NAME_data}}','{{:POST_TEL_data}}','{{:POST_ADDRESS_data}}')" class="glyphicon glyphicon-eye-open"
												id="show{{:#index+1}}"
												style="color:rgb(255,140,60);">
												</span>
											</td>
															 <td class="text-c">{{:REMARK}}</td>
															 <td class="text-c">{{:EXPRESS_DELIVERY}}</td>
															 <td class="text-c">{{:DELIVERY_NO}}</td>
															 <td class="text-c">{{:CREATE_ACC}}</td>
															 <td class="text-c">{{:CREATE_TIME}}</td>
															 <td class="text-c">{{:UPDATE_ACC}}</td>
															 <td class="text-c">{{:UPDATE_TIME}}</td>
															 <td>
																<EasyTag:res resId="peak_end_logistics_delete">
                                          							<a href="javascript:logisticsList.del('{{:ID}}')">删除</a>
 																</EasyTag:res>
                                      						 </td>
														</tr>
												   {{/for}}
																		 
						</script>
	              		</div><!--  row table-responsive -->
	                    <div class="row paginate" id="page">
	                    		<jsp:include page="/pages/common/pagination.jsp">
	                    			<jsp:param value="10" name="pageSize"/>
	                    		</jsp:include>
	                    </div> 
	                 </div>
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type ="text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type = "text/javascript">
		jQuery.namespace("logisticsList");
	    requreLib.setplugs("wdate");
	    $(function(){
	    	logisticsList.init();
	    	$("#logisticsListForm").render({success : function(result){
				logisticsList.search();
	    	}});
	    });
	    
	    function reload(){
			logisticsList.search();
	    }

	    logisticsList.init = function() {
    		$("#createTimeStar").val(getTodayDate(-30)+" 00:00:00");
	    	$("#createTimeEnd").val(getTodayDate()+" 23:59:59");
	    }
	    
	    logisticsList.search = function(){
    		$("#logisticsListForm").searchData();
	    }
	    
	    /*重置*/
		logisticsList.reset = function() {
			document.logisticsListForm.reset();
			$("#createTimeStar").val(getTodayDate(-30)+" 00:00:00");
	    	$("#createTimeEnd").val(getTodayDate()+" 23:59:59");
		}
	    
		logisticsList.importData = function(){
			 popup.layerShow({type:1,title:"导入",offset:'20px',area:['450px','220px']},"${ctxPath}/pages/peakEnd/logistics-import.jsp",null);
		}
		
		logisticsList.del=function(id){
			var ids=[id];
			if(confirm("确认要删除吗？")){
				ajax.remoteCall("${ctxPath}/servlet/logistics?action=logisticsDelete",{ids:ids},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1});
						logisticsList.search();
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			} 
		}
		function showDetail(id,customerName,customerTel1,address){
			showDetailCommon({
				"model":"PeakEnd",
				"url":"/PeakEnd/pages/peakEnd/logistics-list.jsp",
				"action":"acces",
				"describe":"用户査询[物流信息列表]数据，査看[{{customerName}}]敏感信息:[用户号码：{{customerTel1}},地址：{{address}}]"},id,customerName,address,customerTel1);
		}
	</script>
	<script type="text/javascript" src="/iccportal5/static/js/privacyUtil.js"></script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>