<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>话术指引</title>
    <style>
        #menuContent {
            display: none;
            position: absolute;
            border: 1px solid rgb(170, 170, 170);
            max-width: 220px;
            max-height: 350px;
            z-index: 10;
            overflow: auto;
            background-color: #f4f4f4
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="editForm" method="post" autocomplete="off">
        <input class="hidden" name="compensateOrderNo" id="compensateOrderNo" value="${param.compensateOrderNo }">
        <input class="hidden" name="cancelOperatePerson" id="cancelOperatePerson" value="${param.cancelOperatePerson }">
        <input class="hidden" name="compensateCash" id="compensateCash" value="${param.compensateCash }">
        <input class="hidden" name="clientCode" id="clientCode" value="${param.clientCode }">
        <input class="hidden" name="customerPhoneOld" id="customerPhoneOld" value="${param.customerPhone }">
        <input class="hidden" name="applyPersonOld" id="applyPersonOld" value="${param.applyPerson }">
        <input class="hidden" name="command" id="command" value="compensateApplyModify">
            <%--表单展示--%>
        <table class="table table-edit table-vzebra mt-10">
            <tbody>
            <tr>
                <td width="40px">电话号码</td>
                <td>
                    <input name="customerPhone" id="customerPhone" data-rules="required" placeholder="请输入电话号码"
                           class="form-control input-sm" value="${param.customerPhone}">
                </td>
            </tr>
            </tbody>
        </table>
            <%--操作--%>
        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary" type="button" onclick="edit.ajaxSubmitForm()">保存</button>
            <button class="btn btn-sm btn-default ml-20" type="button" onclick="popup.layerClose()">取消</button>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css"/>
    <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
    <script type="text/javascript">
        jQuery.namespace("edit")
        $(function () {
            $("#editForm").custRender();//加载下拉框
            $("#editForm").render();
            console.log("更新页面 compensateOrderNo", $("#compensateOrderNo").val());
            console.log("更新页面 cancelOperatePerson", $("#cancelOperatePerson").val());
        });
        edit.ajaxSubmitForm = function () {
            var data = form.getJSONObject("editForm");
            data.compensateItems = "CASH"
            console.log("data:", data);
            //手机号必须符合11位数
            if (isValidMobileNumber($("#customerPhone").val())){
                var cashCompensationConfig = getCashCompensationConfig($("#clientCode").val());
                if (getNowUpperLimit($("#customerPhone").val(),cashCompensationConfig,$("#clientCode").val())){
                    ajax.remoteCall("/PeakEnd/servlet/peakEnd?action=DoCompensateService", data, function (result) {
                            console.log("结果：" + result)
                            console.log("结果str：" + result.toString())
                            if (result.state == 1) {
                                window.parent.layer.closeAll();
                                window.parent.layer.msg(result.msg, {icon: 1});
                                window.parent.cash.reload()
                            } else {
                                layer.alert(result.msg, {icon: 5});
                            }
                        }
                    );
                }
            }
            else {
                layer.alert("号码不规范,请重新修改", {icon: 5});
            }
        };

        //定义正则表达式
        const mobileRegex = /^1[3-9]\d{9}$/;
        //判断手机号是否要求
        function isValidMobileNumber(number) {
            return mobileRegex.test(number);
        }

        function getCashCompensationConfig(clientCode) {
            var config = {};
            //获取现金补偿配置信息
            ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=GetCashCompensationConfig", {"clientCode": clientCode}, function (result) {
                if (result && result.data) {
                    cashConfig = result.data;
                    if (cashConfig.config) {
                        config = cashConfig.config;
                    }
                }
            }, {async: false});
            return config;
        }


        function getNowUpperLimit(customerPhone, cashConfig, clientCode) {
            var flag = true;
            var compensateCashStr = $("#compensateCash").val();
            var compensateCash = 0;
            if(compensateCashStr){
                compensateCash = parseFloat(compensateCashStr);
            }
            //获取现金补偿配置信息
            ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=GetNowUpperLimit", {"customerPhone": customerPhone,"clientCode": clientCode}, function (result) {
                if (result && result.data) {
                    var limitConfig = result.data;
                    var agentAmountLimit = limitConfig.agentAmountLimit;
                    var custAmountLimit = limitConfig.custAmountLimit;
                    var custNumLimit = limitConfig.custNumLimit;
                    var accMonthlyimitMax = 0;
                    var userMonthlyimitMax = 0;
                    var currentUserRepetitionCountMax = 0;
                    var account = 0;
                    if (cashConfig && cashConfig.length > 0) {
                        for (var i = 0; i < cashConfig.length; i++) {
                            var SINGLE_USER_MAX_AMOUNT = cashCompensateConfig[i].SINGLE_USER_MAX_AMOUNT
                            if(SINGLE_USER_MAX_AMOUNT) {
                                var singleUserMaxCount = parseFloat(SINGLE_USER_MAX_AMOUNT);
                                if (singleUserMaxCount && singleUserMaxCount > account) {
                                    account = parseFloat(singleUserMaxCount)
                                }
                            }
                            var ACC_MONTHLY_LIMIT = cashConfig[i].ACC_MONTHLY_LIMIT
                            if (ACC_MONTHLY_LIMIT) {
                                var accMonthlyimit = parseFloat(ACC_MONTHLY_LIMIT);
                                if (accMonthlyimit && accMonthlyimit > accMonthlyimitMax) {
                                    accMonthlyimitMax = accMonthlyimit
                                }
                            }
                            var USER_MONTHLY_LIMIT = cashConfig[i].USER_MONTHLY_LIMIT
                            if (USER_MONTHLY_LIMIT) {
                                var userMonthlyimit = parseFloat(USER_MONTHLY_LIMIT);
                                if (userMonthlyimit && userMonthlyimit > userMonthlyimitMax) {
                                    userMonthlyimitMax = userMonthlyimit
                                }
                            }
                            var CURRENT_USER_REPETITION_COUNT = cashConfig[i].CURRENT_USER_REPETITION_COUNT
                            if (CURRENT_USER_REPETITION_COUNT) {
                                var currentUserRepetitionCount = parseFloat(CURRENT_USER_REPETITION_COUNT);
                                if (currentUserRepetitionCount && currentUserRepetitionCount > currentUserRepetitionCountMax) {
                                    currentUserRepetitionCountMax = currentUserRepetitionCount
                                }

                            }
                        }
                    }
                    /*if (compensateCash > account) {
                        layer.alert("本次补偿金额超过配置的用户单次补偿最大金额(" + account + ")", {icon: 5});
                        flag = false;
                        return flag;
                    }*/
                    //判断操作人不一致将本条数据的金额进行补充
                    if($("#applyPersonOld").val() !=  limitConfig.operatePerson){
                        agentAmountLimit += compensateCash;
                    }
                    if (agentAmountLimit > accMonthlyimitMax) {
                        layer.alert("超过坐席自然月补偿金额上限，如仍需补偿，请线下升级", {icon: 5});
                        flag = false;
                        return flag;
                    }
                    if($("#customerPhoneOld").val() !=  customerPhone){
                        custAmountLimit += compensateCash;
                        custNumLimit += 1;
                    }
                    if (custAmountLimit > userMonthlyimitMax) {
                        layer.alert("超过用户累计补偿金额上限，如仍需补偿，请线下升级", {icon: 5});
                        flag = false;
                        return flag;
                    }
                    if (custNumLimit > currentUserRepetitionCountMax) {
                        layer.alert("超过用户累计补偿次数上限，如仍需补偿，请线下升级", {icon: 5});
                        flag = false;
                        return flag;
                    }
                }
            }, {async: false});
            return flag;
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
