<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>峰终回访结果查询</title>
	<style>
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form autocomplete="off" name="searchForm"class="form-inline" id="searchForm">
		<div class="ibox">
			   <div class="input-group input-group-sm hidden" style="width: 200px;">
				      <span class="input-group-addon" style="width: 80px;">受访人电话</span>	
					  <input type="text" name="CUSTOMER_MOBILEPHONE1" id="CUSTOMER_MOBILEPHONE1" class="form-control input-sm" >
			   </div>
			<div class="ibox-content">
				<div class="row table-responsive">
					<table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" data-mars="order.resultList">
						<thead>
							<tr>
								<th class="text-c">序号</th>
								<th class="text-c">操作</th>
								<th class="text-c">产品主体</th>
								<th class="text-c">产品品类</th>
								<th class="text-c">用户姓名</th>
								<th class="text-c">区号</th>
								<th class="text-c">用户号码</th>
								<th class="text-c">用户地址</th>
								<th class="text-c">回访结果</th>
								<th class="text-c">服务单号</th>
								<th class="text-c">档案编码</th>
								<th class="text-c">责任方</th>
								<th class="text-c">回访坐席</th>
								<th class="text-c">回访时间</th>
								<th class="text-c">数据来源</th>
							</tr>
						</thead>
						<tbody id="dataList"></tbody>
					</table>
					<script id="list-template" type="text/x-jsrender">
						{{for list}}
							<tr>
			                    <td>{{:#index+1}}</td>
								<td><a href="javascript:revisit.detail('{{:RESULT_ID}}')">查看</a></td>
							    <td>{{sysCodeFUN:ORG_CODE "ORG_CODE"}}</td>
                                <td>{{:PROD_NAME}}</td>
                                <td>{{:CUSTOMER_NAME}}</td>
                                <td>{{:AREA_NUM}}</td>
                                <td>{{:CUSTOMER_MOBILEPHONE1}}</td>
                                <td>{{:CUSTOMER_ADDRESS}}</td>
                                <td>{{dictFUN:VISIT_RESULT 'PEAK_END_RESULT'}}</td>
                                <td>{{:SERVICE_ORDER_NO}}</td>
                                <td>{{:ARCHIVES_NO}}</td>
                                <td>{{dictFUN:RESPONSIBLE 'PEAK_END_RESPONSIBLE'}}</td>
                                <td>{{:VISIT_ACC}}</td>
                                <td>{{:VISIT_TIME}}</td>
、								<td>{{dictFUN:DATA_SOURCES 'PEAKEND_DATA_SOURCE'}}</td>
							</tr>
						{{/for}}					         
					</script>
				</div>
				<div class="row paginate" id="page">
					<jsp:include page="/pages/common/pagination.jsp">
						<jsp:param value="10" name="pageSize" />
					</jsp:include>
				</div>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
	<script type="text/javascript"
		src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript">
		jQuery.namespace("revisit");
		requreLib.setplugs('wdate')//加载时间控件
		function reload() {
			$("#searchForm").searchData();
		}
		$(function() {
			$("#CUSTOMER_MOBILEPHONE1").val($("#PHONE").val());
			$("#searchForm").render({success : function(result) {
				
			}});
		});
		function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$(".moreSearch").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
		}
		
		revisit.exportExcel = function(){
			var startDate = $("#start_visit_time").val();
			var endDate = $("#end_visit_time").val();
			var diffTime = diffDateTime(startDate,endDate);
			var retMsg = checkTimeRange("1",diffTime);
			if(retMsg != true){
				alert(retMsg);
				return false;
			}
			location.href = "${ctxPath}/servlet/export?action=ExportResultList&"
				+ $("#searchForm").serialize();
		}
		
		revisit.detail = function(id){
			var url="${ctxPath}/servlet/peakEnd?action=peakEndResult&type=2";
		    popup.layerShow({type:2,title:'回访详情',area:['1130px','750px'],offset:'20px'},url,{id:id});
		}
		
		function reset() {
			document.searchForm.reset();
			var startDate = getTodayDate(-29);
			var endDate = getTodayDate();
			$("#start_visit_time").val(startDate + " 00:00:00");
			$("#end_visit_time").val(endDate + " 23:59:59");
			$("#tableHead").data("mars", "order.resultList");
			$("#searchForm").searchData();
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>