<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	 <title>服务补偿</title>
	 <style>
		.info-title p:before {
		    content: '';
		    position: absolute;
		    background-color: #337ab7;
		    left: -1px;
		    height: 15px;
		    width: 3px;
		    top: 50%;
		    margin-top: -5px;
		 }
		.info-title p {
		    border-bottom: none;
		    position: relative;
		    height: 35px
		 }
		 .new-input-group input.form-control:first-child{border-top-right-radius:3px;border-bottom-right-radius:3px;}
		 .new-input-group .input-group-addon{cursor:pointer;border:none;background:none;padding-right:0px}
		 .new-input-group .input-group-addon i{color:#17a6f0;font-size:14px}
		 .text-red{
		 	color: red!important
		 }
		 .check_error{
		  	border: 2px solid red !important;
		    //outline: 0 !important;
		   // box-shadow: 0px 0px 25px 0px red !important;
		  }
		  .threeItem{
		  	padding: 0 5px;
		  }
		  .checkRepeat{
			  color: red;
	    	  padding-left: 20px ;
	    	}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	    <form class="ibox-content mb-20 mr-15 ml-15" id="detailFrom"  data-mars="peakEnd.getOrderDetail" method="post" data-mars-prefix="peakEnd.">
	    	<input name="peakEnd.ID" id='id' class="form-control input-sm" type="hidden" value="${orderId}">
	    	<input name="" id='coefficient' class="form-control input-sm" type="hidden" value="${coefficient}">
	    	<input name="" id='coefficientId' class="form-control input-sm" type="hidden" value="${coefficientId}">
	    	<input name="" id='serviceOrderNo' class="form-control input-sm" type="hidden" value="${SERVICE_ORDER_NO}">
	    	<input name="resultId" id='resultId' class="form-control input-sm" type="hidden" value="${resultId}">
             <div class="info-title" >
		  		<p>用户信息</p>
		  		<span class="checkRepeat pull-right" id="checkRepeatConetnt"></span>
		     </div>
		     <table class="table table-edit table-vzebra">
                 <tbody>
                     <tr>
	                      <td class="text-red ">用户姓名</td>
	                      <td><input type="text" name="peakEnd.CUSTOMER_NAME"  onchange="checkSurname(this.value)" id="CUSTOMER_NAME" data-rules="required" class="form-control input-sm" ></td>
	                      <td class="text-red ">联系电话</td>
	                    <td>
	                      <div class="input-group input-group-sm new-input-group">
	                            <input type="text" name="PHONE"   id ="PHONE" data-rules="required" onchange="checkPhone(this),changePhone(this)" class="form-control input-sm" value="" onchange="changeCustomerPhone(this)" >
	                            <span class="input-group-addon" onclick="Call('PHONE')"><i class="glyphicon glyphicon-earphone"></i></span>
	                        </div>
	                    </td>     
	                      <td>补偿类型</td>
	                      <td>
		                      <select readonly  class="form-control input-sm" name="peakEnd.COMPENSATE_TYPE" id="compensateType" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PEAKEND_COMPENSATE_TYPE')"  onfocus="this.defaultIndex=this.selectedIndex;" onchange="this.selectedIndex=this.defaultIndex;">
		                             <option value="" >-请选择-</option>
	                          </select>
	                      </td>
	                      <td>服务时间</td>
	                      <td>
	                           <input type="text" name="peakEnd.SERVICE_TIME" id="serviceTime" class="form-control input-sm" 	readonly>
	                      </td>
	                      <td>服务坐席</td>
	                      <td>
	                           <input type="text" name="peakEnd.SERVICE_NAME" id="serviceName" class="form-control input-sm" 	readonly>
	                      </td>
                     </tr>
                     <tr>
	                    <td class="text-red ">电话区号</td>
                      	<td class="remind-content">			                      
                          <div class="input-group input-group-sm new-input-group">
                             <input type="text" name="peakEnd.AREA_NUM" id="areaNum" data-rules="required" class="form-control input-sm" 	readonly>
                             <span class="input-group-addon" onclick="phoneCode('areaNum')"><i class="glyphicon glyphicon-zoom-in"></i></span>
                          </div>
	                    </td>  	
	                    <td>用户电话1</td>
	                    <td>
	                      <div class="input-group input-group-sm new-input-group">
	                            <input type="text" name="peakEnd.CUSTOMER_MOBILEPHONE1"  id ="CUSTOMER_MOBILEPHONE" class="form-control input-sm" readonly >
	                            <span class="input-group-addon" onclick="Call('peakEnd.CUSTOMER_MOBILEPHONE1')"><i class="glyphicon glyphicon-earphone"></i></span>
	                        </div>
	                    </td>
	                    <td>用户电话2</td>
	                    <td> 
	                        <div class="input-group input-group-sm new-input-group">
	                            <input type="text" name="peakEnd.CUSTOMER_MOBILEPHONE2"  id='CUSTOMER_MOBILEPHONE2' class="form-control input-sm" readonly >
	                            <span class="input-group-addon" onclick="Call('CUSTOMER_MOBILEPHONE2')"><i class="glyphicon glyphicon-earphone"></i></span>
	                        </div>
	                    </td>
	                    <td>用户电话3</td>
	                    <td>
	                         <div class="input-group input-group-sm new-input-group">
	                            <input type="text" name="peakEnd.CUSTOMER_MOBILEPHONE3"   id='CUSTOMER_MOBILEPHONE3'  class="form-control input-sm" readonly>
	                            <span class="input-group-addon" onclick="Call('CUSTOMER_MOBILEPHONE3')"><i class="glyphicon glyphicon-earphone"></i></span>
	                         </div>
	                     </td>
	                     <td>事业部</td>
	                      <td>
	                      	<select  name="peakEnd.ORG_CODE" id ="orgCode"  class="form-control input-sm" disabled="disabled" data-cust-context-path="/neworder"  data-cust-mars="comm.sysCode('ORG_CODE')">
	                              <option value="">--请选择--</option>
	                        </select>
	                      </td>
                     </tr>
	                 
	                  <tr>
						   <td class="text-red ">区域</td>
	                      <td class="remind-content">
	                      	<input type="text" name="peakEnd.AREA_CODE" id="areaCode" data-rules="required" class="form-control input-sm" readonly="readonly">
	                      </td>
	                      <td colspan="2">
	                          <input type="text" name="peakEnd.AREA_NAME" id="areaName" data-rules="required" class="form-control input-sm">
	                      </td>
	                      <td class="text-red text-r ">用户住址</td>
	                      <td colspan="3" class="remind-content">
							  <div class="input-group input-group-sm new-input-group" style="width:100%">
		                      	  <input type="text" name="peakEnd.CUSTOMER_ADDRESS" onchange="orderAddressCheck()" id="customerAddress" data-rules="required" class="form-control input-sm" >
	                          </div>
	                      </td>
	                      <td style="text-align: right;">品牌</td>
	                      <td>
	                      	<input class="form-control input-sm" id="brandName" name="peakEnd.BRAND_NAME" readonly>
	                      	<input class="hidden" id="brandCode" name="peakEnd.BRAND_CODE" >
	                      </td>
					</tr>
					<tr>
						  <td>服务请求</td>
	                      <td>
	                      	<input class="form-control input-sm" id="ORDER_SERV_TYPE_NAME" name="peakEnd.ORDER_SERV_TYPE_NAME" readonly>
	                      	<input class="hidden" id="ORDER_SERV_TYPE_CODE" name="peakEnd.ORDER_SERV_TYPE_CODE" readonly>
	                      </td>
	                      <td class="hidden">服务请求大类</td>
	                      <td class="hidden">
	                      	<input class="form-control input-sm" id="ORDER_SER_ITEM1_NAME" name="peakEnd.ORDER_SER_ITEM1_NAME" readonly>
	                      	<input class="form-control input-sm" id="ORDER_SER_ITEM1_CODE" name="peakEnd.ORDER_SER_ITEM1_CODE" readonly>
	                      </td>
	                      <td>服务请求小类</td>
	                      <td>
	                      	<input class="form-control input-sm"  id='ORDER_SER_ITEM2_NAME' name="peakEnd.ORDER_SER_ITEM2_NAME" readonly>
	                      	<input class="hidden"  id='ORDER_SER_ITEM2_CODE' name="peakEnd.ORDER_SER_ITEM2_CODE" readonly>
	                      </td>
	                      <td>网点</td>
	                      <td>
	                      	<input class="form-control input-sm"  id='UNIT_NAME' name="peakEnd.UNIT_NAME" readonly>
	                      	<input class="hidden"  id='UNIT_CODE' name="peakEnd.UNIT_CODE" readonly>
	                      </td>
	                      <td>型号</td>
	                      <td>
	                      	<input class="form-control input-sm"  id='PRODUCT_MODEL' name="peakEnd.PRODUCT_MODEL" readonly>
	                      	<input class="hidden"  id='PRODUCT_CODE' name="peakEnd.PRODUCT_CODE" readonly>
	                      </td>
	                      <td>品类</td>
	                      <td>
	                      	<input class="form-control input-sm" id="prodName" name="peakEnd.PROD_NAME" readonly>
	                      	<input class="hidden" id="prodCode" name="peakEnd.PROD_CODE" readonly>
	                      </td>
	                 </tr>
					 <tr>
				 		 <td class="text-red">回访结果</td>
	                     <td>
		                      <select  class="form-control input-sm" name="peakEnd.RESULT" id="result" data-rules="required" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PEAK_END_RESULT')"  onfocus="this.defaultIndex=this.selectedIndex;" >
		                             <option value="" >-请选择-</option>
	                          </select>
	                      </td>
	                      <td class="text-red ">责任方</td>
	                      <td>
	                      	  <select  class="form-control input-sm" name="peakEnd.RESPONSIBLE" id="responsible" data-rules="required" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PEAK_END_RESPONSIBLE')"  onfocus="this.defaultIndex=this.selectedIndex;" >
		                             <option value="" >-请选择-</option>
	                          </select>
	                      </td>
                      	  <td>补偿备注</td>
                     	  <td colspan="5">
                     		<input type="text" name="peakEnd.RESULT_CONTENT" id="remarks" class="form-control input-sm" 	>
                     	  </td>
	                 </tr>
	                  <tr>
	                     <td>补偿脚本</td>
	                      <td colspan="9"><textarea class="form-control input-sm" id="MARCKET_SCRIPT" name="peakEnd.MARCKET_SCRIPT" rows="4" readonly></textarea></td>
	                 </tr>
	             </tbody>
             </table>
			 <div>
		 </div>
		</form>
		 <div id="pageLoader" class="ibox-panel" style="height: 560px">
		            <div class="ibox-panel-title clearfix" >
		                <ul class="page-links ">
		                  
		                    <li id='page_service_list'>
		                        <a data-type="page"  data-href="${ctxPath}/pages/peakEnd/gift/gift.jsp?phone=${CUSTOMER_MOBILEPHONE1}">
		                            <span>实物礼品服务</span>
		                        </a>
		                    </li>
		                    <li id='page_service_list2'>
		                        <a data-type="page"  data-href="${ctxPath}/pages/peakEnd/clean-service-list.jsp?id=${param.id}">
		                            <span>清洗券赠送</span>
		                        </a>
		                    </li>
		                    <%-- <li id='page_service_list3'>
		                        <a data-type="page"  data-href="${ctxPath}/pages/peakEnd/service-list.jsp?id=${param.id}">
		                            <span>商城券赠送</span>
		                        </a>
		                    </li> --%>
		                    <li>
		                        <a data-type="page" data-href="${ctxPath}/pages/peakEnd/peakEnd-history.jsp?CUSTOMER_MOBILEPHONE=${CUSTOMER_MOBILEPHONE1}">
		                            <span>历史赠送记录</span>
		                        </a>
		                    </li>
		                    <li>
		                        <a data-type="page" id='page_service_list' data-href="${ctxPath}/pages/peakEnd/peakEnd-result-list.jsp?CUSTOMER_MOBILEPHONE=${CUSTOMER_MOBILEPHONE1}">
		                            <span>历史补偿回访</span>
		                        </a>
		                    </li>
		                    <li>
		                        <a data-type="page" data-href="/neworder/pages/revisit/revisit-material.jsp?archivesNo=${ARCHIVES_NO}&orgCode=${ORG_CODE}">
		                            <span>滤芯更换明细</span>
		                        </a>
		                    </li>
		                    <li>
		                        <a data-type="page" data-href="/neworder/pages/revisit/revisit-charge-details.jsp?serviceOrderNo=${SERVICE_ORDER_NO}&archivesNo=${ARCHIVES_NO}">
		                            <span>收费信息查询</span>
		                        </a>
		                    </li>
		                    <li>
		                        <a data-type="page" data-href="/neworder/pages/revisit/revisit-feelist.jsp?archivesNo=${ARCHIVES_NO}&orgCode=${ORG_CODE}">
		                            <span>费用明细</span>
		                        </a>
		                    </li>
		                    <li>
		                        <a data-type="page" data-href="/neworder/pages/access/service-history.jsp?customerCode=${CUSTOMER_CODE}&orgCode=${ORG_CODE}&prodCode=${PROD_CODE}">
		                            <span>历史服务</span>
		                        </a>
		                    </li>
		                    <li>
		                        <a data-type="page" data-href="/neworder/pages/access/complain-history.jsp?customerCode=${CUSTOMER_CODE}&orgCode=${ORG_CODE}">
		                            <span>历史投诉</span>
		                        </a>
		                    </li>
		                    <li>
		                        <a data-type="page" data-href="/neworder/pages/revisit/revisit-history.jsp?archivesNo=${ARCHIVES_NO}&customerCode=${CUSTOMER_CODE}">
		                            <span>历史回访</span>
		                        </a>
		                    </li>
		                </ul>
		            </div>
		            <div class="ibox-panel-content" style="margin:0 15px 15px;">
		            	
		            </div>
	        </div>	
		
		
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="/PeakEnd/static/js/laydate.js"></script>

	<script type="text/javascript">
		jQuery.namespace("peakEndDetail");
		jQuery.namespace("orderfunc");
		requreLib.setplugs("wdate")
	    var options =[];
		var marcketType="";
		var resultStatus="";
		var orderResult='';
		var orderStatus='';
		var peakEndData={}
		var peakEndInfo;
		var orderData={}
		var showpingCartList=new Array();	//下单勾选的商品列表
		   $(function(){
			   if('${resultId}'!=""){//回访详情页面
				   
				   $("#page_service_list").remove()
				   $("#page_service_list2").remove()
				   $("#page_service_list3").remove()
			   }
			    $(".ibox-panel-title").css("margin-left","15px").css("margin-right","15px");
			    $(".container-fluid").css("height","100%").css("padding-left","0px").css("padding-right","0px");
	            pageLoader.init('#pageLoader');
    			$("#detailFrom").render({success:function(req){
    				if('${param.type}'==""){//接入单进入
    					
    				}else{
    					   if('${resultId}'!=""){//回访详情页面
    	    					$("#PHONE").val('${CUSTOMER_MOBILEPHONE1}');
    					   }else{
    							$("#serviceTime").val("${startTime}")
    	    					$("#serviceName").val("${userAcc}")
    	    					$("#PHONE").val('${CUSTOMER_MOBILEPHONE1}');
    					   }
    				
    					$("#compensateType").val('2');
    					orderData={orgCode:'${ORG_CODE}',prodCode:'${PROD_CODE}',brandCode:'${BRAND_CODE}',serviceTypeCode:'${ORDER_SERV_TYPE_CODE}',serviceItem2Code:'${ORDER_SER_ITEM2_CODE}',compensateType:'2'}
   						GetScript(orderData);
    				}
			         peakEndDetail.Validaddress();

    			}});
    			$("body").find("select").attr("data-cust-mars","");
	       });
		peakEndDetail.statusCg = function(val){
			$("#orderStatus").empty();//清空select
			switch(val){
			case '9'://已发优惠券
				  //$("#orderStatus").append(options[1]);
				  $("#orderStatus").append(options[0]);
				  break;
			  case '10'://待支付
				  //$("#orderStatus").append(options[2]);
				  $("#orderStatus").append(options[0]);
				  break;
			  case '11'://已支付
				  $("#orderStatus").append(options[3]);
				  break;
			  default:
				  $("#orderStatus").append(options[0]);
			      break;
			}
		}
	    //补偿处理-补偿资料查询-处理-暂存  
	    peakEndDetail.tempSave = function(){
	    	 var data = form.getJSONObject("detailFrom");
	    	 data.state=0;//暂存
			 ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=SaveOrder",data,function(result) { 
					if(result.state == 1){
						layer.msg("操作成功",{icon: 1});
						layer.closeAll();
					}else{
						layer.alert(result.msg,{icon: 5});
						 peakEndDetail.showop()
					}
				}
			 );
	 }
	    	
	    peakEndDetail.commit = function(){
	    	if(!form.validate("#detailFrom")){
				return;
			};
		
			if($("#result").val()=='10'||$("#result").val()=='11'||$("#result").val()=='9'){
				layer.alert('无法直接选补偿结果 已发送优惠劵、 待支付或已支付')
				return;
			}
			 	var data = form.getJSONObject("detailFrom");
	    	 data.orderInfo=form.getJSONObject("detailFrom");
			 data.peakEndData =peakEndData;
	    	 data.state=1;//提交
			 ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=SaveOrder",data,function(result) { 
					if(result.state == 1){
						parent.layer.closeAll();
						parent.layer.msg("操作成功",{icon: 1});
						parent.peakEndResource.searchData()

					}else{
						layer.alert(result.msg,{icon: 5});
						 peakEndDetail.showop()
					}
				}
			 );
	    }
	    peakEndDetail.appointmentOrder = function(){
			if($("#result").val()!=""){
				layer.alert('请勿选择补偿结果');
				return;
			}
			var appointment=$("#appointmentDate").val()+" "+$("#appointmentTime").val();	
			layer.closeAll();
		 	var data = form.getJSONObject("detailFrom");
	    	 data.state=0;//暂存
	    	 data.appointment=appointment;//预约时间
			 data.orderInfo=form.getJSONObject("detailFrom");
			 data.peakEndData =peakEndData;
		
			 ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=SaveOrder",data,function(result) { 
					if(result.state == 1){
						parent.layer.closeAll();
						parent.layer.msg("操作成功",{icon: 1});
						parent.peakEndResource.searchData()
	
					}else{
						layer.alert(result.msg,{icon: 5});
						 peakEndDetail.showop()
					}
				}
			
			 );
	    }
	   
	    peakEndDetail.appointment=function (){//预约
	    	if($("#result").val()!=""){
				layer.alert('请勿选择补偿结果')
				return;
			}
			popup.layerShow({type:1,title:'预约时间',area:['200px','250px'],offset:'20px'},
					"${ctxPath}/pages/peakEnd/peakEnd-appointment.jsp");
		 }
	  
	    //补偿处理-补偿资料查询-处理-提交
	   //补偿处理-补偿资料查询-处理- 发送优惠券
	   peakEndDetail.sendCoupon = function() {
		    var userPhone = $("#CUSTOMER_MOBILEPHONE").val();//Defalut firstPone
		    var marketId = $
		    popup.layerShow({
				type : 1,
				title : "优惠券发送",
				offset : '20px',
				area:['530px','460px']
			},"${ctxPath}/servlet/peakEnd?action=sendCopon",{userPhone:userPhone,id:'${param.id}'});
		}
	  
	   
	   
	   function phoneCode(type){
	   		var open = checkWindowOpened("电话区号");
			if(open == false){
				layer.msg("电话区号已经存在，且处于最小化状态！",{icon:5});
				return ;
			}
	   		var areaNum = '';
	   		var areaName = '';
	   		if(type == 'areaNum'){
	   			areaNum = $("#areaNum").val();
	   		}else if(type == 'areaName') {
	   			areaName = encodeURI(orderVue.$data.areaName);
	   		}
		   	popup.layerShow({type:2,title:'电话区号',area:['860px','660px'],offset:'20px',shade:0,maxmin:2,moveOut:true,shadeClose:false},"/neworder/pages/access/phone-code.jsp?areaCodeId=areaCode&areaNameId=areaName&areaNumId=areaNum&areaNum="+areaNum+"&areaName="+areaName);
	   	}
	   orderfunc.phoneNumCallback = function(data) {
		   $("#customerAddress").val(data.areaName)
	         peakEndDetail.Validaddress();

	 	}
	   
	 //检查是否打开某一弹窗
		 function checkWindowOpened(title){
		 
			var iframe = $(".layui-layer-iframe");
			if(iframe && iframe.length > 0){
				for(var i = 0; i< iframe.length ;i++){
					var t = $(iframe[i]).find(".layui-layer-title").html();
					if(title == t){
						return false;
						break;
					}
				}
			}
			return 1;
		}
		 var uids="";
		  function Call(id){
		    	var PHONENUM=$("#"+id).val();
		    	if(PHONENUM.substring(0,1) == "1"&&PHONENUM.length>11){
		    		layer.confirm('该手机号码可能出错，是否继续', {
						 btn : [ '确定', '取消' ]//按钮
				        }, function(index) {
				        	layer.closeAll();
					    	telephoneTemp_callPhone(PHONENUM);
				        });
		    	}else{
			    	telephoneTemp_callPhone(PHONENUM);
		    	}
			 }
		  function telephoneTemp_callPhone(phone){
			  var uid=guid();
			   layer.prompt({formType: 0, shade : false ,title: ['请确认号码','color:red'],value:phone,area: ['500px', '300px'] ,offset: '150px',btn:['拨打','取消']} //自定义文本域宽高
					, function(value,userData, index, elem){
						 uids=uids==""?uid:uid+";"+uids;
						var  userData={ICC_BUSI_TYPE:'03', ICC_BUSI_ID:uid,ICC_COEFFICIENT:"",ICC_DIVISION_ID:""};
						 var areaNum=$("#areaNum").val();
				    	if(areaNum!=null&&areaNum!=""&&value.length <= 8){
				    		value=areaNum+value;
				    	}
				    	 window.opener.call(value,userData, index, elem);
					     layer.closeAll();
				});
		   }
		  
		  function guid() {//生成uuid
		        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
		            var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
		            return v.toString(16);
		        });
		    }
		  
		  peakEndDetail.Validaddress=function(){
			  if($("#areaCode").val()!=""){
				  var params = {};
			 		params.areaCode = $("#areaCode").val();
			 			$.ajax({ url:"${ctxPath}/servlet/peakEnd?query=Validaddress",
			 	 			dataType : "json",
			 	 			data : params,
			 	 			type : "POST",
			 	 			success : function(result) {
			 	 				if(result.respCode =='000' ){//查询成功
			 	 					if(result.respDate!=null&&result.respDate.data){
			 	 						//开通了洗悦家
			 	 					}else{
			 	 	 					layer.alert("该区域未开通洗悦家服务")
			 	 					}
			 	 				}else{
			 	 					layer.alert("校验区域未开通洗悦家服务失败")
			 	 				}
			 	 			},
			 	 			error : function() {
			 	 				console.log("请求地区是否存在洗悦家，出现网络故障或者系统异常,请稍后再试!");
			 	 			}
			 	 		});
			  }
		  }
		  function GetScript(data){
				 ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=GetScript",data,function(result) { 
						if(result.state == 1){
							if(result.data.GUIDED_SPEECH!=null){
								$("#MARCKET_SCRIPT").val(result.data.GUIDED_SPEECH);
								$("#remarks").val(result.data.REMARKS);
								$("#responsible").render({success:function(req){
									if(req["dict.getDictList('PEAK_END_RESPONSIBLE')"]!=null){
										$("#responsible").val(result.data.RESPONSIBLE);
						    		}
								}});
							}else{
				 	    		layer.alert("当前诉求不允许进行服务补偿",{icon:5,time:2000});
							}
						}else{
						}
				})
			}
		  
		  //工单地址校验
			function orderAddressCheck(){
				parent.orderfunc.queryAreaInfoByAddress($("#customerAddress").val(),"1",{areaCode:$("#areaCode").val(),dataAttr:$("#areaName").val(),areaNum:$("#areaNum").val()});

			}
			function checkSurname(name){
				if( name.length > 1){
					$.ajax({ url:"/neworder/servlet/config?action=checkName&name="+name,
		 			    dataType : "json",
		 			    type : "POST",
		 				async : true,
		 				success : function(result) {
		 					if(result.state  == 1){
		 						//用户姓名数字校验
		 						var re = /^[0-9]+.?[0-9]*$/;
		 						var nameFlag = true;
		 						for(var i=0;i<name.length;i++){
		 							if(re.test(name.charAt(i))) {
		 								$("#CUSTOMER_NAME").addClass("check_error");
		 								nameFlag = false;
		 								break;
		 						    }
		 						}
		 						if(nameFlag){
		 							$("#CUSTOMER_NAME").removeClass("check_error");
		 							orderfunc.setSingleVueValue("order","serviceCustomerName",name);
		 						}
		 					}else{
		 						$("#CUSTOMER_NAME").addClass("check_error");
		 					}
		 				},
		 				error : function() {
							layer.alert("出现网络故障或者系统异常,请稍后再试!",{icon:7,time:15000});
		 				}
					});
					
				}else if( name.length == 1){
					layer.alert("姓名填写不正确，请重新填写！",{icon:2});
				}else{
					$("#customerName").removeClass("check_error");
				}
		 	}
			function checkPhone(obj){
				var phone_num=obj.value
				 if(!(of_checkMobilePhone(phone_num)||of_checkPhone(phone_num))){
					   $(obj).addClass("check_error");
					   return;
				   }else{
						$(obj).removeClass("check_error");
				   }
			}
			/**
			 * 固话校验//固话（6、7、8位）数字
			 * @param chkPhone
			 * @returns
			 */
			function of_checkPhone (chkPhone) {
				if(chkPhone!='' &&(chkPhone.length==6||chkPhone.length==7||chkPhone.length==8)){
					var isPhone= /^-?\d+$/;
					return isPhone.test(chkPhone);
				}
				return false;
			}
			/**
			 * 手机号码校验
			 */
			function of_checkMobilePhone (chkMobilePhone){
				//手机号码（1开头，11位）
				if(chkMobilePhone!=''&&chkMobilePhone.length==11){
					var isMob=/^1\d{10}$/;
					return isMob.test(chkMobilePhone);
				}else if(chkMobilePhone!=''&&chkMobilePhone.length==12){
					var isMob=/^01\d{10}$/;
					return isMob.test(chkMobilePhone);
				}
				return false;
			}
			//修改手机号
			function changePhone(ths){
				orderData.phone=ths.value;
			 	getLevel();//获取会员信息
			 	checkRepeat(orderData);
			 }
			/**
			 * 校验补偿记录
			 */
			function checkRepeat(data){
				ajax.remoteCall("/PeakEnd/servlet/peakEnd?action=checkRepeat",data,function(result) { 
					if(result.state == 1){
						if(result.data.num!=0){//有赠送记录
							$("#checkRepeatConetnt").html("一个月内已有赠送记录")
						}else{
							$("#checkRepeatConetnt").html("")
						}
					}else{
						$("#checkRepeatConetnt").html("")
						layer.alert("获取用户补偿记录失败",{icon: 5});
						
					}
				})
			}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>