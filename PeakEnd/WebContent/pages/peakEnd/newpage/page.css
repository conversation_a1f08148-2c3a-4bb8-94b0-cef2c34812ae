* {
	box-sizing: border-box;
	overflow: hidden;
	font-size: 15px;
}

html,
body,
.box {
	width: 100%;
	height: 100%;
	padding: 0;
	margin: 0;
}

.box {
	overflow: auto;
	padding: 0 12px;
}

.theitem {
	width: 100%;
	padding: 5px 0px;
	height: 40px;
	line-height: 40px;
	display: flex;
	-moz-user-select: none;
	-webkit-user-select: none;
	-ms-user-select: none;
	-khtml-user-select: none;
	user-select: none;
}

.theitemSpan {
	display: inline-block;
	width: 60px;
	margin-right: 10px;
	line-height: 35px;
	text-align: right;
}

.itemMsg {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.threeItem {
	padding: 0 10px;
	margin-bottom: 10px;
	margin-right: 2%;
	float: left;
	border-radius: 5px;
	border: 1px solid #ccc;
	text-align: center;
	height: 30px;
	line-height: 27px;
	cursor: pointer;
}

.imgItem {
	max-width: 32%;
	min-width: 32%;
	margin-right: 2%;
	margin-bottom: 10px;
	float: left;
	height: 160px;
	border-radius: 5px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
	-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);

	display: flex;
	flex-direction: column;
	border: 1px solid transparent;
	position: relative;
}

.imgItem:nth-child(3) {
	margin-right: 0;
}

.theimg {
	width: 100%;
	flex: 1;
}

.clearfix:after {
	content: '';
	height: 0;
	line-height: 0;
	display: block;
	visibility: hidden;
	clear: both;
}

.thetext {
	flex: 0.4;
	line-height: 20px;
	width: 100%;
	padding: 5px;
	white-space: normal;
	font-size: 13px;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	color: #808080;
}

.price {
	flex: 0.2;
	color: red;
	text-align: right;
	padding: 5px;
	line-height: 20px;
}


.choosedTop {
	border: 1px solid #007DDB;
}

.imgTop {
	width: 16px;
	height: 16px;
	margin-right: 5px;
	transform: translateY(-1px);
}

.choosedBottom {
	border: 1px solid #007DDB;
}

.mask {
	position: absolute;
	background-color: rgba(240, 240, 240, 0.5);
	left: 0;
	top: 0;
	z-index: 999;
	width: 100%;
	height: 100%;
	display: none;
}

.maskgou {
	position: absolute;
	background-color: #21D99B;
	width: 26px;
	height: 26px;
	text-align: center;
	line-height: 24px;
	border-radius: 50%;
	color: #fff;
	right: 8px;
	top: 8px;
	z-index: 9999;
	display: none;
}

.choosedBottom .mask {
	display: block;
}

.choosedBottom .maskgou {
	display: block;
}

.cantChoose {
	background-color: rgba(0, 0, 0, 0.1);
	pointer-events: none;
}

.itemMessage {
	position: absolute;
	left: 0px;
	bottom: 4px;
	display: inline-block;
	background-color: #E3F4FD;
	color: #17A6F1;
	padding: 3px 5px;
	/* width: 60px; */
	height: 18px;
	line-height: 13px;
	text-align: left;
	border-radius: 0px 3px 3px 0px;
}
