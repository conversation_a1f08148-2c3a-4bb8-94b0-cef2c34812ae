function chooseItem(params) {
	if (!$(params).is('.choosedTop')) {
		$(params).addClass("choosedTop");
	} else {
		$(params).removeClass("choosedTop");
	}
}


function chooseItemgoods(params) {
	if (!$(params).is('.choosedBottom')) {
		$(params).addClass("choosedBottom");
	} else {
		$(params).removeClass("choosedBottom");
	}
}

function Submit(params) {
	let arr = [], arr2=[];
	$('.choosedTop').each(function(a,b) {
		arr.push($(b).text())
	});
	$('.choosedBottom').each(function(a,b) {
		arr2.push($(b).data('id'))  //自定义ID
	});
	let obj = {
		'补偿项目': arr,
		'用户姓名': $('#name').val(),
		'用户电话': $('#phone').val(),
		'区域': $('#area').val(),
		'用户住址': $('#msgarea').val(),
		'责任方': $('#zerenfang').val(),
		'备注': $('#bakup').val(),
		'营销话术': $('#huashu').val(),
		'商品选择': arr2,
	}
	console.log(obj)
}
