* {
  box-sizing: border-box;
  overflow: hidden;
  font-size: 13px;
}

html,
body,
.box {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
}

.box {
  overflow: auto;
  padding: 0 10px 0 0;
}

.theitem {
  width: 100%;
  padding: 5px 0px;
  height: 32px;
  line-height: 32px;
  display: flex;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
}

.theitemSpan {
  display: inline-block;
  width: 65px;
  margin-right: 5px;
  line-height: 35px;
  text-align: right;
  color: #262626;
}

.itemMsg {
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.threeItem {
  padding: 0 10px;
  margin: 0;
  float: left;
  border-radius: 5px;
  border: 1px solid #ccc;
  text-align: center;
  height: 30px;
  line-height: 27px;
  cursor: pointer;
}

#productlistShow {
  position: relative;
  display: flex;
  /* grid-template-columns: repeat(3, 1fr); */
  grid-gap: 12px 10px;
}

.imgItem {
  width: calc(33% - 8px);
  height: 150px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 8px;
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;
}

.theimg {
  width: 100%;
  height: 98px;
  margin-bottom: 8px;
}

.clearfix:after {
  content: "";
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}

.thetext {
  height: 38px;
  line-height: 15px;
  vertical-align: text-bottom;
  width: 100%;
  white-space: normal;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: #262626;
  margin-bottom: 6px;
}

.price {
  position: relative;
  color: #fa9904;
  font-weight: 16px;
  font-weight: bold;
  text-align: right;
  height: 20px;
  line-height: 15px;
  font-size: 10px;
}

.choosedTop {
  border: 2px solid #0092d8;
}

.imgTop {
  width: 16px;
  height: 16px;
  margin-right: 5px;
  transform: translateY(-1px);
}

.choosedBottom {
  border: 1px solid #0092d8;
}

.mask {
  position: absolute;
  background-color: rgba(240, 240, 240, 0.5);
  left: 0;
  top: 0;
  z-index: 99;
  width: 100%;
  height: 100%;
  display: none;
}

.maskgou {
  position: absolute;
  width: 26px;
  height: 26px;
  text-align: center;
  line-height: 24px;
  color: #fff;
  right: 0px;
  top: 0px;
  z-index: 99;
  display: none;
  border-top-right-radius: 8px;
}

.choosedBottom .mask {
  display: block;
}

.choosedBottom .maskgou {
  display: block;
}
.choosedTop .selectGift,
.choosedTop .selectyb,
.choosedTop .selectweixing,
.choosedTop .selectUserLevel,
.choosedTop .selectcashCompensation {
  display: block;
}

.cantChoose {
  background-color: rgba(0, 0, 0, 0.1);
  pointer-events: none;
}

.itemMessage {
  position: absolute;
  left: 0px;
  bottom: 0px;
  display: inline-block;
  background: rgba(250, 153, 4, 0.1);
  /* color: #fa9904; */
  box-sizing: border-box;
  padding: 0px 4px;
  /* width: 60px; */
  height: 15px;
  line-height: 15px;
  text-align: left;
  border-radius: 4px;
  font-size: 10px;
  /* font-weight: normal; */
}
.noProductlist {
  text-align: center;
  font-size: 20px;
  color: #5bc0de;
  margin: 0 auto;
}

/* 样式重构 */
.gray-bg {
  background-color: #fff;
}

.bg {
  position: relative;
  height: 64px;
  background: url("/PeakEnd/pages/peakEnd/gift/images/bg.png") center / 100%
    100% no-repeat;
  margin: 10px 0 10px;
  padding: 6px 24px;
  color: #fff;
  font-size: 14px;
  box-sizing: border-box;
}
#levelName {
  font-size: 13px;
  line-height: 30px;
}
.record {
  display: flex;
  align-items: center;
}
.record .line {
  width: 4px;
  height: 12px;
  background: #fff;
  margin-right: 8px;
}
.theitem {
  padding: 0;
  margin-bottom: 16px;
}
.itemMsg {
  grid-gap: 6px;
}
.threeItem {
  height: 40px;
  line-height: 40px;
  background: #f2f4f7;
  border-radius: 4px;
  text-align: center;
  padding: 0 16px;
  color: #262626;
  font-size: 14px;
  border: 1px solid transparent;
}
.threeItem.choosedTop {
  border: 1px solid #0092d8;
  background: rgba(0, 146, 216, 0.1);
  color: #0092d8;
}
.selectGift,
.selectyb,
.selectweixing,
.selectUserLevel,
.selectcashCompensation {
  position: absolute;
  right: 0px;
  top: 0px;
  z-index: 9999;
  display: none;
}
.selectGift img,
.selectyb img,
.selectweixing img,
.selectUserLevel img,
.selectcashCompensation img {
  display: block;
  width: 16px;
  height: 16px;
}
.threeItem i {
  margin-right: 4px;
  vertical-align: bottom;
}
.threeItem.cantChoose {
  background: #e6e9ee;
  color: #868686;
  border: 1px solid transparent;
}
.btnGroup {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  z-index: 999;
}
.btnGroup .btn {
  margin-right: 8px;
  border: none !important;
}
.btnGroup .btn:last-child {
  margin-right: 0;
}
#submit {
  height: 40px;
  line-height: 40px;
  background: #20c997;
  border-radius: 4px;
  padding: 0 32px;
  box-sizing: border-box;
  color: #fff;
  font-size: 16px;
}
#resetBtn {
  height: 40px;
  line-height: 40px;
  background: #fa9904;
  border-radius: 4px;
  padding: 0 32px;
  box-sizing: border-box;
  color: #fff;
  font-size: 16px;
}
#unMatchBtn {
  height: 40px;
  line-height: 40px;
  background: #0092d8;
  border-radius: 4px;
  padding: 0 16px;
  box-sizing: border-box;
  color: #fff;
  font-size: 16px;
}
.form-control {
  color: #262626;
  background: #f2f4f7;
  border: none;
  box-shadow: none !important;
}
.form-control:focus {
  border: 1px solid #0092d8;
}
a {
  color: #0092d8;
}
.collapse-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #868686;
  cursor: pointer;
  line-height: 24px;
  margin-bottom: 16px;
}
.collapse-title-noclick {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #868686;
  /* cursor: pointer; */
  line-height: 24px;
  margin-bottom: 16px;
}
.collapse-title .text {
  color: #262626;
  font-size: 16px;
  font-weight: bold;
}
.collapse-title-noclick .text {
  color: #262626;
  font-size: 16px;
  font-weight: bold;
}
select.input-sm {
  height: 32px;
  line-height: 32px;
}
.form-control[disabled] {
  background-color: #eee;
  border-color: #eee;
}
#compensateCashDiv {
  margin-left: 70px;
  width: auto;
  grid-gap: 8px 16px;
}
#ybkTitle {
  color: #868686;
  line-height: 24px;
}
.drawer {
  height: 100%;
}
.drawer .drawer-content {
  height: calc(100% - 72px);
  padding: 24px;
}
.drawer .drawer-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 72px;
  padding: 0 24px;
  border-top: 1px solid #e8e8e8;
}
#cancelBtn {
  height: 40px;
  line-height: 40px;
  background: rgba(0, 146, 216, 0.1);
  border-radius: 4px;
  padding: 0 40px;
  box-sizing: border-box;
  color: #0092d8;
  font-size: 16px;
  margin-right: 16px;
  border: none;
}
#okBtn {
  height: 40px;
  line-height: 40px;
  background: #0092d8;
  border-radius: 4px;
  padding: 0 40px;
  box-sizing: border-box;
  color: #fff;
  font-size: 16px;
  border: none;
}
.unitem {
  position: relative;
  height: 40px;
  line-height: 40px;
  background: #f2f4f7;
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
  color: #262626;
  margin-bottom: 16px;
  border: 1px solid transparent;
  cursor: pointer;
}
.unitem.active {
  background: rgba(0, 146, 216, 0.1);
  border: 1px solid #0092d8;
  color: #0092d8;
}
.unitem.active::after {
  position: absolute;
  right: 0;
  top: 0;
  width: 16px;
  height: 16px;
  background: url("./images/choose.png") center / 100% 100% no-repeat;
  content: "";
}
#checkRepeatConetnt {
  position: absolute;
  right: 0;
  top: 0;
  height: 22px;
  line-height: 22px;
  border-radius: 0px 8px 0px 8px;
  opacity: 1;
  background: rgba(240, 56, 56, 0.75);
  padding: 0 8px;
  color: #ffffff;
  font-size: 12px;
}
.requireId {
  display: flex;
}
