function getCurrentSelectedItems() {
  let selectedItems = [];
  $('.choosedTop').each(function() {
    let type = $(this).data('type');
    let name = '';
    switch(type) {
      case 1:
        name = '实物礼品';
        break;
      case 2:
        name = '黄金会员';
        break;
      case 3:
        name = '企微';
        break;
      case 6:
        name = '延保卡';
        break;
      case 7:
        name = '现金补偿';
        break;
    }
    selectedItems.push(name);
  });
  parent.orderpopup.serviceCompObj.Compensation_scheme=selectedItems&&selectedItems.length>0?selectedItems.toString():""
}
function chooseItem(params) {
  if (!$(params).is(".choosedTop")) {
    $(params).addClass("choosedTop");
    $(".giveItem").hide();

    // 设置hasChange标记
    if (parent.orderpopup && parent.orderpopup.serviceCompObj) {
      parent.orderpopup.serviceCompObj.hasChange = 1;
      console.log("补偿类型已选择");
    }

    if ($(params).data("type") == "6") {
      if ($("#cashCompensation").hasClass("choosedTop")) {
        //判断已经选择现金补偿,不能进行延保卡操作
        $(params).removeClass("choosedTop");
        layer.alert("已选择现金补偿，不能进行延保卡操作", { icon: 5 });
      } else {
        $("#ybshow").css("display", "block");
      }
    }
    if ($(params).data("type") == "1") {
      if ($("#cashCompensation").hasClass("choosedTop")) {
        //判断已经选择现金补偿,不能进行赠送实物
        $(params).removeClass("choosedTop");
        layer.alert("已选择现金补偿，不能进行赠送实物礼品", { icon: 5 });
      } else {
        $("#product").css("display", "block");
        $(".address,.giveItem").show();
      }
    }
    if ($(params).data("type") == "7") {
      //选择现金补偿
      if (
        $("#physicalGifts").hasClass("choosedTop") ||
        $("#ybk").hasClass("choosedTop")
      ) {
        //判断是否选择实物补偿/延保卡不能进行现金补偿
        $(params).removeClass("choosedTop");
      } else {
        // if(!isInitContact){
        // 	getServieOrderDetail();
        // }
        $("#remarksDiv").html(
          '<span style="color: red;padding-right: 5px">*</span>补偿说明'
        );
        $("#scriptDiv").html("补偿话术");
        $("#clientDiv").html(
          '<span style="color: red;padding-right: 5px">*</span>委托方'
        );
        $("#ckientCode").prop("disabled", true);
        getUserStatus($("#phone").val());
        $(".cashCompensation,.giveItem").show();
      }
    }
    moveDom($(params).data("type"), true);
  } else {
    $(params).removeClass("choosedTop");
    if ($(params).data("type") == "6") {
      $("#ybshow").css("display", "none");
    }
    if ($(params).data("type") == "1") {
      $("#product").css("display", "none");
      $(".address,.giveItem").hide();
    }
    if ($(params).data("type") == "7") {
      $("#remarksDiv").html("备注");
      $("#scriptDiv").html("营销话术");
      $("#clientDiv").html("委托方");
      $("#ckientCode").prop("disabled", false);
      $(".cashCompensation,.giveItem").hide();
      $("#userloginstatus").html("");
    }
    moveDom($(params).data("type"), false);
  }
  getCurrentSelectedItems()
}

function moveDom(type, flag) {
  var o = $('[data-id="baseInfo"]');
  var e = $('[data-id="cashInfo"]');
  if (flag) {
    if (type == "7") {
      o.find($("[data-clone]")).each((index, dom) => {
        var cloneId = $(dom).data("clone");
        $(dom)
          .children()
          .first()
          .appendTo(e.find($('[data-clone="' + cloneId + '"]')));
      });
    } else {
      e.find($("[data-clone]")).each((index, dom) => {
        var cloneId = $(dom).data("clone");
        $(dom)
          .children()
          .first()
          .appendTo(o.find($('[data-clone="' + cloneId + '"]')));
      });
    }
  } else {
    if (type == "7") {
      e.find($("[data-clone]")).each((index, dom) => {
        var cloneId = $(dom).data("clone");
        $(dom)
          .children()
          .first()
          .appendTo(o.find($('[data-clone="' + cloneId + '"]')));
      });
    } else {
      if ($("#cashCompensation").hasClass("choosedTop")) {
        e.find($("[data-clone]")).each((index, dom) => {
          var cloneId = $(dom).data("clone");
          $(dom)
            .children()
            .first()
            .appendTo(o.find($('[data-clone="' + cloneId + '"]')));
        });
      } else {
      }
    }
  }
}

/**
 * 修改手机号
 * @param ths
 */
function changeCustomerPhone(ths) {
  $("#phone").val($(ths).val());
  getLevel(); //获取会员信息
}
/**
 * 获取会员等级
 */
function getLevel(
  getScriptRes = "",
  gradeObj = {},
  GetScript = null,
  orderData = {}
) {
  var data = { phone: $("#phone").val() };
  console.log(getScriptRes, "getScriptRes");
  ajax.remoteCall(
    "/PeakEnd/servlet/peakEnd?action=getLevel",
    data,
    function (result) {
      if (result.state == 1) {
        console.log("1111");
        if (result.data != null) {
          if (result.data.data == null) {
            console.log("222");
            $("#levelName").html("未注册");
            $("#growValue").val("");
            $("#userLevelGift").addClass("cantChoose");
          } else {
            console.log("333");
            $("#levelName").html(result.data.data.levelName);
            $("#growValue").val(result.data.data.vipGrow);
            gradeObj.grade = result.data.data.grade;
            console.log(getScriptRes, "getScriptRes2");
            if (getScriptRes === "Y") {
              console.log("311");
              if (
                result.data.data.growValue < 1001 &&
                result.data.data.grade < "3"
              ) {
                console.log("getLevel removeCant");
                $("#userLevelGift").removeClass("cantChoose");
                $("#userLevelGift").addClass("choosedTop");
              } else {
                console.log("getLevel addCant");
                $("#userLevelGift").addClass("cantChoose");
              }
            } else {
              console.log("322", getScriptRes);
              $("#userLevelGift").addClass("cantChoose");
            }
          }
        } else {
          console.log("444");
          layer.alert("会员信息获取失败", { icon: 5 });
        }
      } else {
        layer.alert(result.msg, { icon: 5 });
      }
      if (GetScript !== null) {
        GetScript(orderData);
      }
    }
  );
}
function getProductlist(data) {
  ajax.remoteCall(
    "/PeakEnd/servlet/peakEnd?action=Productlist",
    data,
    function (result) {
      if (result.state == 1) {
        if (result.data != null && result.data.data != null) {
          var list = new Array();
          var vecProductList = result.data.data;
          var scriptData = result.data.scriptData;
          for (var i in vecProductList) {
            var val = vecProductList[i];
            if (
              scriptData.MIN_BY_COMPENSATE <= val.retailPrice &&
              scriptData.MAX_BY_COMPENSATE > val.retailPrice
            ) {
              let info = {};
              $.extend(info, val);
              info["ProductType"] = "抱怨";
              list.push(info);
              break;
            }
          }
          for (var i in vecProductList) {
            var val = vecProductList[i];
            if (
              scriptData.MIN_TS_COMPENSATE <= val.retailPrice &&
              scriptData.MAX_TS_COMPENSATE > val.retailPrice
            ) {
              let info = {};
              $.extend(info, val);
              info["ProductType"] = "投诉";
              list.push(info);
              break;
            }
          }
          for (var i in vecProductList) {
            var val = vecProductList[i];
            if (
              scriptData.MIN_YYBG_COMPENSATE <= val.retailPrice &&
              scriptData.MAX_YYBG_COMPENSATE > val.retailPrice
            ) {
              let info = {};
              $.extend(info, val);
              info["ProductType"] = "扬言";
              list.push(info);
              break;
            }
          }
          if (list.length == 0) {
            $("#productlistShow").html(
              "<div class='noProductlist'>无商品信息</div>"
            );
          } else {
            var html = productlist.render(list);
            $("#productlistShow").html(html);
          }
        } else {
          $("#productlistShow").html(
            "<div class='noProductlist'>无商品信息</div>"
          );
        }
        console.log(result.data);
      } else {
        layer.alert(result.msg, { icon: 5 });
      }
    }
  );
}

function chooseItemgoods(params) {
  console.log("newGift", params);
  if (!$(params).is(".choosedBottom")) {
    $(".choosedBottom").each(function () {
      //单选
      $(this).removeClass("choosedBottom");
    });
    $(params).addClass("choosedBottom");

    // 设置hasChange标记
    if (parent.orderpopup && parent.orderpopup.serviceCompObj) {
      parent.orderpopup.serviceCompObj.hasChange = 1;
      console.log("商品已选择");
    }
  } else {
    $(params).removeClass("choosedBottom");
  }
  //   if ($(".choosedBottom").length == 0) {
  //     $("#physicalGifts").removeClass("choosedTop");
  //   } else {
  //     $("#physicalGifts").addClass("choosedTop");
  //   }
}
/**
 * 实物补偿
 * @param data
 */
function productCompensate(data) {
  //校验事业部金额剩余情况
  //当前用户相同的品牌 、品类、服务请求做了服务补偿，提醒用户
  ajax.remoteCall(
    "/PeakEnd/servlet/peakEnd?action=PeakEndIimit",
    data,
    function (result) {
      if (result.state == 1) {
        if (
          (result.data.orgIntegralId != null &&
            result.data.orgIntegralId != "") ||
          data.type.indexOf(1) == -1
        ) {
          data["orgIntegralId"] = result.data.orgIntegralId;
          ajax.remoteCall(
            "/PeakEnd/servlet/peakEnd?action=productCompensate",
            data,
            function (result) {
              if (result.state == 1) {
                layer.alert("操作成功", { icon: 1 });
                $("#submit").addClass("hidden");
                $("#unMatchBtn").addClass("hidden");
                if (data.compensateType == "1") {
                  //工单的事中补偿
                  setPeakEndOpIds(result.data);
                }
              } else {
                layer.alert(result.msg, { icon: 5 });
              }
              parent.orderpopup.serviceCompObj.action='提交补偿'
              parent.orderpopup.bury_service_compensation&&parent.orderpopup.bury_service_compensation()
            }
          );
        }
      } else {
        layer.alert(result.msg, { icon: 5 });
      }
    }
  );
}
/**
 * 单独事业部金额剩余情况
	当前用户相同的品牌 、品类、服务请求做了服务补偿，提醒用户
 * @param data
 */
var peakEndCheckboolean = true;
function peakEndCheck(data) {
  ajax.remoteCall(
    "/PeakEnd/servlet/peakEnd?action=PeakEndIimit",
    data,
    function (result) {
      if (result.state == 1) {
        peakEndCheckboolean = true;
      } else {
        layer.alert(result.msg, { icon: 5 });
        peakEndCheckboolean = false;
        $("#submit").addClass("hidden");
        $("#unMatchBtn").addClass("hidden");
      }
    },
    { async: false }
  );
}
/**
 * 户地址是否规范(区域编码后三位都是0)
 */
function addressCheck() {
  //	let areaCode=$("#areaCode").val();
  let areaName = $("#areaName").val();
  //	if(areaCode.substring(areaCode.length-3)=="000"){
  if (areaName.indexOf("不明分区") > -1) {
    layer.alert("地址区域信息不明确，请人工核对和修改用户的用户地址", {
      icon: 5,
      time: 2000,
    });
    return false;
  }
  return true;
}

/**
 * 商品是否还有存量
 * 重新调用接口查询商品是否有存量
 */
var productCheckBoolean = true;
function productCheck(data, id) {
  ajax.remoteCall(
    "/PeakEnd/servlet/peakEnd?action=Productlist",
    data,
    function (result) {
      if (result.state == 1) {
        console.log(result.data);
        var vecProductList = result.data.data;
        for (var product in vecProductList) {
          if (product.exchangeCode == id) {
            if (product.nStock <= 0) {
              //没有剩余库存
              layer.alert("当前商品没有剩余库存", { icon: 5, time: 2000 });
              productCheckBoolean = false;
            } else {
              productCheckBoolean = true;
            }
          }
        }
      } else {
        layer.alert(result.msg, { icon: 5 });
      }
    },
    { async: false }
  );
}
/**
 * 校验补偿记录
 */
function checkRepeat(data) {
  ajax.remoteCall(
    "/PeakEnd/servlet/peakEnd?action=checkRepeat",
    data,
    function (result) {
      $("#num").text(result.data.num || 0);
      if (result.state == 1) {
        // if (result.data.nonConformance == '1') {
        //     $("#submit").addClass("hidden");
        // } else {
        //     $("#submit").removeClass("hidden");
        // }
        if (result.data.num != 0) {
          //有赠送记录
          $("#checkRepeatConetnt").show();
          $("#checkRepeatConetnt").html(result.data.message);
        } else {
          $("#checkRepeatConetnt").hide();
          $("#checkRepeatConetnt").html("");
        }
      } else {
        $("#checkRepeatConetnt").hide();
        $("#checkRepeatConetnt").html("");
        layer.alert("获取用户补偿记录失败", { icon: 5 });
      }
    }
  );
}
var selectyb;
function handleProItemClick(param) {
  var isIncludeNoDrop = $(param).attr("class").indexOf("proItem-N");
  if (isIncludeNoDrop == "-1") {
    $(param)
      .addClass("proItem-active")
      .siblings()
      .removeClass("proItem-active");
    $(param)
      .find(".item-checked")
      .css("display", "block")
      .parent()
      .siblings()
      .find(".item-checked")
      .css("display", "none");
    $(param)
      .find(".item-checked-icon")
      .css("display", "block")
      .parent()
      .siblings()
      .find(".item-checked-icon")
      .css("display", "none");
    selectyb = $(param).attr("data-id");
  } else {
  }
}
var womCcUserMachineHistoryItemList = {};
function getUserHistorylist(orderData) {
  var data = {
    telephone1: orderData.phone,
    brandCode: orderData.brandCode,
    prodCode: orderData.prodCode,
  };
  ajax.remoteCall(
    "/PeakEnd/servlet/peakEnd?action=userHistorylist",
    data,
    function (result) {
      if (result.state == 1) {
        womCcUserMachineHistoryItemList = {};
        if (
          result.data != null &&
          result.data.womCcUserMachineHistoryItemList != null
        ) {
          var list = result.data.womCcUserMachineHistoryItemList;
          if (list.length == 0) {
            $("#userHistoryShow").html(
              "<div class='noProductlist'>无机器信息</div>"
            );
            $("#ybk").addClass("cantChoose");
          } else {
            for (var i in list) {
              var val = list[i];
              if (val.insideBarcode != "" || val.outsideBarcode != "") {
                womCcUserMachineHistoryItemList[
                  (val.insideBarcode ? val.insideBarcode : "") +
                    "-" +
                    (val.outsideBarcode ? val.outsideBarcode : "")
                ] = val;
              }
            }
            var html = userHistorylist.render(list);
            $("#userHistoryShow").html(html);
            getGoldcardRemainder();
          }
        } else {
          $("#productlistShow").html(
            "<div class='noProductlist'>无机器信息</div>"
          );
          $("#ybk").addClass("cantChoose");
        }
        console.log(result.data);
      } else {
        layer.alert(result.msg, { icon: 5 });
      }
    }
  );
}
function getGoldcardRemainder() {
  var data = {};
  ajax.remoteCall(
    "/PeakEnd/servlet/peakEnd?action=GoldcardRemainder",
    data,
    function (result) {
      if (result.state == 1) {
        $("#ybkTitle").html();
        for (var key in result.data.womCcGoldCardRemainderItemList) {
          $("#ybkTitle").append(
            "<span style='font-weight: 600; padding-right: 10px;'>" +
              result.data.womCcGoldCardRemainderItemList[key].clientName +
              ":" +
              result.data.womCcGoldCardRemainderItemList[key].remainderNum +
              "</span>"
          );
        }
      } else {
        layer.alert(result.msg, { icon: 5 });
      }
    }
  );
}
// 服务补偿点击埋点
function helper_compensation_bury(ele) {
  console.log(ele, "ele");
  if (top.collectEvent) {
    top.collectEvent("click_voice_helper_compensation", {
      element: ele,
    });
  }
}
