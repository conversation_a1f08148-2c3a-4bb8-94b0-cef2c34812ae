<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>实物补偿</title>
</EasyTag:override>
<EasyTag:override name="content">
<html>
	<head>
		<meta charset="utf-8">
		<title></title>
		<meta name="viewport" content="width=device-width,intial-scale=1,user-scalable=no">
		<link rel="stylesheet" type="text/css" href="../pages/peakEnd/gift/layui/layui.css" />
		<link rel="stylesheet" type="text/css" href="../pages/peakEnd/gift/gift.css" />
		<style type="text/css">
			.theitemSpan{
				width: 100px;
			}
			.imgItem {
				max-width: 200px;
				min-width: 200px;
				margin-right: 10px;
				margin-bottom: 10px;
				float: left;
				height: 230px;
				border-radius: 5px;
				box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
				-moz-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
				-webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
				
				display: flex;
				flex-direction: column;
				border: 1px solid transparent;
				position: relative;
			}
			
			
			.thetext{
				flex: 0.4;
				line-height: 20px;
				width: 100%;
				padding: 5px;
				white-space: normal;
				font-size: 13px;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 3;
				-webkit-box-orient: vertical;
				color: #808080;
			}
			
			.imgItem:nth-child(3) {
				margin-right: 10px;
			}
			
			.price{
				line-height: 30px;
			}
			
			.itemMessage{
				position: absolute;left: 0px;bottom: 6px;display: inline-block;background-color: #f3eafd;color: #9046eb;padding: 5px 5px;height: 21px;line-height: 12px;text-align: left;border-radius: 0px 3px 3px 0px;
			}
			
		</style>
	</head>
	<body>
		<div class="box" style="">
			<div class="theitem" style="overflow: visible;height: auto;">
				<input type="text" class="hidden" id="phone" value="${param.phone}">
				<span class="theitemSpan">会员等级</span>
				<div class="itemMsg" id="levelName" style="overflow: visible;height: auto;background-color: #24364B;color: #F1CA96;    max-width: 100px;box-sizing: border-box;padding: 0px 10px;border-radius: 5px;text-align: center;">
					未注册
				</div>
				<input type="text" id="growValue" autocomplete="off" class="hidden">
			</div>
			
			<div class="theitem" style="overflow: visible;height: auto;">
				<span class="theitemSpan">补偿项目</span>
				<div class="itemMsg" style="overflow: visible;height: auto;">
					<div class="threeItem" style="margin-right:10px;" id="physicalGifts" onclick="chooseItem(this)" data-type="1"><img src="../pages/peakEnd/gift/images/gift.png" class="imgTop">实物礼品</div>
					<div class="threeItem" style="margin-right:10px;" id="userLevelGift" onclick="chooseItem(this)"data-type="2"><img src="../pages/peakEnd/gift/images/member.png" class="imgTop">黄金会员</div>
					<div class="threeItem " onclick="chooseItem(this)" id="weixingGift" data-type="3"><img src="../pages/peakEnd/gift/images/businesses.png" class="imgTop">企微</div>
				</div>
			</div>

			<div class="theitem"  style="overflow: visible;height: auto;">
				<div class="theitemSpan">商品选择</div>
				<div class="itemMsg clearfix" id="productlistShow" style="overflow: visible;display: flex;flex-wrap: wrap;justify-content: flex-start;">
					
				</div>
				<script type="text/x-jsrender" id="productlist">
				{{for}}
					<div class="imgItem" onclick="chooseItemgoods(this)" data-id="{{:exchangeCode}}" data-name="{{:name}}" data-amount="{{:retailPrice}}">
						<img src="{{:goodsPicture}}" class="theimg">
						<div class="thetext">{{:name}}</div>
						<div class="price"><span class="itemMessage">{{:ProductType}}</span>{{:retailPrice}}</div>
						<div class="mask"></div>
						<div class="maskgou">
							<img src="../pages/peakEnd/gift/images/ico_choose_big.png" style="width: 26px;height: 26px;">
						</div>
					</div>
				{{/for}}
				</script>
			</div>
			<div style="text-align: center;margin-top: 30px;" id="submit"><button class="layui-btn  layui-btn-normal" onclick="Submit()">提交</button></div>
		</div>
		
		
	</body>
</html>
</EasyTag:override>

<EasyTag:override name="script">
		<script src="../pages/peakEnd/gift/layui/jquery-1.9.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="../pages/peakEnd/gift/layui/layui.js" type="text/javascript" charset="utf-8"></script>
		<script src="../pages/peakEnd/gift/gift.js" type="text/javascript" charset="utf-8"></script>
		<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
	<script type="text/javascript">
		var productlist = $.templates("#productlist");
 	    $(function(){
 	    	$("#phone").val($("#PHONE").val());
 	    	getLevel();//获取会员信息
 	    	$("#weixingGift").addClass("choosedTop");//企微默认赠送
 	    	getProductlist(orderData);//商品列表
 	    	addressCheck();
		})
		
		function Submit(params) {
 	    	if($("#CUSTOMER_NAME").val()==""||$("#PHONE").val()==""||$("#customerAddress").val()==""){
 	    		layer.alert("请填写用户信息",{icon:5,time:2000});
 	    		return;
 	    	}
 	    	if($("#result").val()==""){
 	    		layer.alert("请选择回访结果",{icon:5,time:2000});
 	    		return;
 	    	}
 	    	if($("#responsible").val()==""){
 	    		layer.alert("请选择责任方",{icon:5,time:2000});
 	    		return;
 	    	}
 	    	if($("#result").val()=="1"){//有效回访
 	    		let arr = [];	
 				let productId="";
 				let productName="";
 				let productAmount="";
 				$('.choosedTop').each(function(a,b) {
 					arr.push($(b).data('type'))
 				});
 				$('.choosedBottom').each(function(a,b) {
 					productId=$(b).data('id');  //自定义ID
 					productName=$(b).data('name');  
 					productAmount=$(b).data('amount');  
 				});
 				if(productId==""){
 					layer.alert("未选择商品",{icon:5,time:2000});
 					return;
 				}
 				let obj = {
 					'type': arr,
 					'customerName': $('#CUSTOMER_NAME').val(),
 					'phone': $("#PHONE").val(),
 					'areaName': $('#areaName').val(),
 					'areaCode': $('#areaCode').val(),
 					'customerAddress': $('#customerAddress').val(),
 					'productId': productId,
 					'productName': productName,
 					'productAmount': productAmount,
 					'startTime':$("#serviceTime").val(),
 					'userAcc':$("#serviceName").val(),
 					'growValue':$("#growValue").val(),
					
 					'orgCode':$("#orgCode").val(),
 					'brandName':$("#brandName").val(),
 					'brandCode':$("#brandCode").val(),
 					'prodName':$("#prodName").val(),
 					'prodCode':$("#prodCode").val(),
 					
 					'orderServTypeCode':$("#ORDER_SERV_TYPE_CODE").val(),
 					'orderServTypeName':$("#ORDER_SERV_TYPE_NAME").val(),
 					'orderSerItem1Name':$("#ORDER_SER_ITEM1_NAME").val(),
 					'orderSerItem1Code':$("#ORDER_SER_ITEM1_CODE").val(),
 					'orderSerItem2Name':$("#ORDER_SER_ITEM2_NAME").val(),
 					'orderSerItem2Code':$("#ORDER_SER_ITEM2_CODE").val(),
 					
 					'remarks': $('#remarks').val(),//备注
 					'result': $('#result').val(),//结果
 					'responsible': $('#responsible').val(),//责任方
 					'compensateType': $('#compensateType').val(),//补偿类型
 					'id': $('#id').val(),
 					'uids': uids,//录音id（多个）
 					'coefficientId': $('#coefficientId').val(),//折算类型
 					'coefficient': $('#coefficient').val(),//折算值
 					'serviceOrderNo': $('#serviceOrderNo').val(),//
 					'areaNum': $('#areaNum').val(),//
 				}
 				peakEndCheck(obj)
 				if(!peakEndCheckboolean)return
 				productCheck(obj,productId);
 				if(!productCheckBoolean)return;
 				if(!addressCheck())return;
 				productCompensate(obj)
 	    	}else{
 	    		let data={
 	    				'remarks': $('#remarks').val(),//备注
 	 					'result': $('#result').val(),//结果
 	 					'responsible': $('#responsible').val(),//责任方
 	 					'compensateType': $('#compensateType').val(),//补偿类型
 	 					'id': $('#id').val(),
	 	    			'uids': uids,//录音id（多个）
						'coefficientId': $('#coefficientId').val(),//折算类型
						'coefficient': $('#coefficient').val(),//折算值
					}
 	    			ajax.remoteCall("/PeakEnd/servlet/peakEnd?action=saveResult",data,function(result) {
 	    				if(result.state == 1){
 	    					layer.alert(result.msg,{icon: 1});
 							$("#submit").addClass("hidden");
 	    				}else{
 	    					layer.alert(result.msg,{icon: 5});
 	    				}
 	    			})
 	    	}
			
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
