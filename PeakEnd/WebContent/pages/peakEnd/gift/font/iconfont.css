@font-face {
  font-family: "iconfont"; /* Project id 4672652 */
  src: url('iconfont.woff2?t=1725357556525') format('woff2'),
       url('iconfont.woff?t=1725357556525') format('woff'),
       url('iconfont.ttf?t=1725357556525') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-WECHAT2:before {
  content: "\e671";
}

.icon-VIP2:before {
  content: "\e672";
}

.icon-GIFT2:before {
  content: "\e673";
}

.icon-CARD2:before {
  content: "\e674";
}

.icon-CASH2:before {
  content: "\e675";
}

