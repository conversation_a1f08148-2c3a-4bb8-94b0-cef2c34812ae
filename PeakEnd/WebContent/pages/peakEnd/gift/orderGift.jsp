<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>实物补偿侧边栏</title>
</EasyTag:override>
<EasyTag:override name="content">
<html>
	<head>
		<meta charset="utf-8">
		<title></title>
		<meta name="viewport" content="width=device-width,intial-scale=1,user-scalable=no">
		<link rel="stylesheet" type="text/css" href="/PeakEnd/pages/peakEnd/gift/layui/layui.css" />
		<link rel="stylesheet" type="text/css" href="/PeakEnd/pages/peakEnd/gift/new_gift.css?v=13" />
        <link rel="stylesheet" href="/PeakEnd/pages/peakEnd/gift/font/iconfont.css">
		<style type="text/css">
		.new-input-group .input-group-addon {
		    cursor: pointer;
		    border: none;
		    background: none;
		    padding-right: 0px;
		}
		.new-input-group .input-group-addon i {
		    color: #17a6f0;
		    font-size: 14px;
		}
		 .check_error{
		  	border: 2px solid red;
		    outline: 0;
		    box-shadow: 0px 0px 25px 0px red;
		  }
		  .checkRepeat{
			  color: red;
	    	  line-height: 24px;
	    	}
	    	::-webkit-scrollbar {
			width: 8px;
			height: 8px;
			background: transparent;
		}
		
		::-webkit-scrollbar-track {
			background: transparent;
		}
		
		::-webkit-scrollbar-thumb {
			border-radius: 8px;
			background-color: #C1C1C1;
		}
		
		::-webkit-scrollbar-thumb:hover {
			background-color: #A8A8A8;
		}
		.container-fluid{
		height: 100%!important;
		padding: 0;
		}
		.proItem {
			position: relative;
			background: rgba(0,146,216,0.05);
	    	border: 1px solid transparent;
	    	border-radius: 8px;
	    	padding: 16px;
			cursor: pointer;
			margin-bottom: 16px;
			width: 100%;
            font-size: 14px;
            line-height: 22px;
            color: #868686;
		}
		.proItem-active {
			border-color: #0092D8;
		}
		.proItem-N{
			background: #f3f3f3;
    		cursor: not-allowed;
		}
		.item-checked {
			position: absolute;
			display: none;
			top: 0;
			right: 0;
      width: 0; 
			height: 0;
      border-color: #1e9ffe transparent; /*上下颜色 左右颜色*/
      border-width: 30px 0px 0px 30px;
      border-style: solid;
		}
		.item-checked-icon {
			position: absolute;
			display: none;
            color: #FFF;
            top: -2px;
            right: 3px;
		}
		.proItem-row {
			display: flex;
            align-items: center;
			/* line-height: 13px; */
    	/* margin-bottom: 10px; */
		}
		.proItem-row:last-child {
			margin-bottom: 0;
		}
		.buy-date,.yb-time,.yb-start,.yb-end {
			/* display: flex; */
			flex: 1;
			/* margin-right: 10px; */
		}
		.proItem-row-title {
			display: block;
    		overflow: hidden;
    		text-overflow: ellipsis;
    		white-space: nowrap;
            color: #262626 !important;
            font-size: 16px;
            line-height: 24px;
            font-weight: bold;
		}
        .brand-value {
            flex: 1;
            overflow: hidden;
            font-size: 14px;
            line-height: 22px;
            font-weight: normal;
        }
		.mine
		.proItem-N {
			background: #f3f3f3;
	   		cursor: not-allowed;
		}
		.btnGroup {
			position: fixed;
    		left: 0;
    		right: 0;
    		bottom: 0px;
    		text-align: center;
    		background: #FFF;
    		padding-top: 10px;
		}
		.cacheSpan{
            border: 1px solid transparent;
            border-radius: 4px;
            width: 64px;
            text-align: center;
            display: inline-block;
            height: 32px;
            line-height: 30px;
            position: relative;
            background: #F2F4F7;
            color: #262626;
            cursor: pointer;
		}
        .date-value,.start-value,.end-value, .time-value {
            color: #262626;
        }
        .proItem-row .line {
            width: 1px;
            height: 40px;
            background: #E8E8E8;
            margin: 0 16px;
        }
        .glyphicon {
            cursor: pointer;
        }
		.compensate{
			position: relative;
			padding: 16px;
			background: #F2F4F7;
			border-radius: 4px;
			margin-bottom: 16px;
		}
		.compensate #marcketScript,.compensate #marcketScript p {
			display: -webkit-box;
			-webkit-line-clamp: 7;
			-webkit-box-orient: vertical;
			overflow: hidden;
			text-overflow: ellipsis;
			word-break: break-word;
			line-height: 1.5;
			margin-bottom: 8px;
			max-height: 10.5em; 
			color: #333;
		}
		.compensate a {
			/* position: absolute;
			right: 16px;
			bottom: 16px; */
			display: inline-block;
			margin-left: 5px;
			float: right;
		}
		</style>
	</head>
	<body> 
		<div class="box" style="position: relative;display: block; height: 100%; padding-bottom: 40px;">
			<form id="orderGiftForm" class="form-inline" id="orderListForm" method="post" autocomplete="off" onsubmit="return false" >
			<input class="hidden" id="serviceTime">
			<input class="hidden" id="serviceName">
			<select id="peakendSpecialAreas" style="display:none" class="form-control input-sm" data-cust-context-path="/yq_common"  data-cust-mars="dict.getDictList('PEAKEND_SPECIAL_AREAS')"></select><!-- 特殊区域 不做补偿 -->
			<input class="hidden" id="compensateType" value="1">
				<input type="text" id="growValue" autocomplete="off" class="hidden">
                <div class="bg">
                    <div id="levelName">未注册</div>
                    <div class="record">
                        <div class="line"></div><span onclick="openPage()" style="cursor: pointer;">服务补偿记录</span> <span id="num"></span> <i onclick="openPage()" class="glyphicon glyphicon-chevron-right" style="margin-left: 4px;cursor: pointer;"></i>   
                    </div>
                    <p id="checkRepeatConetnt" style="display: none;"></p>
                </div>
				
				<div class="theitem" style="overflow: visible;height: auto;">
					<div class="itemMsg" style="overflow: visible;height: auto;">
						<div class="threeItem" style="position:relative;" onclick="chooseItem(this);helper_compensation_bury('实物礼品')" id="physicalGifts" data-type="1" style="width: 90px;"> <i class="iconfont icon-GIFT2"></i>实物礼品
							<div class="selectGift">
								<img src="./images/choose.png">
							</div>
						</div>
						<div class="threeItem" style="position:relative;" onclick="chooseItem(this);helper_compensation_bury('黄金会员')" id="userLevelGift" data-type="2" ><i class="iconfont icon-VIP2"></i>黄金会员
							<div class="selectUserLevel">
								<img src="./images/choose.png">
							</div>
						</div>
						<div class="threeItem" style="position:relative;" onclick="chooseItem(this);helper_compensation_bury('企微')" id="weixingGift" data-type="3"><i class="iconfont icon-WECHAT2"></i>企微
							<div class="selectweixing">
								<img src="./images/choose.png">
							</div>
						</div>
						<div class="threeItem" style="position:relative;" onclick="chooseItem(this);helper_compensation_bury('延保卡')" id="ybk" data-type="6"><i class="iconfont icon-CARD2"></i>延保卡
							<div class="selectyb">
								<img src="./images/choose.png">
							</div>
						</div>
						<div class="threeItem" style="position:relative;" onclick="chooseItem(this);helper_compensation_bury('现金补偿')" id="cashCompensation" data-type="7"><i class="iconfont icon-CASH2"></i>现金补偿
							<div class="selectcashCompensation">
								<img src="./images/choose.png">
							</div>
						</div>
						
					</div>
					
				</div>
				<div style="border: 1px solid #E8E8E8;width: 100%;height: 0px;margin: 0 0 24px;"></div>
                <div class="checkRepeat">
					<p id="cashCompensateContent"></p>
				</div>
				<p id="ybkTitle" style="font-weight: bold;"></p>
				<div class="marcket-head collapse-title-noclick">
                    <div class="text">补偿话术</div>
                    <i class="glyphicon glyphicon-click glyphicon-chevron-up" data-collapse="compensateInfo"></i>
                </div>
                <div class="marcket-content collapse-content" data-id="compensateInfo">
					<div class="compensate" >
						<div id="marcketScript"></div>
						<a href="#" onclick="showAllText()" >查看详情</a>
					</div>
				</div>
				<div class="theitem"  id="product" style="overflow: visible;height: auto;display: none;">
                    <div class="collapse-title" data-collapse="productInfo">
                        <div class="text">商品选择</div>
                        <i class="glyphicon glyphicon-chevron-up"></i>
                    </div>
					<div class="itemMsg" id="productlistShow" data-id="productInfo" style="overflow: visible;">
						
					</div>
				</div>
				<script type="text/x-jsrender" id="productlist">
				{{for}}
					<div class="imgItem" onclick="chooseItemgoods(this)" data-id="{{:dissku_id}}" data-name="{{:dis_sku_title}}" data-amount="{{:sale_price}}" data-tax="{{:tax_rate}}" data-naked="{{:naked_price}}">
						<img src="{{:goodsPicture}}" class="theimg">
						<div class="thetext" title="{{:dis_sku_title}}">{{:dis_sku_title}}</div>
						<div class="price"><span class="itemMessage">{{:ProductType}}</span>{{:sale_price}}</div>
						<div class="mask"></div>
						<img class="maskgou" src="/PeakEnd/pages/peakEnd/gift/images/choose.png" style="width: 26px;height: 26px;">
					</div>
				{{/for}}

				</script>
                <div class="collapse-title-noclick">
                    <div class="text">服务补偿信息录入</div>
                    <i class="glyphicon glyphicon-click glyphicon-chevron-up" data-collapse="baseInfo"></i>
                </div>
                <div class="collapse-content" data-id="baseInfo">
                    <div class="theitem">
                        <span class="theitemSpan"><span style="color: red;padding-right: 5px">*</span>用户姓名</span>
                        <div class="itemMsg">
                            <input type="text" name="customerName" id="customerName" onchange="checkSurname(this.value)" autocomplete="off" class="form-control input-sm " style="height: 100%;" id="name">
                        </div>
                    </div>
                    <div class="theitem" style="display: flex;">
                        <span class="theitemSpan"><span style="color: red;padding-right: 5px">*</span>用户号码</span>
                        <div class="itemMsg">
                            <input type="text" name="phone"  id="phone" onchange="checkPhone(this),changePhone(this)" autocomplete="off" class="form-control input-sm " style="height: 100%;flex: 1;" id="phone">
                        </div>
                        <div id="userloginstatus"></div>
                    </div>
                    <div class="theitem address" style="display: none;">
                        <span class="theitemSpan">收货地区</span>
                        <div class="itemMsg" style="display: flex;">
                            <input type="text" readonly onclick="phoneCode('areaNum')" id="areaName" autocomplete="off" class="form-control input-sm " style="height: 100%;flex: 1;background: #F2F4F7;" id="area" >
                        </div>
                    </div>

                    <div class="theitem address" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">收货地址</span>
                        <div class="itemMsg" style="overflow: visible;">
                            <textarea id="customerAddress"  onchange="orderAddressCheck()" rows="2"   autocomplete="off"  class="form-control input-sm"  ></textarea>
                        </div>
                    </div>
                    <div class="theitem giveItem" id="serviceMainType" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">服务请求</span>
                        <div class="itemMsg">
                            <!-- <input type="text" name="serviceMainTypeName" id="serviceMainTypeName" disabled autocomplete="off" class="form-control input-sm " style="height: 100%;"> -->
                            <textarea name="serviceMainTypeName" id="serviceMainTypeName" rows="2" disabled autocomplete="off" class="form-control input-sm "></textarea>
                        </div>
                    </div>

                    <!-- <div class="theitem giveItem" id="serviceMainType" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">服务请求</span>
                        <div class="itemMsg">
                            <input type="text" name="serviceMainTypeName" id="serviceMainTypeName" disabled autocomplete="off" class="form-control input-sm " style="height: 100%;">
                        </div>
                    </div>
                    <div class="theitem giveItem" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">服务小类</span>
                        <div class="itemMsg">
                            <input type="text" id="contactOrderSerItem2Name" disabled autocomplete="off" class="form-control input-sm " style="height: 100%;">
                        </div>
                    </div> -->
                    <div class="theitem giveItem requireId" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">诉求ID</span>
                        <div class="itemMsg">
                            <input type="text" name="contactUserRequireId" id="contactUserRequireId" autocomplete="off" class="form-control input-sm " style="height: 100%;flex: 1;">
                            <div class="input-group input-group-sm new-input-group">
                                <span class="input-group-addon" style="color: #2c9ade;font-weight: bold;" onclick="getServieOrderDetail(true)">代入服务单号</span>
                            </div>
                        </div>
                    </div>
                    <div class="theitem giveItem" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">服务单号</span>
                        <div class="itemMsg">
                            <input type="text" name="serviceOrderNo" id="serviceOrderNo" disabled autocomplete="off" class="form-control input-sm " style="height: 100%;">
                        </div>
                    </div>
                    <div class="theitem" style="display: flex;">
                        <span class="theitemSpan">接入单号</span>
                        <div class="itemMsg">
                            <input type="text" name="contactOrderCode"  disabled autocomplete="off" class="form-control input-sm " style="height: 100%;flex: 1;" id="contactOrderCode">
                        </div>
                    </div>
                    <div data-clone="clone_1"></div>
                    <div class="theitem giveItem" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">品牌品类</span>
                        <div class="itemMsg">
                            <input type="text" name="brandName" id="brandName" disabled autocomplete="off" class="form-control input-sm " style="height: 100%;">
                        </div>
                    </div>
                    <!-- <div class="theitem giveItem" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">品牌</span>
                        <div class="itemMsg">
                            <input type="text" name="brandName" id="brandName" disabled autocomplete="off" class="form-control input-sm " style="height: 100%;">
                        </div>
                    </div>
                    <div class="theitem giveItem" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">品类</span>
                        <div class="itemMsg">
                            <input type="text" name="prodName" id="prodName" disabled autocomplete="off" class="form-control input-sm " style="height: 100%;">
                        </div>
                    </div> -->
                    <div data-clone="clone_3">
                        <div class="theitem">
                            <span class="theitemSpan"><span style="color: red;padding-right: 5px">*</span>责任方</span>
                            <div class="itemMsg">
                                    <select  class="form-control input-sm input-sm" name="peakEnd.RESPONSIBLE" id="responsible" data-rules="required" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PEAK_END_RESPONSIBLE')"  onfocus="this.defaultIndex=this.selectedIndex;" >
                                             <option value="" >-请选择-</option>
                                     </select>					
                            </div>
                        </div>
                    </div>

                    <div data-clone="clone_1">
                        <div class="theitem">
                            <!-- <span style="color: red;padding-right: 5px">*</span> -->
                            <span class="theitemSpan" id="clientDiv"><span style="color: red;padding-right: 5px">*</span>委托方</span>
                            <div class="itemMsg">
                                <select class="form-control input-sm input-sm" name="peakEnd.CKIENT_CODE" id="ckientCode" data-rules="required" data-mars="common.sysCode('SYS_CLIENT')">
                                    <option value="">-请选择-</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div data-clone="clone_2">
                        <div class="theitem" style="overflow: visible;height: auto;">
                            <span class="theitemSpan">
                                <span id="remarksDiv">备注</span>
                                <br><div id="counter_remarks" class="counter mr-10 pull-right">0/80</div>
                            </span>
                            <div class="itemMsg" style="overflow: visible;">
                                <textarea onblur="checkRemarks()" onfocus="focusRemarks()" title="" id="remarks" rows="2" autocomplete="off" class="form-control input-sm " maxlength="80" placeholder="最多输入80个字符"></textarea>
                            </div>
                        </div>
                    </div>


                    <%--<div class="theitem cashCompensation" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">用户投诉内容</span>
                        <div class="itemMsg" style="overflow: visible;">
                            <textarea title="" id="remark" rows="4" disabled autocomplete="off" class="form-control input-sm " ></textarea>
                        </div>
                    </div>--%>

                    <div class="theitem hidden">
                        <span class="theitemSpan">电话区号</span>
                        <div class="itemMsg" style="display: flex;">
                            <input type="text" id="areaNum" autocomplete="off" class="form-control input-sm " style="height: 100%;flex: 1;" id="area" >
                            <div class="input-group input-group-sm new-input-group">
                                 <span class="input-group-addon" onclick="phoneCode('areaNum')"><i class="glyphicon glyphicon-zoom-in"></i></span>
                              </div>
                        </div>
                    </div>
                    <div class="theitem hidden">
                        <span class="theitemSpan">区域</span>
                        <div class="itemMsg" style="display: flex;">
                            <input type="text" id="areaCode" autocomplete="off" class="form-control input-sm " style="height: 100%;flex: 1;" id="area"> 
                            <!-- <input type="text" id="areaName" autocomplete="off" class="form-control input-sm " style="height: 100%;flex: 1;" id="area"> -->
                        </div>
                    </div>

                    <!-- <div data-clone="clone_4">
                        <div class="theitem"  style="overflow: visible;height: auto;">
                            <span class="theitemSpan"><span id="scriptDiv">营销话术</span>
                            <a href="#" onmouseout="hideAllText()" onmouseover="showAllText(this)" >查看详情</a>
                            </span>
                            <div class="itemMsg" style="overflow: visible;">
                                <textarea title="" id="marcketScript" rows="4" autocomplete="off" class="form-control input-sm " ></textarea>
                            </div>
                        </div>
                    </div> -->

                    <!-- <div data-clone="clone_2">
                        <div class="theitem" style="overflow: visible;height: auto;">
                            <span class="theitemSpan">
                                <span id="remarksDiv">短信内容</span> -->
                                <!-- <br><div id="counter_remarks" class="counter mr-10 pull-right">0/300</div> -->
                            <!-- </span>
                            <div class="itemMsg" style="overflow: visible;">
                                <textarea title="" id="content" rows="4" class="form-control input-sm " style="resize: none;" ></textarea>
                            </div>
                        </div>
                    </div> -->
                
                </div>

                <div class="collapse-title cashCompensation" data-collapse="cashInfo" style="display: none;">
                    <div class="text">现金补偿信息录入</div>
                    <i class="glyphicon glyphicon-chevron-up"></i>
                </div>
				
                <div class="collapse-content cashCompensation" data-id="cashInfo">
                    
                    <!-- 补偿类型默认隐藏，页面固定为现金补偿CASH-->
                    <%--<div class="theitem cashCompensation" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">补偿类型</span>
                        <div class="itemMsg">
                            <select  class="form-control input-sm input-sm" name="compensateItems" id="compensateItems" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PEAK_END_COMPENSATE_ITEM')">
                                <option value="" >-请选择-</option>
                            </select>
                        </div>
                    </div>--%>
                    <div class="theitem cashCompensation" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan"><span style="color: red;padding-right: 5px">*</span>补偿原因</span>
                        <div class="itemMsg">
                            <select  class="form-control input-sm input-sm" name="compensateCauseType" id="compensateCauseType" data-mars="common.sysCode('COMPENSATE_CAUSE_TYPE')">
                                <option value="" >-请选择-</option>
                            </select>
                        </div>
                    </div>

                    <div data-clone="clone_2"></div>
                    <div data-clone="clone_3"></div>

                    <div class="theitem cashCompensation" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan"><span style="color: red;padding-right: 5px">*</span>补偿金额</span>
                        <div class="itemMsg">
                            <input name="compensateCash" id="compensateCash" autocomplete="off" class="form-control input-sm" onchange="checkCash(this)" style="height: 100%;">
                        </div>
                    </div>
                    <div class="theitem cashCompensation" id="compensateCashDiv" style="overflow: visible;height: auto;display: none; flex-wrap: wrap;">
                    </div>

                    <div data-clone="clone_4"></div>

                    <div class="theitem cashCompensation" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">分中心</span>
                        <div class="itemMsg">
                            <input type="text" name="unitName" id="branchName" disabled autocomplete="off" class="form-control input-sm " style="height: 100%;">
                        </div>
                    </div>
                    <div class="theitem cashCompensation" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">网点</span>
                        <div class="itemMsg">
                            <input type="hidden" name="unitCode" id="unitCode">
                            <input type="text" name="unitName" id="unitName" disabled autocomplete="off" class="form-control input-sm " style="height: 100%;">
                        </div>
                    </div>
                    <div class="theitem cashCompensation" id="engineer" style="overflow: visible;height: auto;display: none;">
                        <span class="theitemSpan">工程师</span>
                        <div class="itemMsg" style="overflow: visible;">
                            <input type="hidden" name="engineerCode" id="engineerCode">
                            <input type="text" name="engineerName" id="engineerName" disabled autocomplete="off" class="form-control input-sm " style="height: 100%;">
                        </div>
                    </div>
                </div>
				
			
				<div class="theitem"  id="ybshow" style="overflow: visible;height: auto;display: none;">
                    <div class="collapse-title" data-collapse="ybInfo">
                        <div class="text">用户延保机器</div>
                        <i class="glyphicon glyphicon-chevron-up"></i>
                    </div>
					<div class="itemMsg clearfix"  data-id="ybInfo" id="userHistoryShow" style="grid-gap: 0;overflow: visible;display: flex;flex-wrap: wrap;justify-content: flex-start;">
				
						
					
					</div>
				</div>
				<script type="text/x-jsrender" id="userHistorylist">
				{{for}}
				<div class="proItem proItem-{{:canActiveFlag}}" data-id="{{:insideBarcode}}-{{:outsideBarcode}}" onclick="handleProItemClick(this)">
					<div class="item-checked"></div>
				
						<div class="item-checked-icon">✔</div>
						<div class="proItem-row proItem-row-title" style="color:#222">{{:customerAddress}}</div>

						<div class="proItem-row" style="margin-bottom: 8px;">
							<div class="brand-key">产品型号：</div>
							<div class="brand-value proItem-row-title">{{:brandName}}{{:prodName}} {{:productModel}}</div>
						</div>

						<div class="proItem-row" style="align-items: flex-start;">
							<div class="buy-date">
								<div class="date-key">购买日期：</div>
								<div class="date-value">{{LOCALTIME:purchaseDate}}</div>
							</div>

                            <div class="line"></div>

                            <div class="yb-start">
								<div class="start-key">保修开始：</div>
								<div class="start-value">{{LOCALTIME:repairStartDate}}</div>
							</div>

                            <div class="line"></div>

                            <div class="yb-end">
								<div class="end-key">保修结束：</div>
								<div class="end-value">{{repairEndDate:repairStartDate repairPeriod}}</div>
							</div>

							
						</div>

						<div class="proItem-row" style="margin-top: 12px;">
                            <div class="yb-time" style="display: flex;">
								<div class="time-key">保修期限：</div>
								<div class="time-value">{{:repairPeriod}}</div>
							</div>
							
						</div>
					</div>
					{{/for}}
				
				</script>
				<div class="btnGroup">
					<a id="submit" class="btn  btn-info" onclick="Submit()">提交</a>
					<a id="resetBtn" class="btn btn-warning btn-sm" onclick="Reset();helper_compensation_bury('重置')">重置</a>
                    <a id="unMatchBtn" class="btn btn-warning btn-sm" onclick="openDrawer()">不符合单据</a>
				</div>
			</form>
			
		</div>

        <script type="text/x-jsrender" id="reason">
            {{for}}
                <div class="unitem" data-reason="{{:id}}">{{:text}}</div>
            {{/for}}

            </script>

        <div class="drawer" id="drawer" style="display: none;">
            <div class="drawer-content" id="reasonWrapper" style="overflow-y: auto;">
            </div>
            <div class="drawer-footer">
                <a id="cancelBtn" onclick="closeRason()" class="btn btn-warning btn-sm">取消</a>
                <a id="okBtn" onclick="submitNoReason()" class="btn btn-warning btn-sm">确定</a>
            </div>
        </div>
	</body>
</html>

</EasyTag:override>

<EasyTag:override name="script">
		<script src="/PeakEnd/pages/peakEnd/gift/layui/jquery-1.9.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="/PeakEnd/pages/peakEnd/gift/layui/layui.js" type="text/javascript" charset="utf-8"></script>
		<script src="/PeakEnd/pages/peakEnd/gift/new_gift.js?v=20240909" type="text/javascript" charset="utf-8"></script>
		<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
		<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
		<script type="text/javascript" src="/yq_common/static/js/yq/extends.js"></script>
	<script type="text/javascript">
    
    var layIndex = null
    var productsArr = []
    var getScriptRes = ""
    var gradeObj = {
        grade: "0"
    }
    // 添加鼠标累计时长统计相关变量
    var mouseEnterTime = 0;
    var totalMouseTime = 0;


    function getDict() {
        ajax.daoCall({
            params: {'param': ""},
            controls: ['dict.getDictList("NON_COMPLIANCE_REASONS")']
        }, function(result) {
            console.log(result)
            var data = result['dict.getDictList("NON_COMPLIANCE_REASONS")'].data
            var arr = []
            for (var key in data) {
                arr.push({
                    text: data[key],
                    id: key
                })
            }
            console.log(arr)
            var reason = $.templates("#reason");
            var html=reason.render(arr);
			$("#reasonWrapper").html(html);

        }, {
            contextPath: '/yq_common',
        });
    }

    getDict()

    $(document).on('click', '.unitem', e => {
        console.log(e)
        $('.unitem').removeClass('active')
        $(e.target).toggleClass('active')
    })
    
    function openDrawer() {
        layIndex = layer.open({
            type: 1,
            title: '不符合单据',
            area: ['100%', '100%'],
            offset: [0, 0],
            resize: false,
            move: false,
            content: $('#drawer')
        });
    }

    function closeRason() {
        layer.close(layIndex)
    }

    function submitNoReason() {
        var dom = $('.unitem.active')[0];
        console.log(dom, "dom");
        if (!dom) {
            layer.alert("请选择不符合单据原因", {icon: 5});
            return
        }
        ajax.remoteCall('/PeakEnd/servlet/peakEnd?action=nonConformance', {
            'customerName': $('#customerName').val(),
            'phone': $("#phone").val(),
            'areaName': $('#areaName').val(),
            'areaCode': $('#areaCode').val(),
            'customerAddress': $('#customerAddress').val(),
            'responsible': $('#responsible').val(),
            'remarks': $('#remarks').val(),
            'startTime': $("#serviceTime").val(),
            'userAcc': $("#serviceName").val(),
            'growValue': $("#growValue").val(),
            'smsContent': $('#content').val(),
            'orgCode': orderData.orgCode,
            'brandName': orderData.brandName,
            'brandCode': orderData.brandCode,
            'prodName': orderData.prodName,
            'prodCode': orderData.prodCode,
            'orderServTypeCode': orderData.orderServTypeCode,
            'orderServTypeName': orderData.orderServTypeName,
            'orderSerItem1Name': orderData.orderSerItem1Name,
            'orderSerItem1Code': orderData.orderSerItem1Code,
            'orderSerItem2Name': orderData.orderSerItem2Name,
            'orderSerItem2Code': orderData.orderSerItem2Code,
            'unitName' : $("#unitName").val(),
            'unitCode' : $("#unitCode").val(),
            'engineerName' : $("#engineerName").val(),
            'engineerCode' : $("#engineerCode").val(),
            'branchCode' : orderData.branchCode,
            'branchName' : orderData.branchName,
            'areaNum': orderData.areaNum,
            'compensateType': $("#compensateType").val(),
            'summitResultFlag': '1',//提交补偿记录标识
            'compensateItems':  'GIFT',
            'serviceOrderNo': $("#serviceOrderNo").val(),
            'contactOrderCode': $("#contactOrderCode").val(),
            'nonConformanceReason': dom.dataset.reason
        }, function(res) {
            if (res.state == 1) {
                layer.alert('提交成功', {icon: 1, title: '提示', time: 3000, end: function() {
                    $('#checkRepeatConetnt').text('不符合赠送')
                    $('#checkRepeatConetnt').show()
                    $('#submit').addClass('hidden')
                    $('#unMatchBtn').addClass("hidden")
                    closeRason()
                }});
                parent.orderpopup.serviceCompObj.action='不符合单据'
                parent.orderpopup.bury_service_compensation&&parent.orderpopup.bury_service_compensation()
            } else {
                layer.alert(res.data, {icon: 5});
            }
        })
    }

    function openPage() {
        popup.openTab("/PeakEnd/pages/peakEnd/peakEnd-record.jsp",'坐席申请补偿明细(CC)',{customerTel: $("#phone").val()});
    }
    
    $(document).on('click', '.collapse-title', e => {
        console.log("click");
        $('.glyphicon', e.target).toggleClass('glyphicon-chevron-down')
        console.log(e.target, "e.target");
        $('[data-id="'+ e.target.dataset['collapse'] +'"]').slideToggle()
    })
    $(document).on('click', '.glyphicon-click', e => {
        console.log("click");
        $('.glyphicon', ".collapse-title-noclick").toggleClass('glyphicon-chevron-down')
        console.log(e.target, "e.target");
        $('[data-id="'+ e.target.dataset['collapse'] +'"]').slideToggle()
    })
	var productlist = $.templates("#productlist");
	var userHistorylist = $.templates("#userHistorylist");
	var orderData={};
$(function(){
            console.log("1");
	    	getOrderData();
			$("#orderGiftForm").render({success:function(req){
				if(req["dict.getDictList('PEAK_END_RESPONSIBLE')"]!=null){
					
    			}
			}});
			$("#responsible").render({success:function(req){
				if(req["dict.getDictList('PEAK_END_RESPONSIBLE')"]!=null){
				/* $("#responsible option").each(function(i,n){
	                if($(n).text()==parent.orderVue.$data.contactOrderSerItem1Name)
		                {
		                    $(n).attr("selected",true);
		                }
		            }) */
	    		}
			}});
			$("#peakendSpecialAreas").render({success:function(req){
				if(req["dict.getDictList('PEAKEND_SPECIAL_AREAS')"]!=null){
					if(checkSpecialAreas($('#areaCode').val())){
		 	    		layer.alert($('#areaName').val()+"属于特殊区域，不做服务补偿",{icon:5});
		 	    	}
    			}
			}});
	    	// getProductlist(orderData);//商品列表
	    	getUserHistorylist(orderData);
            getLevel(getScriptRes, gradeObj, GetScript, orderData);//获取会员信息
	    	checkRepeat(orderData);//重复标红
	    	// $("#weixingGift").addClass("choosedTop");//企微默认赠送
	    	 $("#areaNum").keydown(function() {
	 	       	if(event.keyCode=='13') {
	 	       		phoneCode('areaNum');
	 	       	}
	 	       });
			getCompensateCash();

            // 添加鼠标进入页面的累计时长统计
            $('body').mouseenter(function() {

                    mouseEnterTime = new Date().getTime();
                    console.log("鼠标进入页面");
            });

            $('body').mouseleave(function() {

                    var currentTime = new Date().getTime();
                    var timeDiff = (currentTime - mouseEnterTime) / 1000;
                    totalMouseTime += timeDiff;
                    console.log("鼠标离开页面，本次停留：" + timeDiff.toFixed(1) + "秒，累计：" + totalMouseTime.toFixed(1) + "秒");

                    // 将累计时间保存到父页面对象中
                    if (parent.orderpopup && parent.orderpopup.serviceCompObj) {
                        parent.orderpopup.serviceCompObj.Compensation_time = totalMouseTime?totalMouseTime.toFixed(1):0;
                    }
					mouseEnterTime=0
            });
			    // 监听服务补偿信息录入区域的输入框变化
            $('[data-id="baseInfo"] input, [data-id="baseInfo"] select, [data-id="baseInfo"] textarea').on('focus click', function() {
                if (parent.orderpopup && parent.orderpopup.serviceCompObj) {
                    parent.orderpopup.serviceCompObj.hasChange = 1;
                    console.log("服务补偿信息已修改");
                }
            });

            // 监听现金补偿信息录入区域的输入框变化
            $('[data-id="cashInfo"] input, [data-id="cashInfo"] select, [data-id="cashInfo"] textarea').on('focus click', function() {
                if (parent.orderpopup && parent.orderpopup.serviceCompObj) {
                    parent.orderpopup.serviceCompObj.hasChange = 1;
                    console.log("现金补偿信息已修改");
                }
            });
	})
	function getOrderData(){
	    	// $("#customerName").val(parent.orderVue.$data.customerName)
	    	$("#customerName").val(parent.orderVue.$data.serviceCustomerName)
	    	$("#phone").val(parent.orderVue.$data.customerMobilephone1)
			if(parent.orderVue.$data.serviceCustomerMobilephone1){
				$("#phone").val(parent.orderVue.$data.serviceCustomerMobilephone1)
			}
            console.log("peakEnd ueType:"+parent.orderVue.$data.ueType)
            if (parent.orderVue.$data.ueType === "ue"){
                $("#phone").val(parent.orderVue.$data.serviceCustomerMobilephone2)
            }
	    	$("#areaNum").val(parent.orderVue.$data.areaNum)
	    	$("#areaCode").val(parent.orderVue.$data.areaCode)
	    	$("#areaName").val(parent.orderVue.$data.areaName)
	    	$("#customerAddress").val(parent.orderVue.$data.customerAddress);
            // console.log(parent.orderVue.$data.contactUserRequireId, parent.orderVue.$data.serviceOrderNo, "info");
            $("#contactUserRequireId").val(parent.orderVue.$data.contactUserRequireId)
            $("#serviceOrderNo").val(parent.orderVue.$data.serviceOrderNo)
            //用来区分有没有进去服务补偿这个页面
            parent.orderpopup.serviceCompObj.Compensation_occasion=$("#serviceOrderNo").val()||1
            let serialId = '${param.serialId}'
            if(parent.orderVue.$data.contactOrderCode !== null && parent.orderVue.$data.contactOrderCode !== "") {
                $("#contactOrderCode").val(parent.orderVue.$data.contactOrderCode)
            }else {
                $("#contactOrderCode").val(serialId);
            }
            parent.orderpopup.serviceCompObj.Compensation_ID= $("#contactOrderCode").val()
            console.log(parent.orderVue.$data, "parent.orderVue.$data");

			orderData={
	    		phone:$("#phone").val(),
	    		orgCode:parent.orderVue.$data.orgCode,
	    		prodCode:parent.orderVue.$data.prodCode,
	    		prodName:parent.orderVue.$data.prodName,
	    		brandCode:parent.orderVue.$data.brandCode,
	    		brandName:parent.orderVue.$data.brandName,
				compensateType:'1',
				orderServTypeCode:parent.orderVue.$data.contactOrderServTypeCode,
				orderServTypeName:parent.$("#contactOrderServTypeCode").find("option:selected").text(),//获取下拉框的name
				orderSerItem1Name:parent.orderVue.$data.contactOrderSerItem1Name,
				orderSerItem1Code:parent.orderVue.$data.contactOrderSerItem1Code,
				orderSerItem2Name:parent.orderVue.$data.contactOrderSerItem2Name,
				orderSerItem2Code:parent.orderVue.$data.contactOrderSerItem2Code,
				areaNum:parent.orderVue.$data.areaNum,
				branchCode:parent.orderVue.$data.branchCode,
				branchName:parent.orderVue.$data.branchName,
			}
		    getContactUserRequireId(parent.orderfunc.nowContactUserRequireId);
            $("#brandName").val(orderData.brandName + "-" + orderData.prodName)
			// $("#brandName").val(orderData.brandName);
			// $("#prodName").val(orderData.prodName);
		    $("#serviceMainTypeName").val(orderData.orderServTypeName + "-" + orderData.orderSerItem2Name);
		    // $("#serviceMainTypeName").val(orderData.orderServTypeName);
		    // $("#contactOrderSerItem2Name").val(orderData.orderSerItem2Name);
			addressCheck();		
            // setTimeout(() => {
	    	//     GetScript(orderData)
            // }, 2000)
            getProductlist(orderData)
	    	peakEndCheck(orderData);
	    }
        function getProductlist(data) {
            ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=GetProduct",data,function(result) {
                if(result.state === 1) {
                    if (result.data.data && result.data.data.length) {
                            result.data.data.forEach(item => {
                                item.goodsPicture = item.main_pic_infos && item.main_pic_infos[0] && item.main_pic_infos[0].pic_url || ''
                            })
                            productsArr = JSON.parse(JSON.stringify(result.data.data))
                            var html=productlist.render(result.data.data);
						    $("#productlistShow").html(html);
                    } else {
                        $("#productlistShow").html("<div class='noProductlist'>无商品信息</div>")
                    }
                }
            })
        }
	    function GetScript(data){
			 ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=GetScript",data,function(result) {
					if(result.state == 1){
                        getScriptRes = result.data.IS_GOLD_MEMBER;
                        console.log(result.data.IS_GOLD_MEMBER, "result.data.IS_GOLD_MEMBER")
						if(result.data.GUIDED_SPEECH!=null){
							$("#serviceTime").val(result.data.startTime);
							$("#serviceName").val(result.data.userAcc);
								$(".marcket-content").show()
								$(".marcket-head").show()
                                let GUIDED_SPEECH1 = result.data.GUIDED_SPEECH ? result.data.GUIDED_SPEECH.replace(/\n<br>/g, '<br/>').replace(/\n/g, '<br/>'):'';
                                $("#marcketScript").html(GUIDED_SPEECH1);
								let GUIDED_SPEECH = result.data.GUIDED_SPEECH ? result.data.GUIDED_SPEECH.replace(/<br\/>/g, '\r\n').replace(/<[^>]+>/g, ''):'';
								$("#marcketScript").attr("title",GUIDED_SPEECH);
					
							$("#remarks").val(result.data.REMARKS);
                            $('#content').val(result.data.SMS_CONTENT);
							$("#responsible").render({success:function(req){
								if(req["dict.getDictList('PEAK_END_RESPONSIBLE')"]!=null){
                                    // console.log("责任方赋值");
									$("#responsible").val(result.data.RESPONSIBLE);
					    		}
							}});
						}else{
							$(".marcket-content").hide()
								$(".marcket-head").hide()
			 	    		layer.alert("当前诉求不允许进行服务补偿",{icon:5,time:2000});
			 				$("#submit").addClass("hidden");
                            $("#unMatchBtn").addClass("hidden");
						}
						if(!result.data.ALLOW_CASH || result.data.ALLOW_CASH!="Y") {
							//判断脚本配置中开启现金补偿操作
							$("#cashCompensation").addClass("cantChoose");
						}
                        if((!result.data.IS_GOLD_MEMBER || result.data.IS_GOLD_MEMBER!="Y") || (gradeObj.grade === '0' || gradeObj.grade >= "3")) {
							//判断脚本配置中开启会员补偿操作
                            console.log("getsScript addCant")
							$("#userLevelGift").addClass("cantChoose");
						}else {
                            console.log("getsScript removeCant")
                            console.log(result.data.IS_GOLD_MEMBER, gradeObj.grade, "remove")
                            $("#userLevelGift").removeClass("cantChoose");
                            // $("#userLevelGift").addClass("choosedTop");
                        }
                        if(!result.data.IS_PHY_COMP || result.data.IS_PHY_COMP!="Y") {
							//判断脚本配置中开启实物礼品补偿操作
							$("#physicalGifts").addClass("cantChoose");
						} else {
                            $("#physicalGifts").trigger('click')
                        }
                        if(!result.data.IS_WX_ENTER || result.data.IS_WX_ENTER!="Y") {
							//判断脚本配置中开启企微补偿操作
							$("#weixingGift").addClass("cantChoose");
						}
                        if(!result.data.IS_EXT_WARRANTY || result.data.IS_EXT_WARRANTY!="Y") {
							//判断脚本配置中开启延保卡补偿操作
							$("#ybk").addClass("cantChoose");
						}
						var roleCashCompensationConfigs = getCashCompensationConfig("");
						var flag = false;
						if(roleCashCompensationConfigs && roleCashCompensationConfigs.length>0){
							for(var i = 0; i<roleCashCompensationConfigs.length; i++){
								roleCashCompensationConfig = roleCashCompensationConfigs[i];
								if(roleCashCompensationConfig.ALLOW_ALL_CASH && roleCashCompensationConfig.ALLOW_ALL_CASH=="Y") {
									flag = true;
								}
							}
						}
						if(!flag) {
							if(!result.data.ALLOW_ALL_CASH || result.data.ALLOW_ALL_CASH!="Y") {
								//判断脚本配置中开启现金补偿操作
								$("#cashCompensation").addClass("cantChoose");								
							}
						}
					}else{
                        $("#productlistShow").html("<div class='noProductlist'>无商品信息</div>")
					}
			}, {async: true})
		}
	    //
	    function Reset(params) {
	    	//document.getElementById('some_frame_id').contentWindow.location.reload();
	    	location.reload()
	    }
	    function Submit() {
            var datatype = $('.threeItem.choosedTop').data('type')

			if ($("#customerName").val() == "" || $("#phone").val() == "" || $("#customerAddress").val() == "") {
				layer.alert("请填写用户信息", {icon: 5, time: 2000});
				return;
			}

            if($("#phone").val().length!=11 || !of_checkMobilePhone($("#phone").val())){
                layer.alert("请检查号码是否正确", {icon: 5});
                return;
            }

            if (!$('#areaCode').val() && datatype == '1') {
                layer.alert("请检查收货地区是否填写", {icon: 5});
                return;
            }

			if(!$("#responsible").val() || $("#responsible").val() === ""){
				layer.alert("请选择责任方", {icon: 5, time: 2000});
				return;
			}

			if (checkSpecialAreas($('#areaCode').val())) {
				layer.alert($('#areaName').val() + "属于特殊区域，不做服务补偿", {icon: 5});
				return;
			}
			let arr = [];
			let productId = "";
			let productName = "";
			let productAmount = "0";
			var physicalGifts = "0";
			//是否选择现金补偿
			var cashCompensation = "0";
			$('.choosedTop').each(function (a, b) {
				var dataType = $(b).data('type');
				if (dataType == '1') {
					physicalGifts = "1";
				}
				if (dataType == '7') {
					cashCompensation = "1";
				}
				arr.push(dataType);
			});
			$('.choosedBottom').each(function (a, b) {
				productId = $(b).data('id');  //自定义ID
				productName = $(b).data('name');
				productAmount = $(b).data('amount');
			});
			//只有选择赠送实物，才判断实物商品
			if (physicalGifts == "1" && productId === "") {
				layer.alert("未选择商品", {icon: 5, time: 2000});
				return;
			}

            // 判断是否选择了委托方
            // if($("#ckientCode").val() == "") {
            //     layer.alert("请选择委托方", {icon: 5, time: 2000});
			// 	return;
            // }
            // console.log($(".choosedTop .selectyb").length, $('.choosedTop .selectGift').length, $("#ckientCode").val(), "test")
            // return;
            // selectyb ==> 延保卡，判断是否选择了延保卡和委托方
			if ($(".choosedTop .selectyb").length > 0 && $("#ckientCode").val() == "") {
				layer.alert("请选择委托方", {icon: 5, time: 2000});
				return;
			}
            // selectGift ==> 实物礼品，判断是否选择了实物礼品和委托方
            if ($('.choosedTop .selectGift').length > 0 &&  $("#ckientCode").val() == "") {
                layer.alert("请选择委托方", {icon: 5, time: 2000});
				return;
            }
			let ckientCode = $("#ckientCode").val();
			let ckientName = $("#ckientCode").find("option:selected").text();

			if ($(".choosedTop .selectyb").length > 0 && selectyb == "") {
				layer.alert("未选择用户延保机器", {icon: 5, time: 2000});
				return;
			}
			let userMachineHistory = {}
			if ($(".choosedTop .selectyb").length > 0) {
				userMachineHistory = womCcUserMachineHistoryItemList[selectyb];
			}
            var proObj = $('.choosedBottom').data()
            var compensateDeatils = []
            if (datatype == '1' && proObj['id']) {
                compensateDeatils.push({
                    'disSkuId': proObj['id'],
                    'disSkuTitle': proObj['name'],
                    'salePrice': proObj['amount'],
                    'taxRate': proObj['tax'],
                    'nakedPrice': proObj['naked'],
                    'purchaseNum': 1
                })
            }
			let obj = {
				'type': arr,
				'customerName': $('#customerName').val(),
				'phone': $("#phone").val(),
				'areaName': $('#areaName').val(),
				'areaCode': $('#areaCode').val(),
				'customerAddress': $('#customerAddress').val(),
				'responsible': $('#responsible').val(),
				'remarks': $('#remarks').val(),
				'productId': productId,
				'productName': productName,
				'productAmount': productAmount,
				'startTime': $("#serviceTime").val(),
				'userAcc': $("#serviceName").val(),
				'growValue': $("#growValue").val(),
                'smsContent': $('#content').val(),
				'orgCode': orderData.orgCode,
				'brandName': orderData.brandName,
				'brandCode': orderData.brandCode,
				'prodName': orderData.prodName,
				'prodCode': orderData.prodCode,
				'orderServTypeCode': orderData.orderServTypeCode,
				'orderServTypeName': orderData.orderServTypeName,
				'orderSerItem1Name': orderData.orderSerItem1Name,
				'orderSerItem1Code': orderData.orderSerItem1Code,
				'orderSerItem2Name': orderData.orderSerItem2Name,
				'orderSerItem2Code': orderData.orderSerItem2Code,
                'unitName' : $("#unitName").val(),
                'unitCode' : $("#unitCode").val(),
                'engineerName' : $("#engineerName").val(),
                'engineerCode' : $("#engineerCode").val(),
                'branchCode' : orderData.branchCode,
                'branchName' : orderData.branchName,
				'areaNum': orderData.areaNum,
				'compensateType': $("#compensateType").val(),
				'ckientName': ckientName,
				'ckientCode': ckientCode,
				'userMachineHistory': userMachineHistory,
				'summitResultFlag': '1',//提交补偿记录标识
				'physicalGifts': physicalGifts,
                'compensateItems': datatype == '1' ? 'GIFT' : ( datatype == '7' ? 'CASH' : '' ),
                'compensateDeatils': compensateDeatils,
			}
            if (datatype == '1' || datatype == '7') {
                obj['serviceOrderNo'] = $("#serviceOrderNo").val();
                obj['contactOrderCode'] = $("#contactOrderCode").val();
            }
			if (cashCompensation == "1") {
				//校验参数必填
				var customerName = $("#customerName").val();
				var phone = $("#phone").val();
				var serviceMainTypeName = $("#serviceMainTypeName").val();
				var compensateItems = "CASH";// $("#compensateItems").val();页面补偿类型注释默认为现金补偿
				var compensateCauseType = $("#compensateCauseType").val();
				var compensateCash = $("#compensateCash").val();
				var serviceOrderNo = $("#serviceOrderNo").val();
				var remarks = $("#remarks").val()
				if (!customerName || !phone || !serviceMainTypeName || !compensateItems || !compensateCauseType || !compensateCash || !ckientCode || !remarks || !serviceOrderNo) {
					layer.alert("请检查现金补偿必填参数是否填写:姓名,号码,服务单号,服务请求,补偿类型,补偿原因,补偿金额,补偿说明,委托方,责任方", {icon: 5});
					return;
				}

				let re = /^\d+(.[0-9]{1})?$/;
				if (!re.test(compensateCash) || parseFloat(compensateCash) <= 0) {
					layer.alert("补偿金额填写错误，只能为非负数且保留一位小数", {icon: 5});
					return;
				}
				var compensateCashNum = parseFloat(compensateCash);
				if(!checkCashConfig(compensateCashNum)){
					return;
				}

				//选择现金补偿,判断用户的手机号码和来电号码不同，需要进行弹窗提醒，提醒案例为 “补偿号码为XXX 补偿金额为XX”
				var callPhone = parent.orderVue.$data.customerPhone;
				if (phone && callPhone && phone != callPhone) {
					layer.alert("补偿号码为:" + phone + ",补偿金额为:" + compensateCash, {icon: 5});
				}
				var orderProdCode = parent.orderVue.$data.prodCode
				if (orderData.prodCode && orderProdCode && orderData.prodCode != orderProdCode) {
					layer.alert("接入单品类和补偿页面品类不一致，请注意核对", {icon: 5});
					$("#submit").addClass("hidden");
                    $("#unMatchBtn").addClass("hidden");
					return;
				}
				obj.compensateCauseType = compensateCauseType;
				obj.compensateCauseTypeName = $("#compensateCauseType option:selected").text();
				obj.compensateItems = compensateItems;
				obj.compensateCash = compensateCash;
				obj.compensateExplain = remarks;
				obj.remark = $("#remark").val();

				obj.yunzhongName = $("#yunzhongName").val();

				layer.confirm('当前现金补偿对应品牌：'+obj.brandName+',品类：'+obj.prodName+'，确定要进行现金补偿吗？', {
					btn: ['确定', '取消']
					//按钮
				}, function (index) {
					// productCheck(obj, productId);
					if (!productCheckBoolean) return;
					if (!addressCheck()) return;
					productCompensate(obj)
					return;
				});
			}else{
			// productCheck(obj,productId);
                if(!productCheckBoolean)return;
                if(!addressCheck())return;
                productCompensate(obj)
                console.log(obj)
			}
		}
        function focusRemarks(){
            parent.orderpopup.serviceCompObj.descStartTime = new Date().getTime()

                }
        function checkRemarks(){
            let timeRange = ((new Date().getTime() - parent.orderpopup.serviceCompObj.descStartTime) / 1000).toFixed(1)
            parent.orderpopup.serviceCompObj.Compensation_description += Number(timeRange)
            parent.orderpopup.serviceCompObj.descStartTime=0
        }
	    function phoneCode(type){
	   		var open = checkWindowOpened("电话区号");
			if(open == false){
				layer.msg("电话区号已经存在，且处于最小化状态！",{icon:5});
				return ;
			}
	   		var areaNum = '';
	   		var areaName = '';
	   		if(type == 'areaNum'){
	   			areaNum = $("#areaNum").val();
	   		}else if(type == 'areaName') {
	   			areaName = encodeURI(orderVue.$data.areaName);
	   		}
		   	window.parent.popup.layerShow({type:2,title:'电话区号',area:['860px','660px'],offset:'20px',shade:0,maxmin:2,moveOut:true,shadeClose:false},"/neworder/pages/access/phone-code.jsp?parentId=peakEndIframe&areaCodeId=areaCode&areaNameId=areaName&areaNumId=areaNum&areaNum="+areaNum+"&areaName="+areaName);
	   	}
	  //检查是否打开某一弹窗
		 function checkWindowOpened(title) {
			var iframe = $(".layui-layer-iframe");
			if(iframe && iframe.length > 0){
				for(var i = 0; i< iframe.length ;i++){
					var t = $(iframe[i]).find(".layui-layer-title").html();
					if(title == t){
						return false;
						break;
					}
				}
			}
			return 1;
		}
	  
		 function phoneNumCallback (data) {
			var  customerAddress=$("#customerAddress").val();//
			var  areaName=$("#areaName").val();//替换前内容
 			$("#areaNum").val(data.areaN) 
 			$("#areaName").val(data.areaName) 
 			$("#areaCode").val(data.areaCode) 
 			if(areaName!=""&&areaName!=data.areaName){//和历史的区域信息不同
 				areaName.length
 				customerAddress=customerAddress.substring(areaName.length,customerAddress.length)//非区域部分
 				customerAddress=data.areaName+customerAddress
 				$("#customerAddress").val(customerAddress);
 				}
		 	} 
		 function queryAreaInfoByAddress(addr){
		 		var areaName = $("#areaNum").val();
		 			ajax.remoteCall("/neworder/servlet/comm?query=ecmAddr",{address:addr},function(result){
		 				if(result.state ==1 && result.data.length>0){
		 					var da = (result.data)[0];
	 						var nAreaCode = $("#areaCode").val();
	 						var title = '';
	 						if(nAreaCode && da.areaCode != nAreaCode){
	 							if( da.areaCode.substring(0,7) == nAreaCode.substring(0,7)){
	 								da.areaName = da.areaName.indexOf("_")>=0?da.areaName.substring(0,da.areaName.indexOf("_")):da.areaName;
	 								title = '地址区域未最小化到"'+ da.areaName +'"，是否需要最小化？';
	 							}else{
	 	 							title = '用户地址与当前区域不一致，是否替换区域信息？';
	 	 						}
	 						}
		 						
		 				}
		 			});
		 	}
		 function contentAlert(content){
			 layer.msg("电话区号已经存在，且处于最小化状态！",{icon:5});
		 }
		 //工单地址校验
		function orderAddressCheck(){
			parent.orderfunc.queryAreaInfoByAddress($("#customerAddress").val(),"1",{areaCode:$("#areaCode").val(),dataAttr:$("#areaName").val(),areaNum:$("#areaNum").val()});

		}
		function checkSurname(name){
			if( name.length > 1){
				$.ajax({ url:"/neworder/servlet/config?action=checkName&name="+name,
	 			    dataType : "json",
	 			    type : "POST",
	 				async : true,
	 				success : function(result) {
	 					if(result.state  == 1){
	 						//用户姓名数字校验
	 						var re = /^[0-9]+.?[0-9]*$/;
	 						var nameFlag = true;
	 						for(var i=0;i<name.length;i++){
	 							if(re.test(name.charAt(i))) {
	 								$("#customerName").addClass("check_error");
	 								nameFlag = false;
	 								break;
	 						    }
	 						}
	 						if(nameFlag){
	 							$("#customerName").removeClass("check_error");
	 							orderfunc.setSingleVueValue("order","serviceCustomerName",name);
	 						}
	 					}else{
	 						$("#customerName").addClass("check_error");
	 					}
	 				},
	 				error : function() {
						layer.alert("出现网络故障或者系统异常,请稍后再试!",{icon:7,time:15000});
	 				}
				});
				
			}else if( name.length == 1){
				layer.alert("姓名填写不正确，请重新填写！",{icon:2});
			}else{
				$("#customerName").removeClass("check_error");
			}
	 	}
		function checkPhone(obj){
			var phone_num=obj.value
			 if(!(of_checkMobilePhone(phone_num)||of_checkPhone(phone_num))){
				   $(obj).addClass("check_error");
				   return;
			   }else{
					$(obj).removeClass("check_error");
			   }
		}
		function checkSpecialAreas(areaCode){
			if(!areaCode){
				layer.alert("特殊区域检查失败，区域编码为空！",{icon:2});
				return false;
			}
			//当前当前区域属于特殊区域，则不补偿
			var provinceAreaCode = areaCode.substring(0,3);
			if(!$('#peakendSpecialAreas option[value="'+provinceAreaCode+'"]')||$('#peakendSpecialAreas option[value="'+provinceAreaCode+'"]').length==0) {
				return false;
			}
			return true;
		}
		
		/**
		 * 固话校验//固话（6、7、8位）数字
		 * @param chkPhone
		 * @returns
		 */
		function of_checkPhone (chkPhone) {
			if(chkPhone!='' &&(chkPhone.length==6||chkPhone.length==7||chkPhone.length==8)){
				var isPhone= /^-?\d+$/;
				return isPhone.test(chkPhone);
			}
			return false;
		}
		/**
		 * 手机号码校验
		 */
		function of_checkMobilePhone (chkMobilePhone){
			//手机号码（1开头，11位）
			if(chkMobilePhone!=''&&chkMobilePhone.length==11){
				var isMob=/^1\d{10}$/;
				return isMob.test(chkMobilePhone);
			}else if(chkMobilePhone!=''&&chkMobilePhone.length==12){
				var isMob=/^01\d{10}$/;
				return isMob.test(chkMobilePhone);
			}
			return false;
		}
		//修改手机号
		function changePhone(ths){
			orderData.phone=ths.value;
                getLevel(getScriptRes, gradeObj);//获取会员信息
		 	checkRepeat(orderData);
			if ($("#cashCompensation").hasClass("choosedTop")) {
				getUserStatus(ths.value);
				getNowUpperLimit(ths.value,$("#ckientCode").val());
			}
		 }
		//工单的写入补偿操作id
		function setPeakEndOpIds(ids){
			if(ids&&ids.length!=0){
				parent.setPeakEndOpIds(ids.toString());
			}
		 }
		
		function showAllText(){
			var text = $("#marcketScript").html();
	    	// if(!text){
	    	// 	text = "无"
	    	// }
			// layer.tips(text,$(obj),{tips:[1, '#0FA6D8'],time:0,area:['250px','auto']});
			 top.popup.layerShow({
				type:2,
				title: '详细补偿话术提醒',
				area: ['60%', '48%'],
				offset: ["12%", "10%"],
				resize: true,
				moveOut:true,
				// btn: ['确认', '取消'],  // 添加确认和取消按钮
				// btnAlign: 'c',
				// yes: function(index, layero){
                //     top.layer.close(index);  // 关闭弹窗
                //     let content = top.getEditorContentFromGift && top.getEditorContentFromGift()  // 确认按钮回调
				// 	$("#marcketScript").html(content)
				// 	// 确认操作逻辑
				// },
				// btn2: function(index, layero){  // 取消按钮回调
				// 	// 取消操作逻辑
				// 	layer.close(index);  // 关闭弹窗
				// }
			},
				"/PeakEnd/pages/peakEnd/gift/editorGift.html",
			);
			top.childPeakEndIframeContent = text
		 }
		function hideAllText(obj) { 
	 		layer.closeAll('tips');
		 }
		 var tip_idx
    $('.proItem-row-title').hover(function() {
        console.log(1111);
        tip_idx = layer.tips($(this).html(),$(this),{
            tips:1
        }) 
    },function() {
        layer.close(tip_idx)
    })
    $.views.converters("repairEndDate", function(repairStartDate,repairPeriod) {
    	if(repairStartDate!=null&&repairStartDate!=""&&repairPeriod!=null){
			return	getNextDate(getLocalDate(repairStartDate),repairPeriod)
		}else{
			return ""
		}
		});
	 $.views.converters("LOCALTIME", function(val) {//时间戳转时间字符
			if(val!=null&&val!=""){
				return	getLocalDate(val)
			}else{
				return ""
			}
		});
	 function getTodayDate(diffDate){
		return getNowTime(diffDate).substring(0,10);
	}

	function getLocalDate(str){  
	  var oDate = new Date(parseInt(str)),  
	  oYear = oDate.getFullYear(),  
	  oMonth = oDate.getMonth()+1,  
	  oDay = oDate.getDate(),  
	  oHour = oDate.getHours(),  
	  oMin = oDate.getMinutes(),  
	  oSen = oDate.getSeconds(),  
	  oTime = oYear +'-'+ getzf(oMonth) +'-'+ getzf(oDay) //最后拼接时间  
	  return oTime;  
	}; 
	function getzf(num){  
	    if(parseInt(num) < 10){  
	        num = '0'+num;  
	    }  
	    return num;  
	}
	function getNextDate(date, day = 1, format = "{y}-{m}-{d}") {
		  if (date) {
		    //处理传入年月日格式 例如 date = 2022年07月12日
		    date = date.match(/\d+/g).join("-");
		    //方式二处理传入年月日格式 (需要针对是否是年月日格式判断)
		    //date = date.replace(/[年月日]/g, "-").slice(0, -1);
		    //方式三处理传入年月日格式
		    //date = date.replace(/[年月]/g, "-").replace("日", "");
		    // console.log(date, "date"); // 2022-07-12
		    const nDate = new Date(date);
		    nDate.setDate(nDate.getDate() + parseInt(day));
		    const formatObj = {
		      y: nDate.getFullYear(),
		      m: nDate.getMonth() + 1,
		      d: nDate.getDate(),
		    };
		    return format.replace(/{([ymd])+}/g, (result, key) => {
		      const value = formatObj[key];
		      return value.toString().padStart(2, "0");
		    });
		  } else {
		    console.log("date为空或格式不正确");
		  }
		}
		
	function getServiceNoPeakNum(serviceOrderNo){
		//获取当前服务单是否30（配置）天内是否有非失败的补偿记录
		if(!serviceOrderNo){
			return;
		}
		ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=GetServiceNoPeakNum",{"serviceOrderNo":serviceOrderNo},function(result) {
			if(result && result.data){
				var data = result.data;
				if(data.existsHistory){
					layer.alert(data.existsMsg,{icon:5});
				}
			}
		})
	}
	
	var cashCompensateConfig;
	function getCashCompensationConfig(clientCode){
		//获取现金补偿配置信息
		var config = {};
		ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=GetCashCompensationConfig",{"clientCode":clientCode},function(result) {
			if(result && result.data){
				var data = result.data;
				if(data.config){
					config = data.config;
					if(clientCode){
						cashCompensateConfig = config;
					}
				}
			}
		},{async:false});
		return config;
	}
	var limitConfig;
	function getNowUpperLimit(customerPhone,clientCode){
		ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=GetNowUpperLimit",{"customerPhone":customerPhone,"clientCode":clientCode},function(result) {
			if (result && result.data) {
				limitConfig = result.data;
			}
		},{async:false});
		if(!limitConfig){
			limitConfig = {agentAmountLimit:0,custAmountLimit:0,custNumLimit:0,day:0};
		}
		$("#cashCompensateContent").html("用户过去"+limitConfig.day+"天，现金补偿已累计补偿"+limitConfig.custNumLimit+"次，金额"+limitConfig.custAmountLimit+"元");
	}
	
	function getServieOrderDetail(isSearch){
        console.log("4");
		//根据诉求ID获取详细的服务单和诉求信息 isSearch为true表示手工搜索，需要判断内容是否为空并提醒
		var contactUserRequireId = $("#contactUserRequireId").val();
		if(!contactUserRequireId){
			if(isSearch){
				layer.alert("请输入诉求ID进行检索",{icon:5});
			}
            console.log("5");
			return;
		}
		ajax.remoteCall('/neworder/webcall?action=contact.serviceOrderList',{
			"pageIndex":1,"pageSize":100,"pageType":3,
			"areaNum":"","contactUserRequireId":contactUserRequireId,"serviceOrderStatus":"","orgCode":"","prodCode":""
		},function(result) {
			if(result && result.data && result.data.length>0){
				var serviceOrder = result.data[0];
				$("#engineerName").val(serviceOrder.engineerName);
				$("#engineerCode").val(serviceOrder.engineerCode);
				$("#unitCode").val(serviceOrder.unitCode);
				$("#unitName").val(serviceOrder.unitName);
                console.log(serviceOrder.clientCode, "serviceOrder.clientCode");
				$("#ckientCode").val(serviceOrder.clientCode);
				if(serviceOrder.clientCode){
					//根据诉求带入委托方时动态加载委托方的配置信息
					getCashCompensationConfig(serviceOrder.clientCode);
					getNowUpperLimit($("#phone").val(),serviceOrder.clientCode);
				}
				var serviceOrderNo = serviceOrder.serviceOrderNo;
                if(serviceOrderNo && serviceOrderNo !== null) {
                    $("#serviceOrderNo").val(serviceOrderNo);
                    parent.orderpopup.serviceCompObj.Compensation_occasion=serviceOrderNo||""
                }
                if(serviceOrder.contactOrderNo && serviceOrder.contactOrderNo !== null) {
                    $("#contactOrderCode").val(serviceOrder.contactOrderNo);
                }
				getServiceNoPeakNum(serviceOrderNo);
				orderData.branchCode = serviceOrder.branchCode;
				orderData.branchName = serviceOrder.branchName;
				$("#branchName").val(serviceOrder.branchName);
				//判断品牌品类是否一致，弹窗提醒
				if ((serviceOrder.prodCode && orderData.prodCode && serviceOrder.prodCode != orderData.prodCode)
					|| (serviceOrder.brandCode && orderData.brandCode && serviceOrder.brandCode != orderData.brandCode)) {
					layer.alert("品牌品类不一致，诉求对应品牌为：" + serviceOrder.brandName + "，品类为：" + serviceOrder.prodName 
							+ "，工单对应品牌为：" + orderData.brandName + "，品类为：" + orderData.prodName,{icon:5,closeBtn:0,yes:function(){
						//重置
						Reset();
					}});
				}
				
				/*if(isSearch){
					//存在数据。进行重置服务单相关得信息
					orderData.orgCode = serviceOrder.orgCode;
					orderData.prodCode = serviceOrder.prodCode;
					orderData.prodName = serviceOrder.prodName;
					orderData.brandCode = serviceOrder.brandCode;
					orderData.brandName = serviceOrder.brandName;
					orderData.orderServTypeCode = serviceOrder.contactOrderServTypeCode;
					orderData.orderServTypeName = serviceOrder.contactOrderServTypeName;
					orderData.contactOrderSerItem1Name = serviceOrder.serviceLargeCategoryName;
					orderData.contactOrderSerItem1Code = serviceOrder.serviceLargeCategory;
					orderData.contactOrderSerItem2Name = serviceOrder.serviceSmallCategoryName;
					orderData.contactOrderSerItem2Code = serviceOrder.serviceSmallCategory;
					//重新获取判断脚本
					GetScript(orderData)
				}*/
			}else{
				//layer.alert("当前诉求为非原单诉求，请进行切换工单诉求重置或则搜索原诉求ID进行操作！",{icon:5});
			}
		});
	}

	function getUserStatus(customerPhone){
		if(!customerPhone){
			return;
		}
		//选择现金补偿,调用cs接口 查询当前手机号号码已关注公众号且三个月内有登录美的服务号的状态,包含识别成功、识别过期、识别失败、未登录
		console.log("包含识别成功、识别过期、识别失败、未登录")
		ajax.remoteCall('${ctxPath}/servlet/peakEnd?action=DoCompensateService',{"command":"compensateUserLoginStatus","customerPhone":customerPhone},function(result) {
			if(result && result.data && result.data.returnObject){
				var returnObject = result.data.returnObject;
				//11未注册，12未登录，13登陆失败，14d登陆成功
				var msg = "";
				var color = "";
				if (returnObject == "11") {
					msg = "未注册";
					color = "gray";
				} else if (returnObject == "12") {
					msg = "未登录";
					color = "#17a6f0";
				} else if (returnObject == "13") {
					msg = "登陆失败";
					color = "red";
				} else if (returnObject == "14") {
					msg = "登陆成功";
					color = "green";
				}
				$("#userloginstatus").html("<span style='padding-left:10px;font-weight:bold;color:"+color+";'>"+msg+"</span>");
			}
		});
	}
	function checkCashConfig(compensateCashNum){
		if (!cashCompensateConfig) {
			layer.alert("该委托方未配置对应的现金补偿信息,不能进行补偿", {icon: 5});
			return false;
		}
		var agentAmountLimit = limitConfig.agentAmountLimit;
		var custAmountLimit = limitConfig.custAmountLimit;
		var custNumLimit = limitConfig.custNumLimit;
		var accMonthlyimitMax = 0;
		var userMonthlyimitMax = 0;
		var currentUserRepetitionCountMax = 0;
		var account = 0;
		//判断本次补偿金额和现金补偿配置中的用户单次补偿最大金额进行比较
		if (compensateCashNum && cashCompensateConfig && cashCompensateConfig.length > 0) {
			for (var i = 0; i < cashCompensateConfig.length; i++) {
				var SINGLE_USER_MAX_AMOUNT = cashCompensateConfig[i].SINGLE_USER_MAX_AMOUNT
				if(SINGLE_USER_MAX_AMOUNT) {
					var singleUserMaxCount = parseFloat(SINGLE_USER_MAX_AMOUNT);
					if (singleUserMaxCount && singleUserMaxCount > account) {
						account = parseFloat(singleUserMaxCount)
					}
				}
				var ACC_MONTHLY_LIMIT= cashCompensateConfig[i].ACC_MONTHLY_LIMIT
				if(ACC_MONTHLY_LIMIT){
					var accMonthlyimit = parseFloat(ACC_MONTHLY_LIMIT);
					if(accMonthlyimit && accMonthlyimit > accMonthlyimitMax){
						accMonthlyimitMax = accMonthlyimit
					}
				}
				var USER_MONTHLY_LIMIT= cashCompensateConfig[i].USER_MONTHLY_LIMIT
				if(USER_MONTHLY_LIMIT){
					var userMonthlyimit = parseFloat(USER_MONTHLY_LIMIT);
					if(userMonthlyimit && userMonthlyimit > userMonthlyimitMax){
						userMonthlyimitMax = userMonthlyimit
					}
				}
				var CURRENT_USER_REPETITION_COUNT= cashCompensateConfig[i].CURRENT_USER_REPETITION_COUNT
				if(CURRENT_USER_REPETITION_COUNT){
					var currentUserRepetitionCount = parseFloat(CURRENT_USER_REPETITION_COUNT);
					if(currentUserRepetitionCount && currentUserRepetitionCount > currentUserRepetitionCountMax){
						currentUserRepetitionCountMax = currentUserRepetitionCount
					}
				}
			}
		}
		if (compensateCashNum > account) {
			layer.alert("本次补偿金额超过配置的用户单次补偿最大金额(" + account + ")，如仍需补偿，请线下升级", {icon: 5});
			return false;
		}
		
		if((compensateCash+agentAmountLimit) > accMonthlyimitMax){
			layer.alert("超过坐席自然月补偿金额上限，如仍需补偿，请线下升级",{icon:5});
			return false;
		}
		if((compensateCash+custAmountLimit) > userMonthlyimitMax){
			layer.alert("超过用户累计补偿金额上限，如仍需补偿，请线下升级",{icon:5});
			return false;
		}
		if((1+custNumLimit) > currentUserRepetitionCountMax){
			layer.alert("超过用户累计补偿次数上限，如仍需补偿，请线下升级",{icon:5});
			return false;
		}
		return true;
	}
	/**
	 * 判断页面是否选择现金补偿，用于外部页面（工单页面）判断使用
	 * @returns {boolean}
	 */
	function judgeIsCheckCashCompensation(){
		if ($("#cashCompensation").hasClass("choosedTop")) {
			return true
		}
		return false
	}
	var isInitContact = false;
	function getContactUserRequireId(contactUserRequireId,serviceOrderNo){
        console.log("3");
		//判断现金补偿是否选择，选择则进行检索，未选择则等待选择后在进行检索
		if(contactUserRequireId){
			$("#contactUserRequireId").val(contactUserRequireId);
		}
		if(serviceOrderNo){
			$("#serviceOrderNo").val(serviceOrderNo);
		}
        setTimeout(() => {
            getServieOrderDetail();
        }, 0)
        // if ($("#cashCompensation").hasClass("choosedTop")) {
		// 	getServieOrderDetail();
		// 	isInitContact = true;
		// }
	}
	/**
	 * 获取现金补偿，金额项字典，进行填充按钮至页面
	 */
	function getCompensateCash(){
		ajax.remoteCall('/yq_common/webcall',{
			"params":{"custContextPath":"/yq_common","custMars":"dict.getDictList('PEAK_END_COMPENSATE_CASH')","mars":"dict.getDictList('PEAK_END_COMPENSATE_CASH')","contextPath":"/yq_common"},
			"controls":["dict.getDictList('PEAK_END_COMPENSATE_CASH')"]
		},function(result) {
			if(result && result["dict.getDictList('PEAK_END_COMPENSATE_CASH')"]){
				var data = result["dict.getDictList('PEAK_END_COMPENSATE_CASH')"];
				var html = "";
				for (var key in data.data) {
					html += '<span class="cacheSpan" onclick="checkCompensateCash('+key+',this)">'+data.data[key]
							+'<img src="./images/choose.png" style="width: 16px;height: 16px; position: absolute;right: 0;top: 0;" class="hide">' +
							'</span>'
				}
				$("#compensateCashDiv").html(html);
			}else{
			}
		});
	}
	function checkCompensateCash(val,obj){
		$(".cacheSpan").find("img").addClass("hide");
		$(".cacheSpan").prop("style", "");
		$(obj).prop("style", "border: 2px solid #0092D8;color: #0092D8;");
		$(obj).find("img").removeClass("hide");
		$("#compensateCash").val(val);
	}
	function checkCash(obj){
		var compensateCash = $(obj).val();
		let re = /^\d+(.[0-9]{1})?$/;
		if (!re.test(compensateCash) || parseFloat(compensateCash) <= 0) {
			layer.alert("补偿金额填写错误，只能为非负数且保留一位小数", {icon: 5});
			return;
		}
		if(!checkCashConfig(compensateCash)){
			return;
		}
	}
	$('#remarks').on('input', function() {
		// 输入时更新计数器
		updateCounter(this.id);
	});
	$('#remarks').on('change', function() {
		// 输入时更新计数器
		updateCounter(this.id);
	});
	function updateCounter(id) {
		const maxLength = 80;
		const currentLength = $('#'+id).val().length;
		const remaining = maxLength - currentLength;
		// 更新显示
		$('#counter_'+id).text(currentLength +'/'+maxLength);
	}
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
