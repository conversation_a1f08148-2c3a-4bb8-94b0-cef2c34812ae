<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0">
  <link href="/sentiment/static/js/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet">
  <style>
    html,body {
        box-sizing: border-box;
        height: 100%;
    }
    .helper-page{
        height: 100%;
        padding: 16px;
        box-sizing: border-box;
        overflow-y: auto;
    }
  </style>
</head>
<body>
  <div id="app" class="helper-page">
    <div id="editor" style="width:100%;height:100%;"></div>
        <!-- <script id="editor" type="text/plain" style="width:100%;height:100%;"></script> -->
  </div>
</body>
<script type="text/javascript" src="/easitline-static/js/jquery.min.js"></script>
<script type="text/javascript" src="/sentiment/static/js/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="/sentiment/static/js/ueditor/ueditor.all.js"> </script>
<script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
<script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
<script>
  var heplerVm = new Vue({
    el: "#app",
    data() {
      return {
    
      }
    },
    methods: {

    },
    mounted() {
        // let content= yq.p("content")
        
      // var ue = UE.getEditor('editor');
      // ue.ready(function() {
      //   ue.setContent(top.childPeakEndIframeContent||'');
      // });
      $("#editor").html(top.childPeakEndIframeContent||'');
    } // mounted functio
  })
</script>
</html>