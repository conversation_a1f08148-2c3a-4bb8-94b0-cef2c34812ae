/**
 * 通用js
 */

/**
 * 千家姓校验
 */
function testSurname(surname){
	var pattern = /^[a-z]+[1-4]{0,1}$/;
	if(surname){
		return pattern.test(surname);
	}else{
		return true;
	}
}
/**
 * 去掉所有的html标记
 * @param str
 * @returns
 */
function replacehtml(str){
	str=str.replace(/<[^>]+>/g,"");
	return  str;
}

/**
 * 反选
 * @param obj
 */
function checkAll(id,obj) {
	$("#"+id).find("input:checkbox").prop('checked',obj.is(':checked'));
}

/**
 * 获取当前时间
 */
function getNowTime(diffDate){
	var date = new Date();
	if(diffDate){
		date =new Date( new Date().getTime() + 86400 * 1000 * diffDate );
	}
	return getStrByDate(date);
}

function getStrByDate(date){
	var month = (date.getMonth() + 1) > 9 ? (date.getMonth() + 1) : "0"	+ (date.getMonth() + 1);
	var day = date.getDate() < 10 ? "0" + date.getDate():date.getDate();
	var hour = date.getHours() <10? "0"+ date.getHours():date.getHours();
	var minute = date.getMinutes() <10 ? "0"+date.getMinutes():date.getMinutes();
	var second = date.getSeconds() <10 ? "0"+ date.getSeconds():date.getSeconds();
	return date.getFullYear() + "-" + month + "-"  + day + " " + hour + ":" + minute+ ":" + second;
}
/**
 * 获取今天日期
 */
function getTodayDate(diffDate){
	return getNowTime(diffDate).substring(0,10);
}

/**
 * 校验文本框是否为空
 */
function checkNull(value){
	if(value =="" || value ==null || value =="undefined"){
		return true;
	}
	return false;
}

/**
 * 校验文本框是否为空
 */
function formatNull(value,defaultValue){
	if(value){
		return value;
	}else{
		return defaultValue==null?"":defaultValue;
	}
}

function diffDateTime(startTime,endTime){
	var start = new Date(startTime);
	var end = new Date(endTime);
	return end.getTime() - start.getTime(); //时间差的毫秒数
}
/**
 * 批量校验电话号码
 * @param phone
 * @param separator 分隔符
 */
function batchCheckPhone(phones,separator){
	var arr = phones.split(separator);
	if(arr && arr.length > 0){
		for (var i in arr ) {
			var p = arr[i];
			if(checkNull(p)){
				continue;
			}else if(!checkPhone(p,true)){
				layer.alert("电话号码["+p+"]有误，请重新手机号码！");
				return false;
			}
		}
		return true;
	}else{
		layer.alert("电话号码栏输入有误，请重新输入！");
		return false;
	}
}

function checkPhone(phone,isAlert){
	var isMob=/^((\+?86)|(\(\+86\)))?(1[0-9]{10})$/;
	var mobilePhone = $.trim(phone);
	if(mobilePhone==""){
		if(!isAlert){
			alert("请填写手机号码！");
		}
		return false;
	}else if(!(isMob.test(mobilePhone))){
		if(!isAlert){
			alert("手机号码格式不正确，请重新填写！");
		}
		return false;
	}
	return true;
}

function downloadExl(data,url){
	if(url) {
		var form = $("<form></form>").attr("action", url).attr("method","post");
		for ( var key in data) {
			form.append($("<input></input>").attr("type", "hidden").attr("name",key).attr("value", data[key]));
		}
		form.appendTo('body').submit().remove();
	}
}

function timestamp2Str(timestamp,type) {
	var time = timestamp;
	if(timestamp && timestamp!=''&& String(timestamp).length>10 && String(timestamp).length != 19){
		var date = new Date(timestamp);
		Y = date.getFullYear() + '-';
		M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
		D = (date.getDate() < 10 ? '0'+date.getDate():date.getDate());
		h = ' ' + (date.getHours() < 10 ? '0'+date.getHours():date.getHours())+ ':';
		m = (date.getMinutes() < 10 ? '0'+date.getMinutes():date.getMinutes()) + ':';
		s = date.getSeconds() < 10 ? '0'+date.getSeconds():date.getSeconds();
		if(type == 'date'){
			time = Y+M+D;
		}else{
			time = Y+M+D+h+m+s;
		}
	}
	return time;
}

/**
 * 时间戳和今天零点比较，返回正数说明晚于今天零点
 * @param timestamp 时间戳
 * @returns
 */
function diffTimestamp(timestamp,defaultTimestamp) {
	if(!defaultTimestamp){
		defaultTimestamp = new Date(new Date().setHours(0, 0, 0, 0));
	}
	if(timestamp){
		return timestamp - defaultTimestamp;
	}else{
		return timestamp
	}
}

function overTimeRange(beginTime, endTime, nowTime) {
    var strb = beginTime.split (":");
    if (strb.length != 2) {
       return false;
    }

    var stre = endTime.split (":");
    if (stre.length != 2) {
        return false;
    }
    var b = new Date ();
    var e = new Date ();
    var n = nowTime;
    if(nowTime == null || nowTime == ''){
    	n = new Date();
    }

    b.setHours (strb[0]);
    b.setMinutes (strb[1]);
    e.setHours (stre[0]);
    e.setMinutes (stre[1]);
  
    if (n.getTime () - b.getTime () > 0 && n.getTime () - e.getTime () < 0) {
    	return false; //用户中心：从当前时间的下一个时间段开始选择
    } else if (b.getTime ()-n.getTime ()>=0) {
       return true;
    }
    return false;
}

function getSerialId(prefix) {
	var id = "";
	if(!checkNull(prefix)){
		id = prefix;
	}
	id = id + getNowTime().replace(/-|:| /g,"") + String(parseInt(Math.random()*900000)+100000)
	return id;
}

function isOverThisDate(dateTime){
	var nowDateTime = getTodayDate(0);
	var flag = compareTime(dateTime,nowDateTime);
	return flag;
}

function compareTime(startDate,endDate){
    var start=new Date(startDate.replace("-", "/").replace("-", "/"));  
    var end=new Date(endDate.replace("-", "/").replace("-", "/"));  
    if(end < start){  
        return false;  
    }  
    return true; 
}

function getWindowOpenStyle(width,height) {
	var left = Math.ceil(( window.screen.width - width )/2);
	var top = Math.ceil((window.screen.height- height )/2);
	var style="width="+width+"px,height="+height+"px,left="+left+"px,top="+top+"px,scrollbars=yes,resizable=no,status=1";
	return style;
	}
/**
 * 手机号码校验
 */
function checkMobilePhone(chkMobilePhone) {
	//手机号码（1开头，11位）
	if(chkMobilePhone!=''&&chkMobilePhone.length==11){
		var isMob=/^1[3-9]\d{9}$/;
		return isMob.test(chkMobilePhone);
	}else if(chkMobilePhone!=''&&chkMobilePhone.length==12){
		var isMob=/^01[3-9]\d{9}$/;
		return isMob.test(chkMobilePhone);
	}
	return false;
}
/**
 * 固话校验//固话（6、7、8位）数字
 * @param chkPhone
 * @returns
 */
function checkTelephone (chkPhone) {
	if(chkPhone!='' &&(chkPhone.length==6||chkPhone.length==7||chkPhone.length==8)){
		var isPhone= /^-?\d+$/;
		return isPhone.test(chkPhone);
	}
	return false;
}

$.views.converters("LOCALTIME", function(val) {//时间戳转时间字符
	if(val!=null&&val!=""){
		return	getLocalTime(val)
	}else{
		return ""
	}
});
function getLocalTime(str){  
  var oDate = new Date(parseInt(str)),  
  oYear = oDate.getFullYear(),  
  oMonth = oDate.getMonth()+1,  
  oDay = oDate.getDate(),  
  oHour = oDate.getHours(),  
  oMin = oDate.getMinutes(),  
  oSen = oDate.getSeconds(),  
  oTime = oYear +'-'+ getzf(oMonth) +'-'+ getzf(oDay) +' '+ getzf(oHour) +':'+ getzf(oMin) +':'+getzf(oSen);//最后拼接时间  
  return oTime;  
}; 
function getLocalDate(str){  
	  var oDate = new Date(parseInt(str)),  
	  oYear = oDate.getFullYear(),  
	  oMonth = oDate.getMonth()+1,  
	  oDay = oDate.getDate(),  
	  oHour = oDate.getHours(),  
	  oMin = oDate.getMinutes(),  
	  oSen = oDate.getSeconds(),  
	  oTime = oYear +'-'+ getzf(oMonth) +'-'+ getzf(oDay) //最后拼接时间  
	  return oTime;  
	}; 
//补0操作
function getzf(num){  
    if(parseInt(num) < 10){  
        num = '0'+num;  
    }  
    return num;  
}
function cleanVal(data,id){//如果值为空则清空某个id的值
	if($(data).val()==""){
		$("#"+id).val('');
	}
}

$.views.converters("timestamp",function(val){
	if(!checkNull(val)){
		return timestamp2Str(val);
	}else{
		return val;
	}
	
	
});