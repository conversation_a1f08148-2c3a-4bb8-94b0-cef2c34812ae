<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>坐席赠送记录查询</title>
	<style>
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form autocomplete="off" name="searchForm"class="form-inline" id="searchForm">
 				<div class="form-group hidden">
					   <div class="input-group input-group-sm" style="width: 200px;">
						      <span class="input-group-addon" style="width: 80px;">用户号码</span>	
							  <input type="text" name="PHONE" id="historyPhone" class="form-control input-sm" value="">
					   </div>
						<div class="input-group input-group-sm pull-right">
							<button type="button" class="btn btn-sm btn-default ml-10" onclick="reload()">搜索</button>
						</div>
				</div>
			<div class="ibox-content">
				<div class="row table-responsive">
					<table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" data-mars="peakEnd.recordList">
						<thead>
							<tr>
								<th class="text-c">序号</th>
								<th class="text-c">产品主体</th>
								<th class="text-c">产品品类</th>
								<th class="text-c" >服务请求</th>
								<th class="text-c" >服务请求小类</th>
								<th class="text-c">补偿项目</th>
								<th class="text-c">卡编号/商品编号</th>
								<th class="text-c">卡券金额/商品金额</th>
								<th class="text-c">赠送人</th>
								<th class="text-c">赠送时间</th>
							</tr>
						</thead>
						<tbody id="dataList"></tbody>
					</table>
					<script id="list-template" type="text/x-jsrender">
						{{for list}}
							<tr>
			                    <td>{{:#index+1}}</td>
							    <td>{{sysCodeFUN:ORG_CODE "ORG_CODE"}}</td>
                                <td>{{:PROD_NAME}}</td>
                                <td>{{:ORDER_SERV_TYPE_NAME}}</td>
								<td>{{:ORDER_SER_ITEM2_NAME}}</td>
                                <td>{{dictFUN:COMPENSATE_MODE "PEAKEND_COMPENSATE_MODE"}}</td>
                                <td>{{:COMPENSATE_NAME}}</td>
                                <td>{{:COMPENSATE_AMOUNT}}</td>
                                <td>{{:CREATE_ACC}}</td>
                                <td>{{:CREATE_TIME}}</td>
							</tr>
						{{/for}}				         
					</script>
				</div>
				<div class="row paginate" id="page">
					<jsp:include page="/pages/common/pagination.jsp">
						<jsp:param value="10" name="pageSize" />
					</jsp:include>
				</div>
			</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
	<script type="text/javascript"
		src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript">
		jQuery.namespace("record");
		requreLib.setplugs('wdate')//加载时间控件
		function reload() {
			$("#searchForm").searchData();
		}
		$(function() {
			$("#historyPhone").val($("#PHONE").val());
			$("#searchForm").render({success : function(result) {
				
			}});
		});
 		function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$(".moreSearch").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
		}
		record.exportExcel = function(){
			var startDate = $("#start_create_time").val();
			var endDate = $("#end_create_time").val();
			var diffTime = diffDateTime(startDate,endDate);
			var retMsg = checkTimeRange("1",diffTime);
			if(retMsg != true){
				alert(retMsg);
				return false;
			}
			location.href = "${ctxPath}/servlet/export?action=ExportPeakEndRecord&"
				+ $("#searchForm").serialize();
		}
		
		function reset() {
			document.searchForm.reset();
			var startDate = getTodayDate(-29);
			var endDate = getTodayDate();
			$("#start_create_time").val(startDate + " 00:00:00");
			$("#end_create_time").val(endDate + " 23:59:59");
			$("#tableHead").data("mars", "peakEnd.recordList");
			$("#searchForm").searchData();
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>