<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>洗悦家服务列表</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="">
             	<div class="ibox">
             	    <div class="ibox-title clearfix" style="border:none;margin-bottom:0">
             	         <!--   js 渲染时拿到数据遍历  -->
             	         <div class="form-group"  id ="PM_ID">
             	         </div>
						 <div class="form-group" style="margin-top:15px">
						       <div class="input-group input-group-sm">
								      <span class="input-group-addon">卡券名称</span>	
									  <input type="text" name="wash.productMenuName" id="inputName" class="form-control input-sm" style="width:140px">
							   </div>
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default"  onclick="serviceList.searchData(this,2)"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
						 </div>
				     </div>
	              	<div class="ibox-content" style="border:none">
		           	     <table class="table table-auto table-bordered table-hover table-condensed text-c" id="store"
		           	      data-container="shTab_List" data-template="shTab_list-template" 
		           	      data-mars="Wash.WASH_GOODS_LIST">
                             <thead>
	                         	 <tr>
	                         	      <th class="text-c">选项</th>
	                         	      <th class="text-c">卡券名称</th>
	                         	      <th class="text-c">卡券类型</th>
	                         	      <th class="text-c">面值</th>
	                         	      <th class="text-c">适用品类</th>
								      <th class="text-c">全品类适用</th>
								      <th class="text-c">订单最小使用金额</th>
	                         	      <th class="text-c">订单最大使用金额</th>
								      <th class="text-c" style="width: 300px">有效期</th>
								      <th class="text-c" style="max-width: 300px">卡券备注</th>
								      <th class="text-c">库存量</th>
								      <th class="text-c">客户中心</th>
	                         	      <th class="text-c">优惠劵短信</th>
	                         	      <!-- <th class="text-c">支付短信</th> -->
		   						 </tr>
                             </thead>
                             <tbody id="shTab_List"></tbody>
		                 	</table>
							<script id="shTab_list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td class="text-c"><label class="checkbox checkbox-info"><input type="checkbox" id="{{:cardId}}" data-id="{{:cardId}}" data-name="{{:cardName}}" ><span></span></label></td>
											<td>{{:cardName}}</td>
											<td>{{cardtype:cardType}}</td>
											<td>{{:cardFaceMoney}}</td>
											<td>{{prod:category allCategoryFlag}}</td>
											<td>{{allcate:allCategoryFlag}}</td>
											<td>{{:minReachAmountLimit}}</td>
											<td>{{:maxReachAmountLimit}}</td>
											<td>{{usefultime:startTime endTime}}</td>
											<td>{{:cardDesc}}</td>
											<td>{{:amount}}</td>
											<td>{{:customerName}}</td>
                                      		<td>{{SENDCOUPON:cardId cardName cardFaceMoney}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	requreLib.setplugs('wdate');
	jQuery.namespace("serviceList");
	
	var pmlist=[];//产品列表List
	var html="";//html
	var cardType = {"MONEY":'代金券',"DISCOUNT":'折扣券',"BALANCE":'余额券'};
	$(function(){
		$("#searchForm").render({
			success: function (result) {
				pmlist = result['Wash.WASH_GOODS_LIST'].pmlist;
				//debugger;
				//console.log(pmlist);
				html = "<span>快捷选择：</span>";
				html+="<a onclick= 'serviceList.searchData(this,2);' val='0' class='btn btn-outline btn-info btn-xs'>全部</a>"
				/*  
				* 后面可以优化为  JsRender 模板渲染  
				* html+="<a href= 'javascript:;' class='btn btn-outline btn-info btn-xs'>"+e+"</a>"
				*/
				$.each(pmlist,function(i,e){
					html+="<a href='javascript:void(0);' onclick= 'serviceList.searchData(this,12);' val='"+e+"' class='btn btn-outline btn-info btn-xs'>"+e+"</a>"
				});
				//var html = $("#templete").render(pmlist);
				$("#PM_ID").html(html);
				 $(".cardCnt").keydown(function(event){
		        	 if(!(event.keyCode==46)&&!(event.keyCode==8)&&!(event.keyCode==37)&&!(event.keyCode==39)){
		        		    if(!((event.keyCode>=48&&event.keyCode<=57)||(event.keyCode>=96&&event.keyCode<=105))){
		        		     return false;   	    
		        		     }
		        		 }
		        	 }).keyup(function(e){
			     		this.value=this.value.replace(/^0+/,'')
			     		}
		        	 ).change(function(e){
				     		if(this.value==""||this.value=="0"){
				     			this.value='1';
				     		}
			     		}
		        	 );
			}
		}); 
	});
	
	//数据搜索
	serviceList.searchData = function(theObj,index) {
		var key = '';
		var value=$(theObj).attr("val");
		if (''!=value && value!=undefined) {
			$("#inputName").val("");
			key = value;
		}else{
			key = $("#inputName").val();
		}
		
		$("#shTab_List tr").each(function(i,e){
			   $(this).children('td:nth-child('+index+')').each(function(j){//遍历每个的第一个数据
			 	var matchKey = $(this).text();
			    if('0'!=key){
			    	if(matchKey.match(key)){//匹配的话
						$(this).parent().show();
					}else{
						$(this).parent().hide();//不符合条件的隐藏
					}
			    }else{
			    	$(this).parent().show();// ==0 全部展示
			    }
				
			 });
		});
	}
	
	function sendCoupon(data) {
	    var userPhone = $("#PHONE").val();//
	    var orderId = $("#id").val();
	    var cardid = $(data).data('cardid');
	    var cardName = $(data).data('cardname');
	    var cardFaceMoney = $(data).data('cardfacemoney');
	    popup.layerShow({
			type : 1,
			title : "优惠券发送",
			offset : '20px',
			area:['530px','460px']
		},"${ctxPath}/pages/peakEnd/clean-send-coupon.jsp",{orderId:orderId,cardid:cardid,userPhone:userPhone,cardName:cardName,cardFaceMoney:cardFaceMoney});
	}
	$.views.converters("SENDCOUPON", function(val,name,cardFaceMoney ) {
    	return '<a  data-cardid="'+val+'" data-cardname="'+name+'" data-cardfacemoney="'+cardFaceMoney+'" onclick="javascript:sendCoupon(this)" href="javascript:#">发送</a>';
	});
	$.views.converters("cardtype", function(val) {
		return cardType[val];
	});
	$.views.converters("prod", function(category,allCategoryFlag) {
		if(!category&&allCategoryFlag=="Y"){
			return "-";
		}else {
			return "test";
		}
	});
	$.views.converters("usefultime", function(t1,t2) {
		var startTime = new Date(t1);
		var endTime = new Date(t2);
		return getDate(startTime) + " - " + getDate(endTime);
	});
	$.views.converters("allcate", function(val) {
		if(val=="Y"){
			return "是";
		}else{
			return "否";
		}
	});
	
	function getDate(val) {
		y = val.getFullYear(),
		m = val.getMonth() + 1,
		d = val.getDate();
        return y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " " + val.toTimeString().substr(0, 8);
    }
			
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>