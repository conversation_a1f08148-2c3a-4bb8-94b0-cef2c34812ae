<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>坐席申请补偿明细(CC)</title>
</EasyTag:override>
<EasyTag:override name="content">
	<form autocomplete="off" name="searchForm"class="form-inline" id="searchForm">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group paramDiv">
					<h5>
						<span class="glyphicon glyphicon-list"></span> 坐席申请补偿明细(CC)
					</h5>
				</div>
				<hr class="paramDiv" style="margin: 5px -15px">
 				<div class="form-group paramDiv">
 					   <div class="input-group width-18">
						      <span class="input-group-addon" style="width:30%">主体</span>	
							  <select class="form-control input-sm width-70" name="ORG_CODE" id = "orgCode"  data-cust-context-path="/neworder" data-cust-mars="comm.sysCode('ORG_CODE')" >
                                     <option value="" selected="selected">--请选择--</option>
                              </select>
					   </div>
					   <div class="input-group input-group-sm">
						      <span class="input-group-addon">赠送人账号</span>	
							  <input type="text" name="CREATE_ACC" class="form-control input-sm" >
					   </div>
					   <div class="input-group input-group-sm" style="width: 200px;">
						      <span class="input-group-addon" style="width: 80px;">用户号码</span>	
							  <input type="text" name="PHONE" class="form-control input-sm" value="${param.customerTel}" >
					   </div>
					   <div class="input-group width-36">
					    	  <span class="input-group-addon">补偿项目</span>	
	                          <select name="COMPENSATE_MODE" id="COMPENSATE_MODE" class="form-control input-sm" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PEAKEND_COMPENSATE_MODE')" data-mars-top="true" >
	                               <option value="">请选择</option>
	                          </select>
					   </div>
					   <div class="input-group width-36">
					    	  <span class="input-group-addon">补偿类型</span>	
	                          <select name="COMPENSATE_TYPE" data-rules="required" id="COMPENSATE_TYPE" class="form-control input-sm" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PEAKEND_COMPENSATE_TYPE')" data-mars-top="true" >
	                               <option value="">请选择</option>
	                          </select>
					   </div>
					  <div class="input-group input-group-sm" >
         		              <span class="input-group-addon" style="width: 80px;">赠送时间</span>	
						  <input type="text" name="START_CREATE_TIME" id="start_create_time"  class="form-control input-sm Wdate"  onFocus="WdatePicker({onpicked:function(){this.blur();maxTime()},dateFmt:'yyyy-MM-dd HH:mm:ss'})">
						  <span class="input-group-addon" >-</span>	
						  <input type="text" name="END_CREATE_TIME" id="end_create_time"  class="form-control input-sm Wdate"  onclick="WdatePicker({minDate:'#F{$dp.$D(\'start_create_time\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">									  
				   	  </div>
				   	  <div class="input-group input-group-sm ">
					      <span class="input-group-addon">运营区域</span>	
							<select class="form-control input-sm" id="areaCode" name="areaCode" data-mars="common.getDept" style="width:100px;">
								<option value="">请选择</option>
							</select>
					   </div>
					  <div class="input-group input-group-sm ">
					      <span class="input-group-addon">坐席班组</span>	
							<select class="form-control input-sm" id="agentDept" name="agentDept" style="width:100px;">
								<option value="">请选择</option>
							</select>
					   </div>
                       <div class="input-group width-36">
                        <span class="input-group-addon">是否补偿</span>
                        <!-- data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PEAKEND_COMPENSATE_TYPE')"  -->
						   <select name="SUBMIT_STATES" data-rules="required" id="SUBMIT_STATES" class="form-control input-sm" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('SUBMIT_STATES')" data-mars-top="true" >
							   <option value="">请选择</option>
						   </select>
                 </div>
				</div>
				<div class="form-group paramDiv">
					<div class="input-group input-group-sm pull-right">
						<button type="button" class="btn btn-sm btn-default" onclick="reset()">重置</button>
						<button type="button" class="btn btn-sm btn-default ml-10" onclick="reload()">搜索</button>
						<EasyTag:res resId="peak_end_compensate_export">
							<button type="button" class="btn btn-sm btn-success ml-10" onclick="record.exportExcel()">导出</button>
						</EasyTag:res>
					</div>
				</div>
			<div class="ibox-content">
				<div class="row table-responsive">
					<table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" data-mars="peakEnd.recordList">
						<thead>
							<tr>
								<th class="text-c">序号</th>
								<th class="text-c">产品主体</th>
								<th class="text-c">产品品类</th>
								<th class="text-c">产品品牌</th>
								<th class="text-c">服务单号</th>
								<th class="text-c">接入单号</th>
								<th class="text-c">区号</th>
								<th class="text-c">用户姓名</th>
								<th class="text-c">号码</th>
								<th class="text-c">地址</th>
								<th class="text-c" style="width:50px;max-width:50px;min-width:50px">查看</th>
								<th class="text-c" >服务请求</th>
								<th class="text-c" >服务请求大类</th>
								<th class="text-c" >服务请求小类</th>
								<th class="text-c">回访结果</th>
								<th class="text-c">补偿类型</th>
								<th class="text-c">补偿项目</th>
								<th class="text-c">卡券码</th>
								<th class="text-c">卡券名称</th>
								<th class="text-c">卡券金额</th>
								<th class="text-c">责任人</th>
								<th class="text-c">委托方</th>
								<th class="text-c">延保卡号</th>
								<th class="text-c">延保类型</th>
								<th class="text-c">赠送人</th>
								<th class="text-c">运营区域</th>
								<th class="text-c">坐席班组</th>
								<th class="text-c">赠送时间</th>
								<th class="text-c">备注</th>
                                <th class="text-c">是否补偿</th>
                                <th class="text-c">不符合补偿原因</th>
                                <th class="text-c">操作</th>
							</tr>
						</thead>
                        <!-- {{:~registerData(#index, #data)}} -->
						<tbody id="dataList"></tbody>
					</table>
					<script id="list-template" type="text/x-jsrender">

						{{for list}}
							<tr>
			                    <td>{{:#index+1}}</td>
							    <td>{{sysCodeFUN:ORG_CODE "ORG_CODE"}}</td>
                                <td>{{:PROD_NAME}}</td>
                                <td>{{:BRAND_NAME}}</td>
                                <td>{{:SERVICE_ORDER_NO}}</td>
                                <td>
                                {{if CONTACT_ORDER_CODE && CONTACT_ORDER_CODE !== ""}}
									{{:CONTACT_ORDER_CODE}}
								{{else}}
									{{:CONTACT_ORDER_ID}}
								{{/if}}
								</td>
                                <td>{{:AREA_NUM}}</td>
                                <td>{{call:CUSTOMER_NAME CUSTOMER_NAME_data #index+1 'name' fn='getData2'}}</td>
                                <td>{{call:PHONE PHONE_data #index+1 'phone' fn='getData2'}}</td>
                                <td>{{call:ADDRESS ADDRESS_data #index+1 'addr' fn='getData2'}}</td>
                                <td title="査看" class="text-c">
												<span onclick="showDetail('{{:#index+1}}','{{:CUSTOMER_NAME_data}}','{{:PHONE_data}}','{{:ADDRESS_data}}')" class="glyphicon glyphicon-eye-open"
												id="show{{:#index+1}}"
												style="color:rgb(255,140,60);">
												</span>
											</td>
                                <td>{{:ORDER_SERV_TYPE_NAME}}</td>
								<td>{{:ORDER_SER_ITEM1_NAME}}</td>
								<td>{{:ORDER_SER_ITEM2_NAME}}</td>
                                <td>{{dictFUN:VISIT_RESULT "PEAK_END_RESULT"}}</td>
								<td>{{dictFUN:COMPENSATE_TYPE "PEAKEND_COMPENSATE_TYPE"}}</td>
                                <td>{{dictFUN:COMPENSATE_MODE "PEAKEND_COMPENSATE_MODE"}}</td>
                                <td>
								{{if COMPENSATE_MODE=='4'||COMPENSATE_MODE=='5'}}
									{{:COMPENSATE_NO}}
								{{/if}}
								</td>
                                <td>{{:COMPENSATE_NAME}}</td>
                                <td>{{amount:COMPENSATE_AMOUNT}}</td>
                                <td>{{dictFUN:RESPONSIBLE "PEAK_END_RESPONSIBLE"}}</td>
                                <td>{{:CKIENT_NAME}}</td>
                                <td>{{getData:CARD_CODE 3 }}</td>
                                <td>{{cardType:CARD_CODE }}</td>
                                <td>{{:CREATE_ACC}}</td>
                                <td>{{:AGENT_AREA_NAME}}</td>
                                <td>{{:AGENT_DEPT_NAME}}</td>
                                <td>{{:CREATE_TIME}}</td>
                                <td>{{:REMARKS}}</td>
                                <td>{{dictFUN:SUBMIT_STATES "SUBMIT_STATES"}}</td>
                                <td>{{dictFUN:NON_CONFORMANCE_REASON "NON_COMPLIANCE_REASONS"}}</td>
                                {{if SUBMIT_STATES == '0'}}
                                <td><button type="button" data-data="{{:~encodeData(#data)}}" onclick="submitCompensation(this)">提交</button></td>
                                {{else}}
                                <td></td>
                                {{/if}}
							</tr>
						{{/for}}				         
					</script>
				</div>
				<div class="row paginate" id="page">
					<jsp:include page="/pages/common/pagination.jsp">
						<jsp:param value="10" name="pageSize" />
					</jsp:include>
				</div>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
	<script type="text/javascript"
		src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript">
        var targetItems = null;
		jQuery.namespace("record");
		requreLib.setplugs('wdate')//加载时间控件
        $.views.helpers({
            encodeData: function(data) {
                return encodeURIComponent(JSON.stringify(data));
            }
        });
		function reload() {
			$("#searchForm").searchData();
		}
		var isShowParam = '${param.isShowParam}';
		$('#areaCode').change(function() {
			var value = $(this).val();
			if(value != "") {
				value = "'" + value.replace(/,/g, "':'") + "'";
			}
			$('#agentDept').data('mars', 'common.getDeptAll("5", "'+value+'")');
			$('#agentDept').render();
		});
		$(function() {
			var startDate = getTodayDate(-29);
			var endDate = getTodayDate();
			$("#start_create_time").val(startDate + " 00:00:00");
			$("#end_create_time").val(endDate + " 23:59:59");
			if (isShowParam && isShowParam == "0") {
				$(".paramDiv").addClass("hide");
				$("#start_create_time").val("");
				$("#end_create_time").val("");
			}
			$("#searchForm").render({success : function(result) {
				
			}});
		});
		function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$(".moreSearch").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
		}

        function submitCompensation(buttonElement) {
            var encodedDataStr = buttonElement.getAttribute('data-data');
            var decodedDataStr = decodeURIComponent(encodedDataStr);
            var dataObj = JSON.parse(decodedDataStr);
            let params = {
                ...dataObj
            }
            popup.layerShow({
                type: 2,
                title: '输入接入单号',
                area: ['430px', '200px'],
                offset: '20px'
            }, "${ctxPath}/pages/peakEnd/peakEnd-record-submit.jsp", params);
            // // 现在您可以使用 data 对象中的数据了
            // var contactOrderNo=prompt("请输入接入单号",""); //显示默认文本 "Keafmd"
            // if (contactOrderNo!=null && contactOrderNo!=""){ 
            //     var data = {
            //         ...dataObj,
            //         contactOrderNo: contactOrderNo
            //     }
            //     ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=SubmitErrorMessage",data,function(result) {
            //         console.log("result:", result);
            //     })
            // }
            // console.log(JSON.stringify(data)); // 输出数据以验证
        }
		
		record.exportExcel = function(){
			var startDate = $("#start_create_time").val();
			var endDate = $("#end_create_time").val();
			var diffTime = diffDateTime(startDate,endDate);
			var retMsg = checkTimeRange("1",diffTime);
			if(retMsg != true){
				alert(retMsg);
				return false;
			}
			location.href = "${ctxPath}/servlet/export?action=ExportPeakEndRecord&"
				+ $("#searchForm").serialize();
		}
		
		function reset() {
			document.searchForm.reset();
			var startDate = getTodayDate(-29);
			var endDate = getTodayDate();
			$("#start_create_time").val(startDate + " 00:00:00");
			$("#end_create_time").val(endDate + " 23:59:59");
			$("#tableHead").data("mars", "peakEnd.recordList");
			$("#searchForm").searchData();
		}
		$.views.converters("getData", function(val,type) {//时间戳转时间字符
			if(type==1){
				if(val && val.length>6) {
					return desensitization(val, 3, 7);
				}else if(val && val.length>3){
					return desensitization(val,3,val.length);
				}else if(val && val.length>1){
					return desensitization(val,1,val.length);
				}else if(val){
					return "*";
				}
			}else if(type==2){
				if(val && val.length>1){
					return desensitization(val,1,val.length);
				}else if(val){
					return "*";
				}
			}else if(type==3){
				if(val && val.length>9) {
					return desensitization(val, 9, val.length-4);
				}else if(val){
					return "***";
				}
			}
			return val;
		});
		$.views.converters("cardType", function(val) {//时间戳转时间字符
			if(val!=""){
				return "1年延保";
			}
			return "";
		});
		function desensitization(str,beginStr,endStr){
			var len = str.length;
			var leftStr = str.substring(0,beginStr);
			var rightStr = str.substring(endStr,len);
			var str = ''
			var i = 0;
			try {
				for (i = 0; i < endStr-beginStr;i++) {
					str = str + '*';
				}
			} catch (error) {

			}
			str = leftStr + str + rightStr;
			return str;
		}

		$.views.converters("amount", function(val) {//时间戳转时间字符
			if(val && val.indexOf(".")==0) {
				return "0"+val
			}
			return val;
		});
		function showDetail(id,customerName,customerTel1,address){
			showDetailCommon({
				"model":"PeakEnd",
				"url":"/PeakEnd/pages/peakEnd/peakEnd-record.jsp",
				"action":"acces",
				"describe":"用户査询[坐席申请补偿明细]数据，査看[{{customerName}}]敏感信息:[用户号码1：{{customerTel1}},地址：{{address}}]"},id,customerName,address,customerTel1);
		}
	</script>
	<script type="text/javascript" src="/iccportal5/static/js/privacyUtil.js"></script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>
