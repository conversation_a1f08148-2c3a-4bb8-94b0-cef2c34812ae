<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>脚本配置</title>
    <style>
        .menuContent {
            display: none;
            position: absolute;
            border: 1px solid rgb(170, 170, 170);
            max-width: 220px;
            max-height: 350px;
            z-index: 10;
            overflow: auto;
            background-color: #f4f4f4
        }

        .textarea-style {

        }
        #editForm{
            padding-bottom: 48px;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="editForm" data-mars="scriptDao.getScriptDetail" data-mars-reload="true" method="post" autocomplete="off"
          data-mars-prefix="Script.">
        <input class="hidden" value="${param.ID }" name="Script.ID">
        <table class="table table-edit table-vzebra mt-10">
            <tbody>
            <tr>
                <td>主体</td>
                <td class="remind-content">
                    <select name="Script.ORG_CODE" id="orgCode" class="form-control input-sm" readonly
                            data-mars="Dict.DICT_SYS_CODE('ORG_CODE')">
                        <option value="">--请选择--</option>
                    </select>
                </td>
                <td>产品品牌</td>
                <td>
                    <input type="hidden" name="Script.BRAND_NAME" id="brandName"/>
                    <select name="Script.BRAND_CODE" id="brandCode" data-mars-top="true" readonly data-rules="required"
                            onchange="scriptConf.changeBrand(this)" class="form-control input-sm"
                            data-context-path="/neworder" data-mars="comm.getBrandlist">
                        <option value="">请选择</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>品类</td>
                <td>
                    <div class="input-group input-group-sm new-input-group">
                        <input type="hidden" name="Script.PROD_CODE" id="prodCode"/>
                        <input class="form-control input-sm width-70" id="proCodeShow" name="Script.PROD_NAME"
                               data-rules="required"/>
                        <span class="input-group-addon" onclick="scriptConf.productType()"><i
                                class="glyphicon glyphicon-zoom-in"></i></span>
                    </div>
                </td>

            </tr>
            <tr>
                <td width="60px">服务请求</td>
                <td>
                    <input class="hidden" name="Script.SERVICE_TYPE_CODE" id="serviceTypeCode">
                    <input class="hidden" name="Script.SERVICE_TYPE_NAME" id="serviceTypeName">
                    <input class="hidden" name="Script.SERVICE_ITEM1_NAME" id="serviceItem1Name">
                    <input class="hidden" name="Script.SERVICE_ITEM1_CODE" id="serviceItem1Code">
                    <input class="hidden" name="Script.SERVICE_ITEM2_CODE" id="serviceItem2Code">
                    <input class="form-control input-sm width-70" data-rules="required" name="Script.SERVICE_ITEM2_NAME"
                           id="serviceRequireShow" onclick="scriptConf.showSerMenu(this,'serMenuContent')"/>
                </td>
            </tr>
            <tr>
                <td width="60px">补偿类型</td>
                <td>
                    <select name="Script.COMPENSATE_TYPE" data-rules="required" class="form-control input-sm"
                            data-cust-context-path="/yq_common"
                            data-cust-mars="dict.getDictList('PEAKEND_COMPENSATE_TYPE')" data-mars-top="true">
                        <option value="">请选择</option>
                    </select>
                </td>
                <td width="60px">责任方</td>
                <td>
                    <select name="Script.RESPONSIBLE" data-rules="required" class="form-control input-sm"
                            data-cust-context-path="/yq_common"
                            data-cust-mars="dict.getDictList('PEAK_END_RESPONSIBLE')" data-mars-top="true">
                        <option value="">请选择</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td width="60px">场景类型</td>
                <td>
                    <select name="Script.SCENE_TYPE" data-rules="required" class="form-control input-sm"
                            data-cust-context-path="/yq_common"
                            data-cust-mars="dict.getDictList('SCENE_TYPE_VALUE')" data-mars-top="true">
                        <option value="">请选择</option>
                    </select>
                </td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>备注</td>
                <td colspan="3">
                    <input class="form-control input-sm" name="Script.REMARKS" id="remarks">
                </td>
            </tr>
            <tr>
                <td>是否允许现金补偿</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="Script.ALLOW_CASH" value="Y">是</label>
                    <label class="radio-inline"><input type="radio" name="Script.ALLOW_CASH" value="N" checked="checked">否</label>
                </td>
                <td>是否全员现金补偿</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="Script.ALLOW_ALL_CASH" value="Y">是</label>
                    <label class="radio-inline"><input type="radio" name="Script.ALLOW_ALL_CASH" value="N" checked="checked">否</label>
                </td>
            </tr>
            <tr>
                <td>是否允许黄金会员补偿</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="Script.IS_GOLD_MEMBER" value="Y">是</label>
                    <label class="radio-inline"><input type="radio" name="Script.IS_GOLD_MEMBER" value="N" checked="checked">否</label>
                </td>
                <td>是否允许实物补偿</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="Script.IS_PHY_COMP" value="Y">是</label>
                    <label class="radio-inline"><input type="radio" name="Script.IS_PHY_COMP" value="N" checked="checked">否</label>
                </td>
            </tr>
            <tr>
                <td>是否允许企微补偿</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="Script.IS_WX_ENTER" value="Y">是</label>
                    <label class="radio-inline"><input type="radio" name="Script.IS_WX_ENTER" value="N" checked="checked">否</label>
                </td>
                <td>是否允许延保卡补偿</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="Script.IS_EXT_WARRANTY" value="Y">是</label>
                    <label class="radio-inline"><input type="radio" name="Script.IS_EXT_WARRANTY" value="N" checked="checked">否</label>
                </td>
            </tr>
            <tr>
                <td>是否启用</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="Script.STATUS" value="Y" checked="checked">启用</label>
                    <label class="radio-inline"><input type="radio" name="Script.STATUS" value="N">禁用</label>
                </td>
                <%--<td>是否允许现金补偿</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="Script.ALLOW_CASH" value="Y">是</label>
                    <label class="radio-inline"><input type="radio" name="Script.ALLOW_CASH" value="N" checked="checked">否</label>
                </td>--%>
            </tr>
            <tr>
                <td width="150px">抱怨最小补偿金额</td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="number" data-rules="digits"
                               onblur="scriptConf.checkNumber('minByCompensate','maxByCompensate')" id="minByCompensate"
                               name="Script.MIN_BY_COMPENSATE" class="form-control input-sm">
                    </div>
                    <span id="minByCompensateTip" style="color:#a94442"></span>
                </td>
                <td>抱怨最大补偿金额</td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="number" data-rules="digits"
                               onblur="scriptConf.checkNumber('minByCompensate','maxByCompensate')" id="maxByCompensate"
                               name="Script.MAX_BY_COMPENSATE" class="form-control input-sm">
                    </div>
                </td>
            </tr>
            <tr>
                <td width="150px">投诉最小补偿金额</td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="number" id="minTsCompensate" data-rules="digits"
                               onblur="scriptConf.checkNumber('minTsCompensate','maxTsCompensate')"
                               name="Script.MIN_TS_COMPENSATE" class="form-control input-sm">
                    </div>
                    <span id="minTsCompensateTip" style="color:#a94442"></span>
                </td>
                <td>投诉最大补偿金额</td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="number" id="maxTsCompensate" data-rules="digits"
                               onblur="scriptConf.checkNumber('minTsCompensate','maxTsCompensate')"
                               name="Script.MAX_TS_COMPENSATE" class="form-control input-sm">
                    </div>
                </td>
            </tr>
            <tr>
                <td width="150px">扬言曝光最小补偿金额</td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="number" id="minYybgCompensate" data-rules="digits"
                               onblur="scriptConf.checkNumber('minYybgCompensate','maxYybgCompensate')"
                               name="Script.MIN_YYBG_COMPENSATE" class="form-control input-sm">
                    </div>
                    <span id="minYybgCompensateTip" style="color:#a94442"></span>
                </td>
                <td>扬言曝光最大补偿金额</td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="number" id="maxYybgCompensate" data-rules="digits"
                               onblur="scriptConf.checkNumber('minYybgCompensate','maxYybgCompensate')"
                               name="Script.MAX_YYBG_COMPENSATE" class="form-control input-sm">
                    </div>
                </td>
            </tr>
            <tr>
                <td>脚本内容<br>
                    <span id="wordsLength1" style="right:5px; bottom:3vh;font-size:12px;  color:#BDCADA">0/1000</span>
                </td>
                <td colspan="3">
                    <script id="editor" type="text/plain" style="width:100%;height:300px;"></script>
                    <input type="hidden" name="Script.GUIDED_SPEECH" id="guidedSpeechContent" />
                </td>
            </tr>
            <tr>
                <td>现金补偿脚本<br>
                    <span id="wordsLength4" style="right:5px; bottom:3vh;font-size:12px;  color:#BDCADA">0/1000</span>
                </td>
                <td colspan="3">
                    <script id="cashEditor" type="text/plain" style="width:100%;height:300px;"></script>
                    <input type="hidden" name="Script.CASH_GUIDED_SPEECH" id="cashGuidedSpeechContent" />
                </td>
            </tr>
            <tr>
                <td>实物补偿脚本<br>
                    <span id="wordsLength5" style="right:5px; bottom:3vh;font-size:12px;  color:#BDCADA">0/1000</span>
                </td>
                <td colspan="3">
                    <script id="giftEditor" type="text/plain" style="width:100%;height:300px;"></script>
                    <input type="hidden" name="Script.GIFT_GUIDED_SPEECH" id="giftGuidedSpeechContent" />
                </td>
            </tr>
            <tr>
                <td>重复提醒内容<br>
                    <span id="wordsLength2" style="right:5px; bottom:3vh;font-size:12px; color:#BDCADA">0/1000</span>
                </td>

                <td colspan="3"><textarea name="Script.REPEAT_CONTENT" maxlength="1000" data-rules="required"
                                          placeholder="" onkeyup="setLength(this,1000,'wordsLength2');"
                                          class="form-control input-sm textarea-style" rows="3"></textarea>

                </td>
            </tr>
            <tr>
                <td>短信内容<br>
                    <span id="wordsLength3" style="right:5px; bottom:3vh;font-size:12px; color:#BDCADA">0/1000</span>
                </td>

                <td colspan="3"><textarea name="Script.SMS_CONTENT" maxlength="1000" data-rules="required"
                                          placeholder="" onkeyup="setLength(this,1000,'wordsLength3');"
                                          class="form-control input-sm textarea-style" rows="3"></textarea>

                </td>
            </tr>
            </tbody>
        </table>
        <div class="layer-foot text-c" style="position: fixed;z-index: 999;">
            <button class="btn btn-sm btn-primary" type="button" onclick="scriptConf.ajaxSubmitForm()">保存</button>
            <button class="btn btn-sm btn-default ml-20" type="button" onclick="popup.layerClose()">取消</button>
        </div>
        <div id="prodMenuContent" class="menuContent">
            <input type="hidden" id="proCodeTreeHidden" data-context-path="/neworder" data-mars="comm.productCode"/>
            <ul id="proCodeTree" class="ztree" style="margin-top:0; width:100%; height:auto;"></ul>
        </div>
        <div id="serMenuContent" class="menuContent">
            <input type="hidden" id="serviceRequireTreeHidden" data-context-path="/neworder"
                   data-mars="comm.serviceRequire"/>
            <ul id="serviceRequireTree" class="ztree" style="margin-top:0; width:100%; height:auto;"></ul>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css"/>
    <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
    <script type="text/javascript" src="/sentiment/static/js/ueditor/ueditor.config.js"></script>
    <script type="text/javascript" src="/sentiment/static/js/ueditor/ueditor.all.js"> </script>
    <script type="text/javascript">
        jQuery.namespace("scriptConf")
        var prodSetting;
        var serSetting;
        var prodData;
        var serData;
        var ue; // 定义富文本编辑器变量
        var cashUe; // 定义现金补偿脚本编辑器变量
        var giftUe; // 定义实物补偿脚本编辑器变量
        $(function () {
            // 初始化富文本编辑器
            ue = UE.getEditor('editor', {
                initialFrameHeight: 200,
                maximumWords: 1000,
                wordCountMsg: '{#count}/1000',
                // wordCount: true,
                autoHeightEnabled: false
            });
            
            // 初始化现金补偿脚本编辑器
            cashUe = UE.getEditor('cashEditor', {
                initialFrameHeight: 200,
                maximumWords: 1000,
                wordCountMsg: '{#count}/1000',
                autoHeightEnabled: false
            });
            
            // 初始化实物补偿脚本编辑器
            giftUe = UE.getEditor('giftEditor', {
                initialFrameHeight: 200,
                maximumWords: 1000,
                wordCountMsg: '{#count}/1000',
                autoHeightEnabled: false
            });
            
            // 监听编辑器内容变化，更新字数统计
            ue.addListener('contentchange', function() {
                var contentLength = ue.getContentLength(true);
                $("#wordsLength1").text(contentLength + "/1000");
            });
            
            // 监听现金补偿脚本编辑器内容变化，更新字数统计
            cashUe.addListener('contentchange', function() {
                var contentLength = cashUe.getContentLength(true);
                $("#wordsLength4").text(contentLength + "/1000");
            });
            
            // 监听实物补偿脚本编辑器内容变化，更新字数统计
            giftUe.addListener('contentchange', function() {
                var contentLength = giftUe.getContentLength(true);
                $("#wordsLength5").text(contentLength + "/1000");
            });
            
            $("#editForm").render({
                success: function (result) {
                    var result = result["scriptDao.getScriptDetail"];
                    if (result && result.data.BRAND_CODE) {
                        setTimeout(function () {
                            $("#brandCode").val(result.data.BRAND_CODE);
                        }, 1000);
                    }
                    
                    // 如果有脚本内容，在编辑器准备好后设置内容
                    if (result && result.data.GUIDED_SPEECH) {
                        setTimeout(function () {
                            ue.setContent(result.data.GUIDED_SPEECH);
                            // 更新字数统计
                            var contentLength = ue.getContentLength(true);
                            $("#wordsLength1").text(contentLength + "/1000");
                        }, 1000);
                    }
                    
                    // 如果有现金补偿脚本内容，在编辑器准备好后设置内容
                    if (result && result.data.CASH_GUIDED_SPEECH) {
                        setTimeout(function () {
                            cashUe.setContent(result.data.CASH_GUIDED_SPEECH);
                            // 更新字数统计
                            var contentLength = cashUe.getContentLength(true);
                            $("#wordsLength4").text(contentLength + "/1000");
                        }, 1000);
                    }
                    
                    // 如果有实物补偿脚本内容，在编辑器准备好后设置内容
                    if (result && result.data.GIFT_GUIDED_SPEECH) {
                        setTimeout(function () {
                            giftUe.setContent(result.data.GIFT_GUIDED_SPEECH);
                            // 更新字数统计
                            var contentLength = giftUe.getContentLength(true);
                            $("#wordsLength5").text(contentLength + "/1000");
                        }, 1000);
                    }
                }
            });
        });
        $('#brandCode').render();
        $('#proCodeTreeHidden').render({
            success: function (result) {
                if (result["comm.productCode"]) {
                    prodData = result["comm.productCode"].data;
                }
                $.fn.zTree.init($("#proCodeTree"), prodSetting, prodData);
                $("#proCodeTreeHidden").val('');
            }
        });
        $('#serviceRequireTreeHidden').render({
            success: function (result) {
                if (result["comm.serviceRequire"]) {
                    serData = result["comm.serviceRequire"].data;
                }
                $.fn.zTree.init($("#serviceRequireTree"), serSetting, serData);
                $("#serviceRequireTreeHidden").val('');
            }
        });
        scriptConf.ajaxSubmitForm = function () {
            if (!form.validate("#editForm")) {
                return;
            }
            
            // 获取富文本编辑器内容并设置到隐藏字段
            var editorContent = ue.getContent();
            $("#guidedSpeechContent").val(editorContent);
            
            // 获取现金补偿脚本编辑器内容并设置到隐藏字段
            var cashEditorContent = cashUe.getContent();
            $("#cashGuidedSpeechContent").val(cashEditorContent);
            
            // 获取实物补偿脚本编辑器内容并设置到隐藏字段
            var giftEditorContent = giftUe.getContent();
            $("#giftGuidedSpeechContent").val(giftEditorContent);
            
            var data = form.getJSONObject("editForm");
            ajax.remoteCall("${ctxPath}/servlet/script?action=scriptSave", data, function (result) {
                    if (result.state == 1) {
                        window.parent.layer.closeAll();
                        window.parent.layer.msg(result.msg, {icon: 1});
                        window.parent.scriptList.search()
                    } else {
                        layer.alert(result.msg, {icon: 5});
                    }
                }
            );
        }
        prodSetting = {
            data: prodData,
            callback: {
                onClick: zTreePcOnclick
            },
            async: {
                enable: true,
                type: "post",
                url: "/neworder/servlet/comm?query=proCode2Level",
                autoParam: ["id", "level"]
            }
        }
        serSetting = {
            data: serData,
            callback: {
                onClick: zTreeSrOnclick
            },
            async: {
                enable: true,
                type: "post",
                url: "/neworder/servlet/comm?query=serviceRequire2Level",
                autoParam: ["id", "level"]
            }
        }

        //品类下拉树的点击事件
        function zTreePcOnclick(event, treeId, treeNode) {
            if (treeNode.isParent) {
                var ztreeObj = $.fn.zTree.getZTreeObj(treeId);
                ztreeObj.expandNode(treeNode);
            } else {
                $("#prodCode").val(treeNode.id);
                $("#prodName").val(treeNode.name);
                $("#proCodeShow").val(treeNode.name);
                scriptConf.hideMenu('prodMenuContent');
            }
        }

        //服务请求的点击事件
        function zTreeSrOnclick(event, treeId, treeNode) {
            if (treeNode.isParent) {
                var ztreeObj = $.fn.zTree.getZTreeObj(treeId);
                ztreeObj.expandNode(treeNode);
            } else {
                var nodes = treeNode.getPath();
                var showName = "";
                for (var i in nodes) {
                    var node = nodes[i];
                    var name = node.name;
                    var id = node.id;
                    if (i == 0) {
                        $("#serviceTypeName").val(name);
                        $("#serviceTypeCode").val(id);
                    } else if (i == 1) {
                        $("#serviceItem1Name").val(name);
                        $("#serviceItem1Code").val(id);
                    } else {
                        //$("#serviceItem2Name").val(name);
                        $("#serviceItem2Code").val(id);
                        showName += name;
                    }
                }
                $("#serviceRequireShow").val(showName);
                scriptConf.hideMenu('serMenuContent');
            }
        }

        //显示菜单
        scriptConf.showProdMenu = function (obj, treeId) {
            var leftPx = $(obj).offset().left;
            var topPx = $(obj).offset().top;
            var heightPx = $(obj).height() + $(obj).innerHeight() / 2;
            $("#" + treeId).css({left: leftPx, top: topPx + heightPx}).slideDown("fast");
            if (obj.id == 'proCodeShow') {
                $("body").bind("mousedown", onBodyDownPc);
            } else {
                $("body").bind("mousedown", onBodyDownSt);
            }

        }
        //显示菜单
        scriptConf.showSerMenu = function (obj, treeId) {
            var leftPx = $(obj).offset().left;
            var topPx = $(obj).offset().top;
            var heightPx = $(obj).height() + $(obj).innerHeight() / 2;
            $("#" + treeId).css({left: leftPx, top: topPx + heightPx}).slideDown("fast");
            if (obj.id == 'serviceRequireShow') {
                $("body").bind("mousedown", onBodyDownPc);
            } else {
                $("body").bind("mousedown", onBodyDownPc);
            }

        }

        //隐藏菜单
        scriptConf.hideMenu = function (divId) {
            $("#" + divId).fadeOut("fast");
            if (divId == "prodMenuContent") {
                $("body").unbind("prodMenuContent", onBodyDownPc);
            }
            if (divId == "serMenuContent") {
                $("body").unbind("serMenuContent", onBodyDownPc);
            }
        }

        scriptConf.changeBrand = function (obj) {
            var brandName = $(obj).find(":selected").text();
            $("#brandName").val(brandName);
        }

        onBodyDownPc = function (event) {
            if (!(event.target.id == "prodMenuContent" || event.target.id == "proCodeShow" || $(event.target).parents("#prodMenuContent").length > 0)) {
                scriptConf.hideMenu('prodMenuContent');
            }
            if (!(event.target.id == "serMenuContent" || event.target.id == "serviceRequireShow" || $(event.target).parents("#serMenuContent").length > 0)) {
                scriptConf.hideMenu('serMenuContent');
            }
        }

        scriptConf.checkNumber = function (number1, number2) {
            var number1Value = $("#" + number1).val();
            var number2Value = $("#" + number2).val();
            if (number1Value && number2Value) {
                if (parseInt(number1Value) > parseInt(number2Value)) {
                    $("#" + number1 + "Tip").text("最小金额不能大于最大金额！");
                    $("#" + number1).parent().addClass("has-error");
                } else {
                    $("#" + number1 + "Tip").text("");
                    $("#" + number1).parent().removeClass("has-error");
                }
            } else {
                $("#" + number1 + "Tip").text("");
                $("#" + number1).parent().removeClass("has-error");
            }
        }
        scriptConf.productType = function () {
            var prodName = $("#proCodeShow").val();
            popup.layerShow({type: 2, title: '产品品类', area: ['860px', '650px'], offset: '20px', shadeClose: false},
                "/neworder/pages/access/product-type.jsp?ccOrgCode=false&orgCode=orgCode&brandCode=brandCode&brandName=brandName&prodCode=prodCode&prodName=proCodeShow" +
                "&productName=" + prodName + "&org=");
        }

        function setLength(obj, maxlength, id) {
            var num = obj.value.length;
            var leng = id;
            /*  if(num<0){
                 num=0;
             } */
            document.getElementById(leng).innerHTML = num + "/" + maxlength;
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
