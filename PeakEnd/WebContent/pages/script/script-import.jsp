<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>脚本导入</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form id="editFormImport" name="editFormImport" method="post" class="form-inline"  enctype="multipart/form-data">
				  <table class="table  table-vzebra mt-10" >
	                    <tbody>
			                 <tr>
			                      <td width="60px">Excel文件</td>
			                      <td>
			                        	 <input class="hidden" type="file" id="file" name=file accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-RevisitWdateel">
										 <button class="btn btn-xs btn-info" type="button" onclick="$('#file').click()">选择文件</button> 
			                        	 <a class="btn btn-sm btn-link" href="javascript:scriptImport.ajaxSubmitForm()">下载导入模板</a>
			                        </td>
		                     </tr>
		                   <tr>
								<td>文件名</td>
								<td><input id="photoCover" class="form-control input-sm" style="width:200px" type="text" readonly="readonly"></td>
							</tr>
	                    </tbody>
	                </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="scriptImport.upload()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="layer.closeAll();">关闭</button>
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	    requreLib.setplugs('wdate');
	    jQuery.namespace("scriptImport");
	    scriptImport.ajaxSubmitForm = function(){
	    	scriptImport.download(); 
		}
	    
	    $('input[id=file]').change(function() {
			var fileName = $(this).val();
			var splitStr = fileName.split('\\');
			var splitStrs = splitStr[splitStr.length-1];
			$('#photoCover').val(splitStrs); 
		});
	    
	    scriptImport.download=function(){
			location.href ="${ctxPath}/servlet/script?action=download";
		}
	    scriptImport.upload = function() {
	    	var photoCover=$("#photoCover").val();
			if(photoCover==""){
				layer.alert("请上传文件",{icon: 5});
				return;
			}
			$("#editFormImport").attr("enctype","multipart/form-data");
			var formData = new FormData($("#editFormImport")[0]); 
			$.ajax({  
		          url: '${ctxPath}/servlet/script?action=scriptDataImport',  
		          type: 'POST',  
		          data: formData,
		          async: true,cache: false,contentType: false,processData: false,  
		          success: function (result) {
		        	  if(result.state == 1){
		        		  $("#editFormImport").removeAttr("enctype");
			        		 /* alert(result.msg); */
			        		 layer.msg(result.msg,{icon: 1});
							 window.location.reload();
					   }else{
							/* layer.close(index); */
							layer.open({
								  type: 1,
								  skin: 'layui-layer-rim', //加上边框
								  area: ['650px', '550px'], //宽高
								  content: result.msg
								});
						}
		          }
		     }); 
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>