<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>脚本配置列表</title>
	<style>
		.menuContent {display:none;position: absolute;border:1px solid rgb(170,170,170);max-width: 220px; max-height: 350px;z-index:10;overflow: auto;background-color: #f4f4f4}
	    #scriptListData th{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
	    #scriptListData td{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
        .button_icon {
            position: absolute;
            right: 10px;
            top: 10px;
            z-index: 100;
            color: #0092D8;
            cursor: pointer;
        }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form name="scriptListForm" class="form-inline" id="scriptListForm" method="post" onsubmit="return false" autocomplete="off">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 脚本配置查询</h5>
	             		 </div>
	             		 <hr style="margin:5px -15px">
	             		 <input type="hidden" name = "realPageIndex" id="hid_pageIndexV" />
	             		 <div class="form-group">
							   <div class="input-group width-18">
								      <span class="input-group-addon" style="width:30%">品牌</span>	
									  <select name="brandCode" id="brandCode" data-mars-top="true" data-rules="required" class="form-control input-sm" data-context-path="/neworder" data-mars="comm.getBrandlist">
				                            <option value="">请选择</option>
				                      </select>
							   </div>
                               <div class="input-group width-18">
                                    <span class="input-group-addon" style="width:30%">主体</span>	
                                    <select name="ORG_CODE" id="ORG_CODE" data-mars-top="true" data-rules="required" class="form-control input-sm" data-context-path="/neworder" data-mars="common.sysCode('ORG_CODE')">
                                        <option value="">请选择</option>
                                    </select>
                                </div>
                                <div class="input-group width-18">
                                    <span class="input-group-addon" style="width:30%">品类</span>	
                                    <input class="hidden" name="prodCode" id="prodCode">
                                    <input class="form-control input-sm width-70" id ="proCodeShow" data-rules="required" onchange="cleanVal(this,'proCodeShow')" onclick="scriptList.showProdMenu(this,'prodMenuContent')" />
                             </div>
                             <div class="input-group width-18">
                                    <span class="input-group-addon" style="width:30%">服务请求</span>	
                                    <input class="hidden" name="serviceItem2Code" id="serviceItem2Code">
                                    <input class="form-control input-sm width-70" data-rules="required" id ="serviceRequireShow" onclick="scriptList.showSerMenu(this,'serMenuContent')" />
                             </div>
							   <div class="input-group width-36">
							    	  <span class="input-group-addon">场景类型</span>	
			                          <select name="sceneType" id="sceneType" class="form-control input-sm" 
			                          data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('SCENE_TYPE_VALUE')" data-mars-top="true" >
			                               <option value="">请选择</option>
			                          </select>
							   </div>
							   <div class="input-group width-36">
							    	  <span class="input-group-addon">补偿类型</span>	
			                          <select name="compensateType" data-rules="required" id="compensateType" class="form-control input-sm" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PEAKEND_COMPENSATE_TYPE')" data-mars-top="true" >
			                               <option value="">请选择</option>
			                          </select>
							   </div>
							    <div class="input-group width-36">
							    	  <span class="input-group-addon">状态</span>	
								        <select class="form-control input-sm width-70" name="status" id="status">
                                          <option value="" selected="selected">--请选择--</option>
                                          <option value="Y">启用</option>
                                          <option value="N">禁用</option>
                                      </select>
							   </div>
							 <div class="input-group width-36" style="display: none">
								 <span class="input-group-addon">是否</span>
								 <select class="form-control input-sm width-70" name="sf_yn" id="sf_yn" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('SF_YN')">
									 <option value="" selected="selected">--请选择--</option>
								 </select>
							 </div>
							   <div class="input-group width-36">
								        <span class="input-group-addon">责任方</span>	
			                          <select name="responsible" data-rules="required" id="responsible" class="form-control input-sm" 
			                          data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PEAK_END_RESPONSIBLE')" data-mars-top="true" >
			                               <option value="">请选择</option>
			                          </select>
							   </div>
						  </div>
						  <div class="form-group">
							   <div class="input-group input-group-sm pull-left mr-10">
									  <button type="button" class="btn btn-sm btn-success mr-10" onclick="scriptList.reset()"><span class="glyphicon glyphicon-repeat"></span> 重置</button>
							   </div>
                               <div class="input-group input-group-sm pull-right mr-10">
                                <button type="button" class="btn btn-sm btn-success" onclick="scriptList.remove()"> <span class="glyphicon glyphicon-remove"></span> 删除</button>
                                </div>
							   <div class="input-group input-group-sm pull-right mr-10">
									  <button type="button" class="btn btn-sm btn-success" onclick="scriptList.add()"> +新增</button>
							   </div>
							   <div class="input-group input-group-sm pull-right mr-10">
									  <button type="button" class="btn btn-sm btn-success" onclick="scriptList.importData()"><span class="glyphicon glyphicon-import"></span>导入</button>
							   </div>
							   <div class="input-group input-group-sm pull-right mr-10">
									  <button type="button" class="btn btn-sm btn-success" onclick="scriptList.exportScriptConfig()"><span class="glyphicon glyphicon-import"></span>导出</button>
							   </div>
						 	   <div class="input-group input-group-sm pull-right ml-10">
									  <button type="button" class="btn btn-sm btn-default" onclick="scriptList.search()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
						  </div>
             	    </div> 
	              	<div class="ibox-content ">
	              		<div class="row table-responsive" ><!-- style="max-height:700px;" -->
	              		  <table style="border: 0px solid #fff;" class="table table-bordered" id="scriptListData">
	              		  		<thead>
	                         	 <tr>
                                     <!-- <div>选择</div> -->
                                     
                                      <th class="text-c" style="padding-left: 10px;"><label class="checkbox checkbox-info"><input type="checkbox" id="checkAll" ><span></span></label></th>
	                         	      <th class="text-c" >序号</th>
	                         	      <th class="text-c" >主体</th>
	                         	      <th class="text-c" >品牌</th>
								      <th class="text-c" >品类</th>
								      <th class="text-c" >服务请求</th>
								      <th class="text-c" >服务请求大类</th>
								      <th class="text-c" >服务请求小类</th>
								      <th class="text-c" >场景类型</th>
								      <th class="text-c" >补偿类型</th>
								      <th class="text-c" >责任方</th>
								      <th class="text-c" style="width:300px;max-width:400px;" >话术脚本</th>
								      <th class="text-c" style="width:300px;max-width:400px;" >现金补偿脚本</th>
								      <th class="text-c" style="width:300px;max-width:400px;" >实物补偿脚本</th>
								      <th class="text-c" style="width:300px;max-width:400px;" >重复提醒脚本</th>
									  <th class="text-c" >是否允许现金补偿</th>
									  <th class="text-c" >是否全员现金补偿</th>
									  <th class="text-c" >状态</th>
								      <th class="text-c" >创建账号</th>
								      <th class="text-c" >创建时间</th>
								      <th class="text-c" >更新账号</th>
								      <th class="text-c" >更新时间</th>
								      <th class="text-c">操作</th>
		   						 </tr>
                             </thead>
							 <tbody id="dataList">
							 
							 </tbody>
							 <script id="list-template" type="text/x-jsrender">
												   {{for list}}
														<tr>
                                                            <td class="text-c"><label class="checkbox checkbox-info"><input type="checkbox" name="items" data-id="{{:ID}}" ><span></span></label></td>
			                             					 <td>{{:#index+1}}</td>
															  <td>{{sysCodeFUN:ORG_CODE 'ORG_CODE'}}</td>
															 <td class="text-c">{{:BRAND_NAME}}</td>
															 <td class="text-c">{{:PROD_NAME}}</td>
															 <td class="text-c">{{:SERVICE_TYPE_NAME}}</td>
															 <td class="text-c">{{:SERVICE_ITEM1_NAME}}</td>
															 <td class="text-c">{{:SERVICE_ITEM2_NAME}}</td>
															 <td>{{getText:SCENE_TYPE 'sceneType'}}</td>
															 <td>{{getText:COMPENSATE_TYPE 'compensateType'}}</td>
															 <td>{{getText:RESPONSIBLE 'responsible'}}</td>
															 <td class="text-c" style="width:300px;max-width:400px;" title="{{formatTitle:GUIDED_SPEECH}}">{{:GUIDED_SPEECH}}</td>
															 <td class="text-c" style="width:300px;max-width:400px;" title="{{formatTitle:CASH_GUIDED_SPEECH}}">{{:CASH_GUIDED_SPEECH}}</td>
															 <td class="text-c" style="width:300px;max-width:400px;" title="{{formatTitle:GIFT_GUIDED_SPEECH}}">{{:GIFT_GUIDED_SPEECH}}</td>
															 <td class="text-c" style="width:300px;max-width:400px;" ><p title="{{:REPEAT_CONTENT}}">{{:REPEAT_CONTENT}}</p></td>
															 <td class="text-c">{{getText:ALLOW_CASH 'sf_yn'}}</td>
															 <td class="text-c">{{getText:ALLOW_ALL_CASH 'sf_yn'}}</td>
															 <td class="text-c">{{getText:STATUS 'status'}}</td>
															 <td class="text-c">{{:CREATE_ACC}}</td>
															 <td class="text-c">{{:CREATE_TIME}}</td>
															 <td class="text-c">{{:UPDATE_ACC}}</td>
															 <td class="text-c">{{:UPDATE_TIME}}</td>
															 <td>
                                          						<a href="javascript:scriptList.edit('{{:ID}}')">编辑</a>&nbsp;
                                          						<a href="javascript:scriptList.del('{{:ID}}')">删除</a>
                                      						 </td>
														</tr>
												   {{/for}}
																		 
							</script>
	              		  </table>
	              		</div><!--  row table-responsive -->
	                    <div class="row paginate" id="page">
	                    		<jsp:include page="/pages/common/pagination.jsp">
	                    			<jsp:param value="10" name="pageSize"/>
	                    		</jsp:include>
	                    </div> 
	                 </div>
                </div>
                <div id="prodMenuContent" class="menuContent">
		        	<input type="hidden" id = "proCodeTreeHidden"  data-context-path="/neworder"  data-mars = "comm.productCode" />
		         	<ul id="proCodeTree" class="ztree" style="margin-top:0; width:100%; height:auto;"></ul>
			    </div>
			    <div id="serMenuContent" class="menuContent">
			    		<input type="hidden" id = "serviceRequireTreeHidden" data-context-path="/neworder"  data-mars = "comm.serviceRequire" />
			         	<ul id="serviceRequireTree" class="ztree" style="margin-top:0; width:100%; height:auto;"></ul>
			    </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type ="text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css" />
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type = "text/javascript">
		jQuery.namespace("scriptList");
	    requreLib.setplugs("wdate");
	    var prodSetting;
	    var serSetting;
	    var prodData;
	    var serData;
        var orgcode;

        window.setProdInfo = function (orgCode, brandCode, brandName, prodCode, prodName) {
            console.log(orgCode, brandCode, brandName, prodCode, prodName)
            orgcode = orgCode
            $('#prodCode').val(prodCode)
            $('#proCodeShow').val(prodName)
            $('#ORG_CODE').val(orgcode)
        }

        window.orderfunc = {
            setArrVueValue: function(key, value) {
                console.log(key, value)
                var name = '', code = ''
                value.forEach(item => {
                    if (item.key == 'contactOrderSerItem2Name') {
                        name = item.value
                    }
                    if (item.key == 'contactOrderSerItem2Code') {
                        code = item.value
                    }
                })
                $("#serviceItem2Code").val(code);
		    	$("#serviceRequireShow").val(name);
            },
            serciveQueryCallback: function() {

            }
        }

	    $(function(){
	    	scriptList.init();
	    	$("#scriptListForm").render({success : function(result){
    			$("#scriptListData").attr("data-mars","scriptDao.scriptList");
				scriptList.search();
	    	}});
	    });
	    $('#brandCode').render();
	    $('#proCodeTreeHidden').render({
			success: function(result) {
				if(result["comm.productCode"]){
					prodData = result["comm.productCode"].data;
    			}
    			$.fn.zTree.init($("#proCodeTree"), prodSetting,prodData);
    			$("#proCodeTreeHidden").val('');
			}
		});
	    $('#serviceRequireTreeHidden').render({
			success: function(result) {
				if(result["comm.serviceRequire"]){
					serData = result["comm.serviceRequire"].data;
    			}
    			$.fn.zTree.init($("#serviceRequireTree"), serSetting,serData);
    			$("#serviceRequireTreeHidden").val('');
			}
		});
        $("#checkAll").click(function() { 
			if (this.checked){  
		        $("input[name='items']:checkbox").each(function(){ 
		              $(this).prop("checked", true);  
		        });
		    } else {   
		        $("input[name='items']:checkbox").each(function() {   
		              $(this).prop("checked", false);  
		        });
		    }  
		});
	    prodSetting ={
	    		data : prodData,
	    		callback :{
	    			onClick : zTreePcOnclick
	    		},
	    		async:{
	    	    	enable:true,
	    	    	type:"post",
	    	    	url:"/neworder/servlet/comm?query=proCode2Level",
	    	    	autoParam:["id","level"]
	    	    }
	    }
	    serSetting = {
	    		data : serData,
	    		callback :{
	    			onClick : zTreeSrOnclick
	    		},
    		    async:{
	    	    	enable:true,
	    	    	type:"post",
	    	    	url:"/neworder/servlet/comm?query=serviceRequire2Level",
	    	    	autoParam:["id","level"]
	    	    }
	    }
		$.views.converters("formatTitle", function(text) {
			if(!text) return "";
			// 去除HTML标签
			return text.replace(/<[^>]+>/g, '');
		});
	  //品类下拉树的点击事件
		function zTreePcOnclick(event,treeId,treeNode){
			if(treeNode.isParent){ 
				var ztreeObj = $.fn.zTree.getZTreeObj(treeId); 
				ztreeObj.expandNode(treeNode);
		    }else{ 
		    	$("#prodCode").val(treeNode.id);
		    	$("#proCodeShow").val(treeNode.name);
		    	scriptList.hideMenu('prodMenuContent');
		    }
		}
		
		//服务请求的点击事件
		function zTreeSrOnclick(event,treeId,treeNode){
			if(treeNode.isParent){ 
				var ztreeObj = $.fn.zTree.getZTreeObj(treeId); 
				ztreeObj.expandNode(treeNode);
		    }else{
	    		$("#serviceItem2Code").val(treeNode.id);
		    	$("#serviceRequireShow").val(treeNode.name);
			    scriptList.hideMenu('serMenuContent');
		    }
		}
		//显示菜单
		scriptList.showProdMenu = function(obj,treeId) {

            // popup.layerShow({type: 2, title: '产品品类', area: ['860px', '700px'], offset: '20px', shadeClose: false},
            //     "/neworder/pages/access/product-type.jsp?ccOrgCode=false&orgCode=orgCode&brandCode=brandCode&brandName=brandName&prodCode=prodCode&prodName=prodName&callback=5&productName="+$('#proCodeShow').val());
            // return;

			var leftPx = $(obj).offset().left;
			var topPx = $(obj).offset().top;
			var heightPx = $(obj).height()+$(obj).innerHeight()/2;
		    $("#"+treeId).css({ left: leftPx, top: topPx+heightPx }).slideDown("fast");
		    if(obj.id =='proCodeShow'){
		    	$("body").bind("mousedown", onBodyDownPc);
		    }else{
		    	$("body").bind("mousedown", onBodyDownSt);
		    }
		    
		}
		//显示菜单
		scriptList.showSerMenu = function(obj,treeId) {
            // if (!$('#prodCode').val()) {
            //     layer.alert("请选择品类",{icon: 5});
            //     return
            // }
            // popup.layerShow({type:2,title:'服务请求',area:['860px','700px'],offset:'20px',shadeClose:false},"/neworder/pages/access/service-request.html?orgCode="+orgcode+"&prodCode="+$('#prodCode').val()+"&serviceRequireItemCode=&callback=1&isExistUrge=false&text=");

            // return
			var leftPx = $(obj).offset().left;
			var topPx = $(obj).offset().top;
			var heightPx = $(obj).height()+$(obj).innerHeight()/2;
		    $("#"+treeId).css({ left: leftPx, top: topPx+heightPx }).slideDown("fast");
		    if(obj.id =='serviceRequireShow'){
		    	$("body").bind("mousedown", onBodyDownPc);
		    }else{
		    	$("body").bind("mousedown", onBodyDownPc);
		    }
		    
		}
		
		//隐藏菜单
		scriptList.hideMenu = function(divId) {
		    $("#"+divId).fadeOut("fast");
		    if(divId == "prodMenuContent"){
		    	$("body").unbind("prodMenuContent", onBodyDownPc);
		    }
			if(divId == "serMenuContent"){
		    	$("body").unbind("serMenuContent", onBodyDownPc);
		    }
		}
		
		onBodyDownPc = function(event) {
    	    if (!( event.target.id == "prodMenuContent" || event.target.id == "proCodeShow" || $(event.target).parents("#prodMenuContent").length > 0)) {
    	    	scriptList.hideMenu('prodMenuContent');
    	  	}
    	    if (!( event.target.id == "serMenuContent" || event.target.id == "serviceRequireShow" || $(event.target).parents("#serMenuContent").length > 0)) {
    	    	scriptList.hideMenu('serMenuContent');
    	  	}
    	}
	    scriptList.init = function() {
    		$("#createTimeStar").val(getTodayDate(-7)+" 00:00:00");
	    	$("#createTimeEnd").val(getTodayDate()+" 23:59:59");
	    }
	    scriptList.search = function(){
    		$("#scriptListForm").searchData();
	    }
	    
	    /*重置*/
		scriptList.reset = function() {
			document.scriptListForm.reset();
			$("#createTimeStar").val(getTodayDate(-7)+" 00:00:00");
	    	$("#createTimeEnd").val(getTodayDate()+" 23:59:59");
		}

        scriptList.remove = function() {
            var ids = $("#scriptListForm").find("input[name='items']:checked")
            if (!ids.length) return;
            layer.confirm('是否删除所选数据', {
                btn : [ '确定', '取消' ]//按钮
            }, function(index) {
                var arr = []
                for (var i = 0; i < ids.length; i++) {
                    arr.push($(ids[i]).attr("data-id"))
                }
                var data={ids: arr};
                console.log(data,"data");
                ajax.remoteCall("/PeakEnd/servlet/script?action=scritpDelete",data,function(result) { 
                    if(result.state == 1){
                        layer.msg(result.data,{icon: 1});
                        scriptList.search()
                    }else{
                        layer.msg(result.msg,{icon: 5});
                    }
                });
            })
        }
	    
		scriptList.add = function(){
		    popup.layerShow({type:2,title:'新增维护',area:['930px','950px'],offset:'60px'},"${ctxPath}/pages/script/script-edit.jsp");
		}
		
		scriptList.edit = function(id){
		    popup.layerShow({type:2,title:'编辑维护',area:['930px','900px'],offset:'60px'},"${ctxPath}/pages/script/script-edit.jsp",{ID:id});
		}

		scriptList.importData = function(){
			 popup.layerShow({type:1,title:"导入",offset:'20px',area:['450px','220px']},"${ctxPath}/pages/script/script-import.jsp",null);
		}
		scriptList.exportScriptConfig = function(){
			location.href = "${ctxPath}/servlet/script?action=exportScriptConfig&"
				+ $("#scriptListForm").serialize();
		}
		scriptList.del=function(id){
			var ids=[id];
			if(confirm("确认要删除吗？")){
				ajax.remoteCall("${ctxPath}/servlet/script?action=scritpDelete",{ids:ids},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1});
						scriptList.search();
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			} 
		}
		
		function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$(".moreSearch").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon glyphicon-menu-up")
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
