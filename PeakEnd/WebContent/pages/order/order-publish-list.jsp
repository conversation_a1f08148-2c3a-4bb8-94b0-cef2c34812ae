<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>回访发布记录</title>
		<style>
			.pagination pagination-sm pageNumV{float:right}
		</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form autocomplete="off" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false">
            	<div class="ibox">
            		<div class="ibox-title clearfix">
					 <div class="form-group">
             		       <h5><span class="glyphicon glyphicon-list"></span>回访发布记录</h5>
             		 </div>
             		 <hr style="margin:5px -15px">
             			<div class="form-group">
             				<div class="input-group input-group-sm" style="width: 200px;">
								<span class="input-group-addon" style="width: 80px;">发布人</span>	
								<input type="text" name="PUB_ACC" class="form-control input-sm" >
							</div>
							<div class="input-group input-group-sm">
							      <span class="input-group-addon" style="width:80px">折算类型</span>	
								  <select name="CONVERSION_TYPE" class="form-control input-sm" data-mars="common.getConvert">
		                          	<option value="">请选择</option>
		                          </select>
						    </div>
							<div class="input-group input-group-sm">
			          		    <span class="input-group-addon" style="width: 80px;">发布时间</span>	
								<input type="text" name="PUB_START_TIME" id="pubStart" class="form-control input-sm Wdate" value="" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">
								<span class="input-group-addon" >-</span>	
								<input type="text" name="PUB_END_TIME" id="pubEnd" class="form-control input-sm Wdate" value=""   onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">									  
							</div>
					  </div>
					 <div class="form-group">
					 		<div class="input-group input-group-sm">
								<button type="button" class="btn btn-sm btn-success" onclick="pubList.reset()">重置</button>
							</div>
						    <div class="input-group input-group-sm pull-right ml-10">
								<button type="button" class="btn btn-sm btn-default" onclick="pubList.reload()"><span class="glyphicon glyphicon-search"></span>搜索</button>
						   </div>
             		 </div>
            	    </div>  
            	  	<div class="ibox-content">
              		<div class="row table-responsive">
	           	     	<table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" >
                            	<thead>
                         		 <tr>
	                         	      <th class="text-c">发布时间</th>
	                         	      <th class="text-c">发布人</th>
								      <th class="text-c">发布类型</th>
								      <th class="text-c">数据来源</th>
								      <th class="text-c">发布数</th>
								      <th class="text-c">折算类型</th>
								      <th class="text-c">申请人数</th>
								      <th class="text-c">操作</th>
		   						 </tr>
                           	 </thead>
                            <tbody id="dataList">
                            </tbody>
                          </table>
                       	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
                                      		<td>{{:PUBLISH_TIME}}</td>
                                     		<td>{{:PUBLISH_ACC}}</td>
                                      		<td>{{dictFUN:PUBLISH_TYPE 'PEAK_END_PUBLISH_TYPE'}}</td>
                                      		<td>{{dictFUN:DATA_SOURCES 'PEAKEND_DATA_SOURCE'}}</td>
                                      		<td>{{:PUBLISH_NUM}}</td>
                                      		<td>{{CONVERSION_TYPE:CONVERSION_TYPE}}</td>
                                     		<td>{{:APPLY_NUM}}</td>
											<td><a href="javascript:pubList.open('{{:C_NO_PUBLISH_ID}}','{{:PUBLISH_TIME}}')">查看</a></td>
									    </tr>
								   {{/for}}					         
						 	</script>
              		 </div> 
              		 <div class="row paginate" id="page">
                     		<jsp:include page="/pages/common/pagination.jsp">
                     			<jsp:param value="10" name="pageSize"/>
                     		</jsp:include>
                     </div> 
        	</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
		<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript">
		jQuery.namespace("pubList");
		requreLib.setplugs('wdate')//加载时间控件
		var conversion_type;
		$(function(){
			var startDate = getTodayDate(-7);
   	    	var endDate = getTodayDate();
	    	$("#pubStart").val(startDate+" 00:00:00");
	    	$("#pubEnd").val(endDate+" 23:59:59");
	    	$("#searchForm").render({success : function(result){
	    		//折算类型
	    		if(result["common.getConvert"]){
    				conversion_type = result["common.getConvert"].data;
    			}
	    		pubList.reload();
    		}});
	    });
		$.views.converters("CONVERSION_TYPE", function(val) {
			var req = conversion_type;
			if (typeof (req) == "undefined") {
				return val;
			} else {
				return req[val];
			}
		});
		pubList.open = function(id,pubTime){
			popup.layerShow({
				type:2,
				title:'详情',
				shadeClose:false,
				scrollbar: false,
				area:['750px','670px'],
				offset:'20px'},"${ctxPath}/pages/order/order-apply-detail.jsp",{ID:id,TIME:pubTime});
		}
		pubList.reload = function(){
			$("#tableHead").data("mars","order.publishList");
			$("#searchForm").searchData();
		}
		pubList.reset = function(){
			document.searchForm.reset(); 
			var startDate = getTodayDate(-7);
   	    	var endDate = getTodayDate();
	    	$("#pubStart").val(startDate+" 00:00:00");
	    	$("#pubEnd").val(endDate+" 23:59:59");
	    	pubList.reload();
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>