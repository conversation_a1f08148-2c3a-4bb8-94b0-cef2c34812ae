<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>补偿结果查询</title>
	<style>
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form autocomplete="off" name="searchForm"class="form-inline" id="searchForm">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
					<h5>
						<span class="glyphicon glyphicon-list"></span> 补偿结果查询
					</h5>
				</div>
				<hr style="margin: 5px -15px">
 				<div class="form-group">
					   <div class="input-group input-group-sm" style="width: 200px;">
						      <span class="input-group-addon" style="width: 80px;">档案类型</span>	
							  <select class="form-control input-sm" id="list_archives_type" name="ARCHIVES_TYPE"  data-mars="common.sysCode('WOM_ARCHIVE_TYPE')" >
                                  <option value="">请选择</option>
                              </select>
					   </div>
					   <div class="input-group input-group-sm" style="width: 200px;">
						      <span class="input-group-addon" style="width: 80px;">受访人电话</span>	
							  <input type="text" name="CUSTOMER_MOBILEPHONE1" class="form-control input-sm" >
					   </div>
					   <div class="input-group input-group-sm" style="width: 200px;">
						      <span class="input-group-addon" style="width: 80px;">服务单号</span>	
							  <input type="text" name="SERVICE_ORDER_NO" class="form-control input-sm" >
					   </div>
					   <div class="input-group input-group-sm" style="width: 200px;">
						      <span class="input-group-addon" style="width: 80px;">回访结果</span>	
						      <select name="RESULT" id="VISIT_RESULT" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PEAK_END_RESULT')"  class="form-control input-sm">
	                                <option value="">请选择</option>
	                          </select>							  
	                   </div>
	                   <div class="input-group input-group-sm" style="width: 200px;">
						      <span class="input-group-addon" style="width: 80px;">回访坐席</span>	
							  <input type="text" name="RESULT_ACC" class="form-control input-sm" >
					   </div>
					   <div class="input-group input-group-sm pull-right ml-10">
						 	<button type="button" id="moreBtn" class="btn btn-sm btn-link" onclick="toggleMore()" title="展开"> 展开 <span class="glyphicon glyphicon glyphicon-menu-up"></span>&nbsp;</button>
					   </div>
				</div>
				<div class="form-group moreSearch" style="display:none">
				   <div class="input-group input-group-sm" style="width: 200px;">
					      <span class="input-group-addon" style="width: 80px;">档案编码</span>	
					      <input type="text" name="ARCHIVES_NO" class="form-control input-sm" >
				   </div>
				   <div class="input-group input-group-sm" style="width: 200px;">
					      <span class="input-group-addon" style="width:80px">品牌</span>	
					      <select name="BRAND_CODE" id="brandCode" data-mars-top="true" class="form-control input-sm" data-cust-context-path="/neworder" data-cust-mars="comm.getBrandlist">
                                <option value="">请选择</option>
                          </select>
				   </div> 
				   <div class="input-group input-group-sm" style="width: 200px;">
					      <span class="input-group-addon" style="width: 80px;">数据来源</span>	
					      <select name="DATA_SOURCES"  data-mars="common.getDict('PEAKEND_DATA_SOURCE')" class="form-control input-sm">
                                <option value="">请选择</option>
                            </select>
				   </div>
				   <div class="input-group input-group-sm" style="width: 200px;">
					      <span class="input-group-addon" style="width:80px">是否结算</span>	
						  <select name="SETTLEMENT"  data-rules="required" class="form-control input-sm" data-mars="comm.getBrandlist">
                                <option value="">请选择</option>
                          </select>
				   </div>
				   <div class="input-group input-group-sm" style="width: 200px;">
					      <span class="input-group-addon" style="width:80px">责任方</span>	
					      <select name="RESPONSIBLE" id="RESPONSIBLE" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PEAK_END_RESPONSIBLE')"  class="form-control input-sm">
                                <option value="">请选择</option>
                          </select>
				   </div>
			    </div>
				<div class="form-group moreSearch" style="display:none">
				   <div class="input-group input-group-sm" >
         		              <span class="input-group-addon" style="width: 80px;">回访时间</span>	
						  <input type="text" name="START_VISIT_TIME" id="start_visit_time"  class="form-control input-sm Wdate"  onFocus="WdatePicker({onpicked:function(){this.blur();maxTime()},dateFmt:'yyyy-MM-dd HH:mm:ss'})">
						  <span class="input-group-addon" >-</span>	
						  <input type="text" name="END_VISIT_TIME" id="end_visit_time"  class="form-control input-sm Wdate"  onclick="WdatePicker({minDate:'#F{$dp.$D(\'start_visit_time\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">									  
				   </div>
				   <div class="input-group input-group-sm" style="width: 200px;">
					      <span class="input-group-addon" style="width: 80px;">录音编号</span>	
					      <input type="text" name="CALL_ID" class="form-control input-sm" >
				   </div>
			    </div>
				<div class="form-group">
					<div class="input-group input-group-sm pull-right">
						<button type="button" class="btn btn-sm btn-default" onclick="reset()">重置</button>
						<button type="button" class="btn btn-sm btn-default ml-10" onclick="reload()">搜索</button>
						<button type="button" class="btn btn-sm btn-success ml-10" onclick="revisit.exportExcel()">导出</button>
					</div>
				</div>
			</div>
			<div class="ibox-content">
				<div class="row table-responsive">
					<table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" data-mars="order.resultList">
						<thead>
							<tr>
								<th class="text-c">序号</th>
								<th class="text-c">操作</th>
								<th class="text-c">产品主体</th>
								<th class="text-c">产品品类</th>
								<th class="text-c">用户姓名</th>
								<th class="text-c">区号</th>
								<th class="text-c">用户号码</th>
								<th class="text-c">用户地址</th>
								<th class="text-c" style="width:50px;max-width:50px;min-width:50px">查看</th>
								<th class="text-c">回访结果</th>
								<th class="text-c">服务单号</th>
								<th class="text-c">是否结算</th>
								<th class="text-c">档案编码</th>
								<th class="text-c">责任方</th>
								<th class="text-c">回访坐席</th>
								<th class="text-c">回访时间</th>
								<th class="text-c">资料来源</th>
							</tr>
						</thead>
						<tbody id="dataList"></tbody>
					</table>
					<script id="list-template" type="text/x-jsrender">
						{{for list}}
							<tr>
			                    <td>{{:#index+1}}</td>
								<td><a href="javascript:revisit.detail('{{:RESULT_ID}}')">查看</a></td>
							    <td>{{sysCodeFUN:ORG_CODE "ORG_CODE"}}</td>
                                <td>{{:PROD_NAME}}</td>
                                <td>{{call:CUSTOMER_NAME CUSTOMER_NAME_data #index+1 'name' fn='getData2'}}</td>
                                <td>{{:AREA_NUM}}</td>
                                <td>{{call:CUSTOMER_MOBILEPHONE1 CUSTOMER_MOBILEPHONE1_data #index+1 'phone' fn='getData2'}}</td>
                                <td>{{call:CUSTOMER_ADDRESS CUSTOMER_ADDRESS_data #index+1 'addr' fn='getData2'}}</td>
                                <td title="査看"class="text-c">
												<span onclick="showDetail('{{:#index+1}}','{{:CUSTOMER_NAME_data}}','{{:CUSTOMER_MOBILEPHONE1_data}}','{{:CUSTOMER_ADDRESS_data}}')" class="glyphicon glyphicon-eye-open"
												id="show{{:#index+1}}"
												style="color:rgb(255,140,60);">
												</span>
											</td>
                                <td>{{dictFUN:VISIT_RESULT 'PEAK_END_RESULT'}}</td>
                                <td>{{:SERVICE_ORDER_NO}}</td>
                                <td>{{:SETTLEMENT}}</td>
                                <td>{{:ARCHIVES_NO}}</td>
                                <td>{{dictFUN:RESPONSIBLE 'PEAK_END_RESPONSIBLE'}}</td>
                                <td>{{:VISIT_ACC}}</td>
                                <td>{{:VISIT_TIME}}</td>
、								<td>{{dictFUN:DATA_SOURCES 'PEAKEND_DATA_SOURCE'}}</td>
							</tr>
						{{/for}}					         
					</script>
				</div>
				<div class="row paginate" id="page">
					<jsp:include page="/pages/common/pagination.jsp">
						<jsp:param value="10" name="pageSize" />
					</jsp:include>
				</div>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
	<script type="text/javascript"
		src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript">
		jQuery.namespace("revisit");
		requreLib.setplugs('wdate')//加载时间控件
		function reload() {
			$("#searchForm").searchData();
		}
		$(function() {
			var startDate = getTodayDate(-29);
			var endDate = getTodayDate();
			$("#start_visit_time").val(startDate + " 00:00:00");
			$("#end_visit_time").val(endDate + " 23:59:59");
			$("#searchForm").render({success : function(result) {
				
			}});
		});
		function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$(".moreSearch").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
		}
		
		revisit.exportExcel = function(){
			var startDate = $("#start_visit_time").val();
			var endDate = $("#end_visit_time").val();
			var diffTime = diffDateTime(startDate,endDate);
			var retMsg = checkTimeRange("1",diffTime);
			if(retMsg != true){
				alert(retMsg);
				return false;
			}
			location.href = "${ctxPath}/servlet/export?action=ExportResultList&"
				+ $("#searchForm").serialize();
		}
		
		revisit.detail = function(id){
			var url="${ctxPath}/servlet/peakEnd?action=peakEndResult&type=2";
		    popup.layerShow({type:2,title:'回访详情',area:['100%','100%'],offset:'20px'},url,{id:id});
		}
		
		function reset() {
			document.searchForm.reset();
			var startDate = getTodayDate(-29);
			var endDate = getTodayDate();
			$("#start_visit_time").val(startDate + " 00:00:00");
			$("#end_visit_time").val(endDate + " 23:59:59");
			$("#tableHead").data("mars", "order.resultList");
			$("#searchForm").searchData();
		}
		function showDetail(id,customerName,customerTel1,address){
			showDetailCommon({
				"model":"PeakEnd",
				"url":"/PeakEnd/pages/order/revisit-result-list.jsp",
				"action":"acces",
				"describe":"用户査询[补偿结果查询]数据，査看[{{customerName}}]敏感信息:[用户号码1：{{customerTel1}},地址：{{address}}]"},id,customerName,address,customerTel1);
		}
	</script>
	<script type="text/javascript" src="/iccportal5/static/js/privacyUtil.js"></script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>