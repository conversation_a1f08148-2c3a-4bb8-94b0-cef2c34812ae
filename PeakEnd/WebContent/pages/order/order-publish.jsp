<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>发布</title>
	<style>
		#menuContent1 {
			display:none;position: absolute;border:1px solid rgb(170,170,170);max-width: 220px; max-height: 350px;z-index:10;overflow: auto;background-color: #f4f4f4
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="publishForm"  method="post"  autocomplete="off" data-mars="common.getLastMin">
		<table class="table table-vzebra mt-10">
	    	<tbody>
	        	<tr>
                	<td class="required">数据来源</td>
		            <td> 
	                    <select name="DATA_SOURCES" id="data_sources" data-rules="required"   class="form-control input-sm"  data-mars="common.getDict('PEAKEND_DATA_SOURCE')">
	                    	<option value="">请选择</option>
	                    </select>
					</td>
                    <td class="required">折算类型</td>
                 	<td>
	                  	<select name="CONVERSION_TYPE" id="CONVERSION_TYPE" data-rules="required"  class="form-control input-sm" data-mars="common.getConvert">
	                  		<option value="">请选择</option>
	                  	</select>
              	  	</td>
				</tr>
                <tr>
					<td>发布类型</td>
                    <td>
                        <select name="PUBLISH_TYPE" id="publish_type"  class="form-control input-sm"   onchange="publish.clickPulishType(this);">
                        	<option value="0">全部发布</option>
                        	<option value="1">部分发布</option>
                        </select>
                    </td>
                    <td >可分配人数</td>
                	<td><input type="text" name="PUB_USER_NUM" id="pulish_num" data-rules="digits" class="form-control input-sm" readonly="readonly"></td>
                </tr>
				<tr>
					<td class="required">所属主体</td>
            		<td>
            			<select class="form-control input-sm" name="ORG_CODE" data-rules="required" id="orgCode" onchange="publish.loadConversionType(this)" data-mars="common.sysCode('ORG_CODE')">
                           <option value="">请选择</option>
                       </select>
					</td>
					<td >档案类型</td>
					<td>
						<select class="form-control input-sm" id="ARCHIVE_TYPE"  name="ARCHIVE_TYPE" data-mars="common.sysCode('WOM_ARCHIVE_TYPE')" onchange="publish.loadConversionType(this)" >
	                       <option value="">请选择</option>
						</select>
					</td>
				</tr>
				<tr>
					<td>委托方</td>
            		<td>
            			<select class="form-control input-sm" name="CLIENT_CODE" data-mars="common.getDict('CLIENT_NAME')">
                           <option value="">请选择</option>
                       </select>
					</td>
				</tr>
            	<tr>
                  	<td>品类</td>
                  	<td>
                      	<input class="hidden" name="PROD_CODE" id="prodCode" onchange="publish.getOrderCount()">
						<input class="form-control input-sm" onchange="cleanVal(this,'prodCode')" onclick="showMenu(this,'menuContent1')" name="PROD_NAME"  id="prodName"  />
						<div id="menuContent1" class="menuContent1" >
					       	<input type="hidden" id="proCodeTreeHidden" data-mars="common.productCode" />
					        <ul id="proCodeTree1" class="ztree" style="margin-top:0; width:100%; height:auto;"></ul>
					    </div>
                  	</td>
                  	<td>产品品牌</td>
	                <td>
	                	<select name="BRAND_CODE" id="brand_code" class="form-control input-sm" data-mars="common.getBrandlist">
	                        <option value="">请选择</option>
	                  </select>
	                </td>
                </tr>
                <tr>
                	<td class="required">服务请求</td>
                  	<td>
                    	<select id="contactOrderServTypeCode" name="CONTACT_ORDER_SERVICE_TYPECODE" data-rules="required" data-mars="common.serviceRequire1Level"  class="form-control input-sm" onchange="changecontactOrderServTypeCode(this)">
							<option value="">请选择</option>
						</select>
                  	</td>
                	<td >服务请求2</td>
                  	<td > 
                 	 	<div class="input-group input-group-sm">
	                   	   	<input type="text" name="CONTACT_ORDER_SERVICE_NAME2" id="contactOrderSerItem2Name" readonly="readonly"   class="form-control input-sm" >
		 					<input type="text" name="CONTACT_ORDER_SERVICE_CODE2" id="contactOrderSerItem2Code" readonly="readonly" onchange="publish.getOrderCount()"  class="hidden" > 
	                    	<span class="input-group-addon" onclick="queryServer()"><i class="glyphicon glyphicon-zoom-in"></i></span>
            			</div>
                  	</td>
                </tr>
                <tr>
                   	<td>区域</td>
		            <td> 
	                    <select name="REGION" id="region" class="form-control input-sm" data-mars="common.getDict('CALLBACK_REGION')">
	                    </select>
					</td>
                    <td>退换机类型</td>
                    <td>
                        <select name="RETURN_REPLACE_TYPE" id="returnReplaceType" class="form-control input-sm" data-mars="common.getDict('PEAKEND_RETURN_REPLACE_TYPE')">
                            <option value="">请选择</option>
                            <option value="none">空</option>
                            <option value="all">全部</option>
                        </select>
                    </td>
				</tr>
				<tr>
	                <td>回访结果</td>
	                <td>
	                    <select name="REVISIT_RESULT" id="revisitResult" class="form-control input-sm"  data-mars="common.getDict('CALLBACK_RESULT')" >
	                        <option value="">请选择</option>
	                    </select>
	                </td>
	                <td>卡券赠送</td>
	                <td>
	                    <select name="CARD_TYPE" class="form-control input-sm" data-mars="common.getDict('PEAKEND_CARD_TYPE')" onchange="cardTypeChange($(this).val())">
	                        <option value="">请选择</option>
	                    </select>
	                </td>
		        </tr>
                <tr> 
					<td class="required">同步开始时间</td>
					<td><input type="text" name="SYN_START_TIME" id="publish_syn_start_time" data-rules="required" class="form-control input-sm" onFocus="WdatePicker({onpicked:function(){publish.getOrderCount();this.blur();},maxDate:'#F{$dp.$D(\'publish_syn_end_time\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" ></td>
					<td class="required">同步结束时间</td>
                  	<td><input type="text" name="SYN_END_TIME" id="publish_syn_end_time" data-rules="required" class="form-control input-sm"  onclick="WdatePicker({onpicked:function(){publish.getOrderCount();this.blur();},minDate:'#F{$dp.$D(\'publish_syn_start_time\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})"></td>
				</tr>
				<tr class="">
	            	<!-- 统计数 -->
					<th colspan="4" >
						<div>
		                 	<div style="float: left;padding-left: 10px">截止时间：<span id="lastTime" style="color: blue"></span>(分钟)</div>
		                	<div style="padding-right: 10px" >档案数：<span style="color: red"  id="countContent">0</span></div>
						</div>
		        	</th>
				</tr>
			</tbody>
		</table>  
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary"  type="button" onclick="publish.ajaxSubmitForm()">确认</button>
			<button class="btn btn-sm btn-default ml-20"  type="button" onclick="layer.closeAll();">关闭</button>
	    </div>
	</form>		
</EasyTag:override>
<EasyTag:override name="script">
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css" />
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript">
		jQuery.namespace("publish");
	    requreLib.setplugs("wdate");
		$(function(){
			var startDate = getTodayDate(-5);
			var endDate = getTodayDate();
	    	$("#publish_syn_start_time").val(startDate + " 00:00:00");
	    	$("#publish_syn_end_time").val(endDate + " 23:59:59");
    		$("#publishForm").render({success : function(result){
    			if(result["common.productCode"]){
    				pcNodes1 = result["common.productCode"].data;
    			}
    			if(result["common.serviceRequire"]){
    				srNodes1 = result["common.serviceRequire"].data;
    			}
    			if(result["common.getLastMin"]){
	    			$("#lastTime").html(result["common.getLastMin"].data.TIME)
	    		}
    			$.fn.zTree.init($("#proCodeTree1"), pcSetting1,pcNodes1);
    		}});
		})
		
		
	    publish.clickPulishType=function(data){
			if(data.value=="1"){//部分发布
				$("#pulish_num").removeAttr('readonly')
	 		}else{
	 			$("#pulish_num").val('');
	 			$("#pulish_num").attr('readonly','readonly')
			}
		}
	    publish.loadConversionType = function(data){
	    	var val=$("#CONVERSION_TYPE").val();
	    	$("#CONVERSION_TYPE").data({"mars":"common.getConvertByData('"+$("#orgCode").val()+"','"+$("#ARCHIVE_TYPE").val()+"')",});
	    	$("#CONVERSION_TYPE").render({
	    		success:function(req){//存在之前选项值默认带入
	    			for(var key in req){
	    				if(req[key].data[val]!=null){
	    	    			$("#CONVERSION_TYPE").val(val);	
	    				}
	    			}
	    		}
	    	});
	    	
	    	var params = {};
	 		params.queryLevel = 1;
	 		params.laseServiceItem = '';
	 		params.prodCode = $("#prodCode").val();
	 		params.orgCode = $("#orgCode").val();
	 		if(ordercons.openDetail){
				orderfunc.getBranch(val,this.areaCode);
	 		}
	 		if(!checkNull(val)){
	 			$("#contactOrderServTypeCode").render({data:params,success:function(result) {
	 				
	 			}})
	 		}
		}
		$("#publishForm").on("change","input[class='form-control input-sm']",function(){
			 publish.getOrderCount();
		});
		$("#publishForm").on("change","select[class='form-control input-sm']",function(){
			 publish.getOrderCount();
		});
	    publish.getOrderCount=function(data){
			var data = form.getJSONObject("publishForm");
			ajax.remoteCall("${ctxPath}/servlet/order?action=getOrderCount",data,function(result) { 
				if(result.state == 1){
					var count = result.data.NUM || 0;
					$("#countContent").html(count);
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		publish.ajaxSubmitForm = function(){
			if($("#publish_type").val()=="1"){//部分发布
				if($("#pulish_num").val()==""){
					layer.alert("可分配人数不为空",{icon: 5});
			 		return;	
				}
			}
			if(!form.validate("#publishForm")){
				return;
			};
			var data = form.getJSONObject("publishForm");
			ajax.remoteCall("${ctxPath}/servlet/order?action=publish",data,function(result) { 
				if(result.state == 1){
					layer.closeAll();
					layer.msg(result.msg,{icon: 1});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
	    var pcNodes1;
	    var pcSetting1 = {
			data: {
    	        simpleData: {
    	            enable: true,
    				idKey: "id",
    				pIdKey: "pId",
    				rootPId: 0
    	        },
    	        key: {
    	        	name: "name"
    	        }
    	    },
    	    callback:{
    	    	onClick:zTreeOnclick1
    	    },
    	    async:{
    	    	enable:true,
    	    	type:"post",
    	    	url:"/neworder/servlet/comm?query=proCode2Level",
    	    	autoParam:["id","level"]
    	    }
    	};
	    var srNodes1;
		var srSetting1 = {
			data: {
    	        simpleData: {
    	            enable: true,
    				idKey: "id",
    				pIdKey: "pId",
    				rootPId: 0
    	        },
    	        key: {
    	        	name: "name"
    	        }
    	    },
    	    callback:{
    	    	onClick:zTreeOnclick1
    	    },
    	    async:{
    	    	enable:true,
    	    	type:"post",
    	    	url:"/neworder/servlet/comm?query=serviceRequire2Level",
    	    	autoParam:["id","level"],
    	    	otherParam:["orgCode",function() { return $("#orgCode").val() }]
    	    }
    	};
		function onBodyDownPc1(event) {
    	    if (!( event.target.id == "menuContent1" || event.target.id == "prodName" || $(event.target).parents("#menuContent1").length > 0)) {
    	        hideMenu1('menuContent1');
    	  	}
    	}
    	//点击事件
		function zTreeOnclick1(event,treeId,treeNode){
			if(treeNode.isParent){ 
				var ztreeObj = $.fn.zTree.getZTreeObj(treeId); 
				ztreeObj.expandNode(treeNode);
            }else{
		    	$("#prodCode").val(treeNode.id).change();
		    	$("#prodName").val(treeNode.name);
		    	hideMenu1('menuContent1');
            }
		}
		//显示菜单
		function showMenu(obj,treeId) {
			var leftPx = $(obj).offset().left;
			var topPx = $(obj).offset().top;
			var heightPx = $(obj).height()+$(obj).innerHeight()/2;
		    $("#"+treeId).slideDown("fast");
		    $("body").bind("mousedown", onBodyDownPc1);
		}
		//隐藏菜单
		function hideMenu1(divId) {
		    $("#"+divId).fadeOut("fast");
		    	$("body").unbind("mousedown", onBodyDownPc1);
		}
		function cleanVal(data,id){//如果值为空则清空某个id的值
			if($(data).val()==""){
				$("#"+id).val('');
			}
		}
		
		function queryServer(){
	   		var orgCode= $("#orgCode").val(); 
	   		var prodCode= $("#prodCode").val(); 
	   		var laseServiceItem= $("#contactOrderServTypeCode").val();
	   		var text= "";//带入搜索框的内容
		   	text= $("#contactOrderSerItem2Name").val();
	   		var isExistUrge = false;
	   		popup.layerShow({type:2,title:'服务请求',area:['400px','450px'],offset:'20px',shadeClose:false},"/neworder/pages/access/service-query.jsp?revisit=1&orgCode="+orgCode+"&prodCode="+prodCode+"&laseServiceItem="+laseServiceItem+"&isExistUrge="+isExistUrge+"&text="+text);
		}
		
		function changecontactOrderServTypeCode(ths){
			$("#contactOrderSerItem2Code").val("");
		}
		
		function cardTypeChange(val){
			if(val==2){
				$("#prodCode").attr("data-rules","required");
				$("#prodName").attr("data-rules","required");
				$("#prodCode").parents("tr").find("td:first").addClass("required");
			}else{
				$("#prodCode").attr("data-rules","");
				$("#prodName").attr("data-rules","");
				$("#prodCode").parents("tr").find("td:first").removeClass("required");
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>