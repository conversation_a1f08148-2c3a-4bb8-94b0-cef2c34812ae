<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>峰终回访单处理</title>
	<style>
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form autocomplete="off" name="searchForm"class="form-inline" id="searchForm">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
					<h5>
						<span class="glyphicon glyphicon-list"></span> 回访单处理
					</h5>
				</div>
				<hr style="margin: 5px -15px">
				<div class="form-group">
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width:80px;color: red">折算类型</span>	
						<select name="CONVERSION_TYPE" id="conversionType" class="form-control input-sm" data-mars="common.getUserConvert">
                    		<option value="">请选择</option>
                    	</select>
				    </div>
				    <div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width:80px">产品主体</span>	
						<select class="form-control input-sm" name="ORG_CODE" data-mars="common.sysCode('ORG_CODE')">
							<option value="">请选择</option>
                        </select>
				    </div>
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width:80px">产品品牌</span>	
						<select class="form-control input-sm" name="BRAND_CODE" data-mars="common.getBrandlist">
							<option value="">请选择</option>
                        </select>
				    </div>
				    <div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width:80px">用户号码</span>	
						<input type="text" name="CUSTOMER_MOBILEPHONE1" class="form-control input-sm">
				   	</div>
				    <div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width: 80px;">服务单号</span>	
						<input type="text" name="SERVICE_ORDER_NO" class="form-control input-sm" >
					</div>
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width: 80px;">服务请求</span>
						<select class="form-control input-sm" name="ORDER_SERV_TYPE_CODE" data-mars="common.serviceRequire1Level">
							<option value="">请选择</option>
                    	</select>
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" style="width: 80px;">同步时间</span>	
						<input type="text" name="SYN_START_TIME" id="select_syn_start_time" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">
						<span class="input-group-addon" >-</span>	
						<input type="text" name="SYN_END_TIME" id="select_syn_end_time" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">									  
					</div>
					<div class="input-group input-group-sm pull-right ml-10">
					 	<button type="button" id="moreBtn" class="btn btn-sm btn-link" onclick="toggleMore()" title="展开"> 展开 <span class="glyphicon glyphicon glyphicon-menu-up"></span>&nbsp;</button>
					</div>
					
				</div>
				<div class="form-group moreSearch" style="display:none">
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width: 80px;">状态</span>	
						<select class="form-control input-sm" name="REVISIT_STATE" data-mars="common.getDict('PEAKEND_REVISIT_STATE')">
							<option value="">请选择</option>
                       	</select>
					</div>
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width: 80px;">档案类型</span>	
						<select class="form-control input-sm" name="ARCHIVES_TYPE" data-mars="common.sysCode('WOM_ARCHIVE_TYPE')" >
							<option value="">请选择</option>
						</select>
					</div>
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width: 80px;">委托方</span>	
						<select class="form-control input-sm" name="CLIENT_CODE" data-mars="common.getDict('CLIENT_NAME')" >
							<option value="">请选择</option>
                        </select>
				   	</div>
					<div class="input-group input-group-sm" id="ACC_DIV" style="width: 200px;display: none">
						<span class="input-group-addon" style="width: 80px;">申请人</span>	
						<input type="text" name="USER_ACC" id="userAc" class="form-control input-sm" >
					</div>
					<div class="input-group input-group-sm" style="width: 200px">
						<span class="input-group-addon" style="width: 80px;">卡券类型</span>	
						<select class="form-control input-sm" name="CARD_TYPE" data-mars="common.getDict('PEAKEND_CARD_TYPE')" >
							<option value="">请选择</option>
                        </select>
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" style="width: 80px;">发布时间</span>	
						<input type="text" name="PUBLISH_START_TIME" id="PUBLISH_START_TIME" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">
						<span class="input-group-addon" >-</span>	
						<input type="text" name="PUBLISH_END_TIME" id="PUBLISH_END_TIME" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">									  
					</div>
				</div>
				<div class="form-group">
					<EasyTag:res resId="peak_end_handle_apply">
						<div class="input-group input-group-sm pull-left mr-10">
							<button type="button" class="btn btn-sm btn-success" onclick="handle.apply()">资料申请</button>
						</div>
					</EasyTag:res>
					<EasyTag:res resId="peak_end_handle_export">
						<div class="input-group input-group-sm pull-left mr-10">
							<button type="button" class="btn btn-sm btn-success" onclick="handle.exportFile()">导出</button>
						</div>
					</EasyTag:res>
						<div class="input-group input-group-sm pull-right">
							<button type="button" class="btn btn-sm btn-default" onclick="resetForm()">重置</button>
							<button type="button" class="btn btn-sm btn-default ml-10" onclick="reload()">搜索</button>
						</div>
				</div>
			</div>
			<div class="ibox-content">
				<div class="row table-responsive">
					<table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" data-mars="order.handleList">
						<thead>
							<tr>
								<th class="text-c">操作</th>
								<th class="text-c">申请人</th>
								<th class="text-c">状态</th>
								<th class="text-c">工单结果</th>
								<th class="text-c">卡券类型</th>
								<th class="text-c">发布时间</th>
								<th class="text-c">用户姓名</th>
								<th class="text-c">区号</th>
								<th class="text-c">区域</th>
								<th class="text-c">用户号码1</th>
								<th class="text-c">用户号码2</th>
								<th class="text-c">用户号码3</th>
								<th class="text-c">用户地址</th>
								<th class="text-c">折算类型</th>
								<th class="text-c">档案类型</th>
								<th class="text-c">产品主体</th>
								<th class="text-c">产品品牌</th>
								<th class="text-c">产品品类</th>
								<th class="text-c">产品型号</th>
								<th class="text-c">服务单号</th>
								<th class="text-c">服务请求类型</th>
								<th class="text-c">服务请求大类</th>
								<th class="text-c">服务请求小类</th>
								<th class="text-c">网点</th>
							</tr>
						</thead>
						<tbody id="dataList"></tbody>
					</table>
					<script id="list-template" type="text/x-jsrender">
						{{for list}}
							<tr>
								<td>
									<a href="javascript:handle.toHandle('{{:ID}}','{{:BRAND_CODE}}','{{:PROD_CODE}}','{{:ORDER_SER_ITEM2_CODE}}')">处理</a>
								</td>
                            	<td>{{:RECEIVE_ACC}}</td>
                                <td>{{dictFUN:REVISIT_STATE 'PEAKEND_REVISIT_STATE'}}</td>
                                <td>{{:ORDER_RESULT}}</td>
                                <td>{{dictFUN:CARD_TYPE 'PEAKEND_CARD_TYPE'}}</td>
                                <td>{{:PUBLISH_TIME}}</td>
                                <td>{{:CUSTOMER_NAME}}</td>
                                <td>{{:AREA_NUM}}</td>
                                <td>{{:AREA_NAME}}</td>
                                <td>{{:CUSTOMER_MOBILEPHONE1}}</td>
                                <td>{{:CUSTOMER_MOBILEPHONE2}}</td>
                                <td>{{:CUSTOMER_MOBILEPHONE3}}</td>
                                <td>{{:CUSTOMER_ADDRESS}}</td>
                                <td>{{:CONVERSION_TYPE}}</td>
                                <td>{{sysCodeFUN:ARCHIVES_TYPE 'WOM_ARCHIVE_TYPE'}}</td>
                                <td>{{sysCodeFUN:ORG_CODE 'ORG_CODE'}}</td>
                                <td>{{:BRAND_NAME}}</td>
                                <td>{{:PROD_NAME}}</td>
                                <td>{{:PRODUCT_CODE}}</td>
                                <td>{{:SERVICE_ORDER_NO}}</td>
                                <td>{{:ORDER_SERV_TYPE_NAME}}</td>
                                <td>{{:ORDER_SER_ITEM1_NAME}}</td>
                                <td>{{:ORDER_SER_ITEM2_NAME}}</td>
                                <td>{{:UNIT_NAME}}</td>
							</tr>
						{{/for}}					         
					</script>
				</div>
				<div class="row paginate" id="page">
					<jsp:include page="/pages/common/pagination.jsp">
						<jsp:param value="10" name="pageSize" />
					</jsp:include>
				</div>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript">
		jQuery.namespace("handle");
		requreLib.setplugs('wdate');//加载时间控件
		var myWindow;
		var conversion_type;//折算类型
		function reload() {
			$("#searchForm").searchData();
		}
		$(function() {
			var startDate = getTodayDate(-7);
			var endDate = getTodayDate();
			$("#select_syn_start_time").val(startDate + " 00:00:00");
			$("#select_syn_end_time").val(endDate + " 23:59:59");
			$("#searchForm").render({success : function(result) {
				if(result["order.handleList"].flag){
					$("#ACC_DIV").show();
				}
				if(result["common.getConvert"]!=null){
					conversion_type = result["common.getConvert"];
    			}
			}});
		});
		//申请
		handle.apply = function() {
			var id = $("#conversionType").val();//折算类型
			if(!id){
				layer.msg("请选择折算类型",{icon:0});
				return;
			}
			var data = form.getJSONObject("searchForm");
    		ajax.remoteCall("${ctxPath}/servlet/order?action=apply",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1});
				}else{
					layer.alert(result.msg,{icon:2});
				}
			});
		}
		//处理,param:id、品牌、品类、服务请求小类
		handle.toHandle = function(id,brandCode,prodCode,serviceItem2Code){
			let d = {brandCode,prodCode,serviceItem2Code};
			//判断订单是否为事后补偿
			ajax.remoteCall("${ctxPath}/servlet/order?action=checkCompensation",d,function(result){
				if(result.state == 1){
					var url="${ctxPath}/servlet/peakEnd?action=peakEndDetail&id="+id+"&type=2";
					var nLeft = ( window.screen.width - 1600 )/2;
					var nTop = 150;
					var h = window.screen.height * 0.7;
					var sStyle="width="+1600+"px,height="+h+"px,left="+nLeft+"px,top="+nTop+"px,scrollbars=yes,resizable=no,status=1";
					if(!myWindow||myWindow.closed){
					 	myWindow = window.open(url,"回访详情",sStyle);
					}else{
						alert("请先关闭上一个回访详情界面");
					}
				}else{
					layer.alert("该回访单不属于事后补偿类型",{icon:2});
				}
			});
		}
		
		handle.exportFile = function(){
			location.href = "${ctxPath}/servlet/order?action=exportResult&"
				+ $("#searchForm").serialize();
		}

		$.views.converters("ORG_CODE", function(val) {
			var req = org_code;
			if (typeof (req.data) == "undefined") {
				return val;
			} else {
				return req.data[val];
			}
		});

		$.views.converters("WOM_ARCHIVE_TYPE", function(val) {
			var req = wom_archive_type;
			if (typeof (req.data) == "undefined") {
				return val;
			} else {
				return req.data[val];
			}
		});
		$.views.converters("CONVERSION_TYPE", function(val) {
			var req = conversion_type;
			if (typeof (req) == "undefined") {
				return val;
			} else {
				return req[val];
			}
		});

		function resetForm() {
			document.searchForm.reset();
			var startDate = getTodayDate(-7);
			var endDate = getTodayDate();
			$("#select_syn_start_time").val(startDate + " 00:00:00");
			$("#select_syn_end_time").val(endDate + " 23:59:59");
			$("#tableHead").data("mars", "order.handleList");
			$("#searchForm").searchData();
		}
		
		function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$(".moreSearch").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
		}
		function call(value, index, elem){
			if(value){
	   			//外呼
		  		parent.top.myCCbar.call(value,function(){
				    compUpgrade.changeParentStatus('call');
				    layer.msg("正在接通···",{icon:1,time:1000});
				    layer.close(index); 
			  	}
				,function(){
					 layer.msg("外呼失败，请重新拨打！",{icon:5,time:3000});
				});
	   		}
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>