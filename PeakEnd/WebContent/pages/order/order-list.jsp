<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>峰终回访单查询</title>
	<style>
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form autocomplete="off" name="searchForm"class="form-inline" id="searchForm">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
					<h5>
						<span class="glyphicon glyphicon-list"></span> 回访单查询
					</h5>
				</div>
				<hr style="margin: 5px -15px">
				<div class="form-group">
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width: 80px;">服务单号</span>	
						<input type="text" name="SERVICE_ORDER_NO" class="form-control input-sm" >
					</div>
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width: 80px;">服务请求</span>
						<select class="form-control input-sm" name="ORDER_SERV_TYPE_CODE" data-mars="common.serviceRequire1Level">
							<option value="">请选择</option>
                    	</select>
					</div>
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width:80px">产品主体</span>	
						<select class="form-control input-sm" name="ORG_CODE" id="orgCode" data-mars="common.sysCode('ORG_CODE')">
							<option value="">请选择</option>
                        </select>
				    </div>
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width:80px">产品品牌</span>	
						<select class="form-control input-sm" name="BRAND_CODE" id="brandCode" data-mars="common.getBrandlist">
							<option value="">请选择</option>
                        </select>
				    </div>
           			<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width: 80px;">档案类型</span>	
						<select class="form-control input-sm" name="ARCHIVES_TYPE" id="archivesType" data-mars="common.sysCode('WOM_ARCHIVE_TYPE')" >
							<option value="">请选择</option>
						</select>
					</div>
				    <div class="input-group input-group-sm">
						<span class="input-group-addon" style="width: 80px;">同步时间</span>	
						<input type="text" name="SYN_START_TIME" id="select_syn_start_time" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">
						<span class="input-group-addon" >-</span>	
						<input type="text" name="SYN_END_TIME" id="select_syn_end_time" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">									  
					</div>
					<div class="input-group input-group-sm pull-right ml-10">
					 	<button type="button" id="moreBtn" class="btn btn-sm btn-link" onclick="toggleMore()" title="展开"> 展开 <span class="glyphicon glyphicon glyphicon-menu-up"></span>&nbsp;</button>
					</div>
				</div>
				<div class="form-group moreSearch" style="display:none">
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width: 80px;">状态</span>	
						<select class="form-control input-sm" name="REVISIT_STATE" data-mars="common.getDict('PEAKEND_REVISIT_STATE')">
							<option value="">请选择</option>
                       	</select>
					</div>
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width: 80px;">委托方</span>	
						<select class="form-control input-sm" name="CLIENT_CODE" id="clientCode" data-mars="common.getDict('CLIENT_NAME')" >
							<option value="">请选择</option>
                        </select>
				   	</div>
				   	<div class="input-group input-group-sm" style="width: 200px;">
					      <span class="input-group-addon" style="width: 80px;">网点</span>	
						  <input type="text" name="UNIT_CODE" id="website_code" class="hidden">
	                      <input type="text" name="UNIT_NAME" id="website_name"  onclick="revisit.website()" class="form-control input-sm" >
					</div>
			  	   	<div class="input-group input-group-sm" style="width: 200px;">
				    	<span class="input-group-addon" style="width: 80px;">申请人</span>	
					  	<input type="text" name="RECEIVE_ACC" class="form-control input-sm" >
				   	</div>
				   	<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width: 80px;">指派人</span>	
						<input type="text" name="APPOINT_ACC" class="form-control input-sm" >
				   	</div>
				   	<div class="input-group input-group-sm" style="width: 200px;">
					<span class="input-group-addon" style="width: 80px;">数据来源</span>	
						<select name="DATA_SOURCES"  data-mars="common.getDict('PEAKEND_DATA_SOURCE')"  class="form-control input-sm">
							<option value="">请选择</option>
						</select>
				   	</div>
				    <div class="input-group input-group-sm" style="">
					      <span class="input-group-addon" style="width: 80px;">分配时间</span>	
						  <input type="text" name="RECEIVE_START_TIME" id="receive_start_time"  class="form-control input-sm Wdate"  onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">
						  <span class="input-group-addon" >-</span>	
						  <input type="text" name="RECEIVE_END_TIME" id="receive_end_time"  class="form-control input-sm Wdate"  onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">									  
				   	</div> 	
				</div>
				
				
				<div class="form-group">
					<EasyTag:res resId="peak_end_order_publish">
						<div class="input-group input-group-sm pull-left mr-10">
							<button type="button" class="btn btn-sm btn-success" onclick="revisit.publish()">发布</button>
						</div>
					</EasyTag:res>
					<EasyTag:res resId="peak_end_order_appoit">
						<div class="input-group input-group-sm pull-left mr-10">
							<button type="button" class="btn btn-sm btn-success" onclick="revisit.appoint()">指派</button>
						</div>
					</EasyTag:res>
					<EasyTag:res resId="peak_end_order_recycle">
						<div class="input-group input-group-sm pull-left mr-10">
							<button type="button" class="btn btn-sm btn-success" onclick="revisit.recycle()">回收</button>
						</div>
					</EasyTag:res>
					<EasyTag:res resId="peak_end_order_delete">
						<div class="input-group input-group-sm pull-left mr-10">
							<button type="button" class="btn btn-sm btn-danger" onclick="revisit.deleteData()">删除</button>
						</div>
					</EasyTag:res>
					<EasyTag:res resId="peak_end_order_dataImport">
						<div class="input-group input-group-sm pull-left mr-10">
							<button type="button" class="btn btn-sm btn-success" onclick="revisit.rstImport()">结果Excel导入</button>
						</div>
						<div class="input-group input-group-sm pull-left mr-10">
							<button type="button" class="btn btn-sm btn-success" onclick="revisit.dataImport()">数据Excel导入</button>
						</div>
					</EasyTag:res>
					<div class="input-group input-group-sm pull-right">
						<button type="button" class="btn btn-sm btn-default" onclick="resetForm()">重置</button>
						<button type="button" class="btn btn-sm btn-default ml-10" onclick="reload()">搜索</button>
					</div>
				</div>
			</div>
			<div class="ibox-content">
				<div class="row table-responsive">
					<table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" data-mars="order.list">
						<thead>
							<tr>
								<th class="text-c">选择</th>
								<th class="text-c">序号</th>
								<th class="text-c">状态</th>
								<th class="text-c">工单结果</th>
								<th class="text-c">用户姓名</th>
								<th class="text-c">产品主体</th>
								<th class="text-c">产品品牌</th>
								<th class="text-c">区号</th>
								<th class="text-c">区域</th>
								<th class="text-c">用户地址</th>
								<th class="text-c">用户号码1</th>
								<th class="text-c">用户号码2</th>
								<th class="text-c">用户号码3</th>
								<th class="text-c" style="width:50px;max-width:50px;min-width:50px">查看</th>
								<th class="text-c">网点</th>
								<th class="text-c">委托方</th>
								<th class="text-c">档案类型</th>
								<th class="text-c">档案编码</th>
								<th class="text-c">服务单号</th>
								<th class="text-c">服务请求类型</th>
								<th class="text-c">服务请求大类</th>
								<th class="text-c">服务请求小类</th>
								<th class="text-c">同步时间</th>
								<th class="text-c">回访结果</th>
							</tr>
						</thead>
						<tbody id="dataList"></tbody>
					</table>
					<script id="list-template" type="text/x-jsrender">
						{{for list}}
							<tr>
								<td><label class="checkbox checkbox-info"><input type="checkbox" data-phone="{{:CUSTOMER_MOBILEPHONE1}}" data-id="{{:ID}}" ><span></span></label></td>
								<td>{{:#index+1}}</td>
								<td>{{dictFUN:REVISIT_STATE 'PEAKEND_REVISIT_STATE'}}</td>
								<td>{{:ORDER_RESULT}}</td>
                            	<td>{{call:CUSTOMER_NAME CUSTOMER_NAME_data #index+1 'name' fn='getData2'}}</td>
                                <td>{{sysCodeFUN:ORG_CODE 'ORG_CODE'}}</td>
                                <td>{{:BRAND_NAME}}</td>
                                <td>{{:AREA_NUM}}</td>
                                <td>{{:AREA_NAME}}</td>
                                <td>{{call:CUSTOMER_ADDRESS CUSTOMER_ADDRESS_data #index+1 'addr' fn='getData2'}}</td>
                                <td>{{call:CUSTOMER_MOBILEPHONE1 CUSTOMER_MOBILEPHONE1_data #index+1 'phone' fn='getData2'}}</td>
                                <td>{{call:CUSTOMER_MOBILEPHONE2 CUSTOMER_MOBILEPHONE2_data #index+1 'phone2' fn='getData2'}}</td>
                                <td>{{call:CUSTOMER_MOBILEPHONE3 CUSTOMER_MOBILEPHONE3_data #index+1 'phone3' fn='getData2'}}</td>
                                <td title="査看" class="text-c">
												<span onclick="showDetail('{{:#index+1}}','{{:CUSTOMER_NAME_data}}','{{:CUSTOMER_ADDRESS_data}}','{{:CUSTOMER_MOBILEPHONE1_data}}','{{:CUSTOMER_MOBILEPHONE2_data}}','{{:CUSTOMER_MOBILEPHONE3_data}}')" class="glyphicon glyphicon-eye-open"
												id="show{{:#index+1}}"
												style="color:rgb(255,140,60);">
												</span>
											</td>
                                <td>{{:UNIT_NAME}}</td>
                                <td>{{:CLIENT_NAME}}</td>
                                <td>{{sysCodeFUN:ARCHIVES_TYPE 'WOM_ARCHIVE_TYPE'}}</td>
                                <td>{{:ARCHIVES_NO}}</td>
                                <td>{{:SERVICE_ORDER_NO}}</td>
                                <td>{{:ORDER_SERV_TYPE_NAME}}</td>
                                <td>{{:ORDER_SER_ITEM1_NAME}}</td>
                                <td>{{:ORDER_SER_ITEM2_NAME}}</td>
                                <td>{{:SYN_TIME}}</td>
                                <td>{{dictFUN:RESULT 'PEAK_END_RESULT'}}</td>
							</tr>
						{{/for}}					         
					</script>
				</div>
				<div class="row paginate" id="page">
					<jsp:include page="/pages/common/pagination.jsp">
						<jsp:param value="10" name="pageSize" />
					</jsp:include>
				</div>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript">
		jQuery.namespace("revisit");
		requreLib.setplugs('wdate');//加载时间控件
		var orgCodeData;//主体
		var archiveTypeData;//档案类型
		var clientData;//委托方
	
		function reload() {
			if(!$("#select_syn_start_time").val()||!$("#select_syn_end_time").val()){
				layer.msg("请选择同步时间",{icon:2});
				return;
			}
			$("#searchForm").searchData();
		}
		$(function() {
			var startDate = getTodayDate(-7);
			var endDate = getTodayDate();
			$("#select_syn_start_time").val(startDate + " 00:00:00");
			$("#select_syn_end_time").val(endDate + " 23:59:59");
			$("#searchForm").render({success : function(result) {
				if(result["common.sysCode('ORG_CODE')"]!=null){
					orgCodeData = result["common.sysCode('ORG_CODE')"];
    			}
				if(result["common.sysCode('WOM_ARCHIVE_TYPE')"]!=null){
					archiveTypeData = result["common.sysCode('WOM_ARCHIVE_TYPE')"];
    			}
			}});
		});

		revisit.publish = function() {
			popup.layerShow({
				type : 1,
				title : '发布',
				area : [ '700px', '550px' ],
			}, "${ctxPath}/pages/order/order-publish.jsp");
		}
		revisit.appoint = function() {
			popup.layerShow({
				type : 1,
				title : '指派',
				area : [ '700px', '550px' ],
			}, "${ctxPath}/pages/order/order-appoint.jsp");
		}
		revisit.recycle = function() {
			popup.layerShow({
				type : 1,
				title : '回收',
				area : [ '700px', '600px' ],
			}, "${ctxPath}/pages/order/order-recycle.jsp");
		}
		//结果导入
		revisit.rstImport = function() {
			popup.layerShow({
				type : 1,
				title : "结果导入",
				offset : '20px',
				area : [ '420px', '200px' ]
			}, "${ctxPath}/pages/order/order-import-result.jsp", null);
		}
		//数据导入
		revisit.dataImport = function() {
			popup.layerShow({
				type : 1,
				title : "数据导入",
				offset : '20px',
				area : [ '420px', '200px' ]
			}, "${ctxPath}/pages/order/order-import-data.jsp", null);
		}
		//网点
		revisit.website= function() {
	   		popup.layerShow({type:2,title:'网点查询',area:['920px','700px'],offset:'20px'},"/neworder/pages/access/website-list.jsp?&ok=1&unitCodeId=website_code&unitNameId=website_name&sqfwsFlag=N");
	   	}
		//删除
		revisit.deleteData = function() {
			var start=$("#select_syn_start_time").val();
			var end=$("#select_syn_end_time").val();
			if($("#select_syn_start_time").val()==""){
				layer.alert('请选择同步开始时间',{icon: 5});
				return;
			}
			if($("#select_syn_end_time").val()==""){
				layer.alert('请选择同步结束时间',{icon: 5});
				return;
			}
			var ids = $("#dataList").find("input[type='checkbox']:checked");
			if(ids.length<1){
				layer.confirm('是否删除条件内的所有的导入数据', {
		            btn : [ '确定', '取消' ]//按钮
		        }, function(index) {
					var data={org_code:$("#orgCode").val(),archives_type:$("#archivesType").val(),client_code:$("#clientCode").val(),brand_code:$("#brandCode").val(),startTime:start,endTime:end};
		        	ajax.remoteCall("${ctxPath}/servlet/order?action=del",data,function(result) { 
						if(result.state == 1){
							layer.msg(result.data,{icon: 1});
							reload();
						}else{
							layer.msg(result.msg,{icon: 5});
						}
			        });
				});
			}else{
				var id="";
				for(var i=0;i<ids.length;i++){
					id=id==""?$(ids[i]).attr("data-id"):id+";"+$(ids[i]).attr("data-id");
				}
				var data={id:id,startTime:start,endTime:end};
				layer.confirm('是否删除勾选的的数据', {
		            btn : [ '确定', '取消' ]//按钮
		        }, function(index) {
		        	ajax.remoteCall("${ctxPath}/servlet/order?action=del",data,function(result) { 
						if(result.state == 1){
							layer.msg(result.data,{icon: 1});
							reload();
						}else{
							layer.msg(result.msg,{icon: 5});
						}
			        });
				});
			}
	   	}
		

		$.views.converters("ORG_CODE", function(val) {
			var req = orgCodeData;
			if (typeof (req) == "undefined" || typeof (req.data) == "undefined") {
				return val;
			} else {
				return req.data[val];
			}
		});

		$.views.converters("WOM_ARCHIVE_TYPE", function(val) {
			var req = archiveTypeData;
			if (typeof (req) == "undefined" || typeof (req.data) == "undefined") {
				return val;
			} else {
				return req.data[val];
			}
		});
		$.views.converters("CONVERSION_TYPE", function(val) {
			var req = conversionTypeData;
			if (typeof (req) == "undefined") {
				return val;
			} else {
				return req[val];
			}
		});

		function resetForm() {
			document.searchForm.reset();
			var startDate = getTodayDate(-7);
			var endDate = getTodayDate();
			$("#select_syn_start_time").val(startDate + " 00:00:00");
			$("#select_syn_end_time").val(endDate + " 23:59:59");
			$("#tableHead").data("mars", "order.list");
			$("#searchForm").searchData();
		}
		
		function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$(".moreSearch").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
		}
		function showDetail(id,customerName,address,customerTel1,customerTel2,customerTel3){
			showDetailCommon({
				"model":"PeakEnd",
				"url":"/PeakEnd/pages/order/order-list.jsp",
				"action":"acces",
				"describe":"用户査询[峰终回访单查询]数据，査看[{{customerName}}]敏感信息:[用户号码1：{{customerTel1}},用户号码2：{{customerTel2}},用户号码3：{{customerTel3}},地址：{{address}}]"},id,customerName,address,customerTel1,customerTel2,customerTel3);
		}
	</script>
	<script type="text/javascript" src="/iccportal5/static/js/privacyUtil.js"></script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>