<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>申请详情</title>
    <style>
		 .tab-page{margin-right:15px;margin-left:15px;padding:15px;background:#fff}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	    <div class="tab-page">
	          <p>发布时间：<b>${param.TIME }</b></p>
			  <ul class="nav nav-tabs" role="tablist">
				    <li role="presentation" class="active"><a href="#tab-1" aria-controls="tab-1" role="tab" data-toggle="tab" onclick="aF()">申请详情</a></li>
				    <li role="presentation"><a href="#tab-2" aria-controls="tab-2" role="tab" data-toggle="tab" onclick="dF()">分配详情</a></li>
			  </ul>
			  <div class="tab-content pt-10">
			      <div role="tabpanel" class="tab-pane active" id="tab-1">	
					<div class="ibox">
			          <form id="admissionsUserForm"  data-mars="" method="post" data-text-model="true" data-mars-prefix="">
			          	<input class="hidden" value="${param.ID }" name="id1" >  
			          	<div class="ibox-content ">
	              		<div class="row table-responsive">
			               <table class="table table-auto table-bordered table-hover table-condensed text-c" 
			               data-container="dataList1" data-template="list-template1" data-mars="order.applyList" data-container="">
					        	    <thead>
						        			<tr>
						        				<th class="text-c">账号</th>
						        				<th class="text-c">姓名</th>
						        				<th class="text-c">申请时间</th>
									        	<th class="text-c">周期回访数</th>
						        			</tr>
					        		</thead>	
					        		<tbody id="dataList1">
									    
								    </tbody>
						    </table>
						     <script id="list-template1" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:APPLY_ACC}}</td>
											<td>{{:APPLY_NAME}}</td>
											<td>{{:APPLY_TIME}}</td>
											<td>{{:REVISIT_NUM}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
							 </div>
						    	 <div class="row paginate" id="page">
	                     			<jsp:include page="/pages/common/pagination2.jsp">
	                     			<jsp:param value="10" name="pageSize"/>
	                     				</jsp:include>
	                     		</div> 
	                     		</div>
			          </form>
			          </div>
			      </div>
			      <div role="tabpanel" class="tab-pane" id="tab-2">
			            <form id="distributionUserForm"  data-mars="" method="post" data-text-model="true" data-mars-prefix="">
			            	<input class="hidden" name="id2" value="${param.ID }">  
				            	<div class="ibox-content ">
		              				<div class="row table-responsive">
							            <table class="table table-auto table-bordered table-hover table-condensed text-c"
							             data-mars="order.distributeList" 
							          	 data-container="dataList2" data-template="list-template2"  data-container="">
								        	    <thead>
									        			<tr>
									        				<th class="text-c">账号</th>
									        				<th class="text-c">姓名</th>
									        				<th class="text-c">申请时间</th>
									        			    <th class="text-c">任务领取数</th>
									        			</tr>
								        		</thead>
								        		<tbody id="dataList2">
												      
											    </tbody>
									    </table>
									     <script id="list-template2" type="text/x-jsrender">
								                 {{for list}}
										             <tr>
										                	<td>{{:USER_ACC}}</td>
															<td>{{:USER_NAME}}</td>
															<td>{{:CREATE_TIME}}</td>
															<td>{{:RECEIVE_NUM}}</td>
									   				 </tr>
												 {{/for}}					         
										 </script>
								 </div>
								      <div class="row paginate" id="page">
			                     		<jsp:include page="/pages/common/pagination2.jsp">
			                     			<jsp:param value="10" name="pageSize"/>
			                     		</jsp:include>
			                     	</div> 
		                     	</div>
						</form>
			      </div>
			  </div>
	    </div>
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-default"  type="button" onclick="parent.layer.closeAll()">关闭</button>
		</div>
				
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	    $(function(){
	    	$("#admissionsUserForm").render({});
	    });
	    function aF(){
	    	$("#admissionsUserForm").render({});
	    }
	    function dF(){
	    	$("#distributionUserForm").render({});
	    }
	    
	    
	    /*
	    function detailDownloadExl(){
	    	location.href = "${ctxPath}/servlet/revisit?action=detailDownloadExl&id=${param.id}"
	    }*/
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>