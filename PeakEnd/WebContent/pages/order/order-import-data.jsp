<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>名单导入</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form id="importForm" method="post" name="uploadForm" class="form-inline" id="uploadForm" enctype="multipart/form-data">
		<table class="table  table-vzebra mt-10">
			<tbody>
				<tr>
					<td width="60px">Excel文件</td>
					<td><input class="hidden" type="file" id="file" name=file accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel">
						<button class="btn btn-xs btn-info" type="button"
							onclick="$('#file').click()">选择文件</button> <a
						class="btn btn-sm btn-link" href="JavaScript:exc.download()"  >下载导入模板</a>
					</td>
				</tr>
			</tbody>
		</table>
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary" type="button"
				onclick="exc.upload()">保存</button>
			<button class="btn btn-sm btn-default ml-20" type="button"
				id="backbut" onclick="layer.closeAll();">关闭</button>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("exc");
		exc.download=function(){
			location.href ="${ctxPath}/servlet/order?action=download&type=1";
		}
		exc.upload = function() {
			if($("#file").val()!=""){
				var filePath = $('#file').val().toLowerCase().split(".");
			    var fileType =  filePath[filePath.length - 1]; //获得文件结尾的类型如 zip rar 这种写法确保是最后的
			    console.log(fileType);
			    if(!(fileType == "xlsx" )){
			    	layer.alert('文件格式不符合要求(.xlsx)！')
			    	return;
			    }
				$("#importForm").attr("enctype","multipart/form-data");
				var formData = new FormData($("#importForm")[0]); 
				var index = layer.load(1, {shade: [0.1,'#fff']});
				$.ajax({  
			    	url: '${ctxPath}/servlet/order?action=upload&type=1',  
			        type: 'POST',  
			        data: formData,
			        timeout:0,
			        async: true,cache: false,contentType: false,processData: false,  
			        success: function (result) {
						$("#importForm").removeAttr("enctype");
			        	if(result.state == 1){
							layer.closeAll();
							layer.msg(result.msg,{icon: 1});
							reload();
						}else{
							layer.close(index);
							layer.alert(result.msg,{icon:5});
						}
					}
				}); 
			}else{
				layer.alert('请上传文件',{icon:2});
				layer.close(index);
			}
				
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp"%>