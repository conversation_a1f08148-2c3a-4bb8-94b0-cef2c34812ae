<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>现金补偿规则配置</title>
    <style>
        .menuContent {
            display: none;
            position: absolute;
            border: 1px solid rgb(170, 170, 170);
            max-width: 220px;
            max-height: 350px;
            z-index: 10;
            overflow: auto;
            background-color: #f4f4f4
        }

        .wt-75 {
            width: 75%;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="editForm" method="post" autocomplete="off" data-mars="roleCashScriptDao.getRoleCashScript" data-mars-prefix="cashScript.">
        <input type="hidden" id="scripId" name="cashScript.ID" value="${param.id}">
        <table class="table table-edit table-vzebra mt-10">
            <tbody>
            <tr>
                <td>名称</td>
                <td>
                    <input class="form-control input-sm wt-75"
                           name="cashScript.SCRIPT_NAME"
                           id="scriptName"
                           data-rules="required"
                           maxlength="32">
                </td>
            </tr>
            <tr>
                <td>角色</td>
                <td class="remind-content">
                    <select name="cashScript.ROLE_ID"
                            id="roleId"
                            class="form-control input-sm"
                            data-mars="Dict.getRoleList"
                            data-rules="required"
                            data-title="角色"
                            readonly>
                        <option value="">--请选择--</option>
                    </select>
                    <input type="hidden" class="hidden" name="cashScript.ROLE_NAME" id="roleName">
                </td>
            </tr>
            <tr>
                <td>委托方</td>
                <td>
                    <div class="input-group input-group-sm new-input-group wt-75">

                        <select class="form-control input-sm input-sm" name="cashScript.PROD_CODE" id="prodCode"
                                data-rules="required" data-mars="common.sysCode('SYS_CLIENT')">
                            <option value="">-请选择-</option>
                        </select>
                        <input type="hidden" name="cashScript.PROD_NAME" id="prodName"/>
                    </div>
                </td>
            </tr>
            <tr>
                <td width="150px">用户单次现金补偿最大金额</td>
                <td>
                    <div class="input-group-sm">
                        <input type="number"
                               data-rules="digits"
                               id="singleUserMaxAmount"
                               name="cashScript.SINGLE_USER_MAX_AMOUNT"
                               oninput="if(value <= 0 ){layer.msg('只允许为正数',{icon:7});value = ''}"
                               class="form-control input-sm wt-75">
                    </div>
                </td>
            </tr>
            <tr>
                <td>用户累计现金补偿最大金额</td>
                <td>
                    <div class="input-group-sm">
                        <input type="number"
                               data-rules="digits"
                               id="userMonthlyLimit"
                               name="cashScript.USER_MONTHLY_LIMIT"
                               oninput="if(value <= 0 ){layer.msg('只允许为正数',{icon:7});value = ''}" 
                               class="form-control input-sm wt-75"/>
                    </div>
                </td>
            </tr>
            <tr>
                <td>用户累计补偿次数上限</td>
                <td>
                    <div class="input-group-sm">
                        <input type="number"
                               id="currentUserRepetitionCount"
                               data-rules="digits"
                               name="cashScript.CURRENT_USER_REPETITION_COUNT"
                               oninput="if(value <= 0 ){layer.msg('只允许为正数',{icon:7});value = ''}"
                               class="form-control input-sm wt-75">
                    </div>
                </td>
            </tr>
            <tr>
                <td>坐席自然月现金补偿金额上限</td>
                <td>
                    <div class="input-group-sm">
                        <input type="number"
                               id="accMonthlyLimit"
                               data-rules="digits"
                               name="cashScript.ACC_MONTHLY_LIMIT"
                               oninput="if(value <= 0 ){layer.msg('只允许为正数',{icon:7});value = ''}"
                               class="form-control input-sm wt-75">
                    </div>
                </td>
            </tr>
            <tr>
                <td>是否启用</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="cashScript.ENABLED" value="Y"
                                                       checked="checked">启用</label>
                    <label class="radio-inline"><input type="radio" name="cashScript.ENABLED" value="N">禁用</label>
                </td>
            </tr>
            <tr>
                <td>是否扩展服务请求</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="cashScript.ALLOW_ALL_CASH" value="Y"
                                                       checked="checked">启用</label>
                    <label class="radio-inline"><input type="radio" name="cashScript.ALLOW_ALL_CASH" value="N">禁用</label>
                </td>
            </tr>
            </tbody>
        </table>
        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary" type="button" onclick="cashScriptConf.ajaxSubmitForm()">保存</button>
            <button class="btn btn-sm btn-default ml-20" type="button" onclick="popup.layerClose()">取消</button>
        </div>
        <div id="prodMenuContent" class="menuContent">
            <input type="hidden" id="proCodeTreeHidden" data-context-path="/neworder" data-mars="comm.productCode"/>
            <ul id="proCodeTree" class="ztree" style="margin-top:0; width:100%; height:auto;"></ul>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
    <link type="text/css" rel="stylesheet"
          href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css"/>
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css"/>
    <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
    <script type="text/javascript"
            src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>

    <script type="text/javascript">
        jQuery.namespace("cashScriptConf")

        cashScriptConf.ajaxSubmitForm = function () {
            if (!form.validate("#editForm")) {
                return;
            }

            if ($('#singleUserMaxAmount').val() == '') {
                layer.alert('用户单次现金补偿最大金额不能为空');
                return;
            }

            if ($('#userMonthlyLimit').val() == '') {
                layer.alert('用户累计现金补偿最大金额不能为空');
                return;
            }

            if ($('#accMonthlyLimit').val() == '') {
                layer.alert('坐席自然月现金补偿金额上限不能为空');
                return;
            }

            if ($('#currentUserRepetitionCount').val() == '') {
                layer.alert('用户累计补偿次数上限不能为空');
                return;
            }
            let ajaxURL = "${ctxPath}/servlet/roleCashScript?action=cashScriptSave";

            let id = $('#scripId').val()
            if (id) {
                ajaxURL = "${ctxPath}/servlet/roleCashScript?action=cashScriptUpdate";
            }
            var data = form.getJSONObject("editForm");
            ajax.remoteCall(ajaxURL, data, function (result) {
                    if (result.state == 1) {
                        window.parent.layer.closeAll();
                        window.parent.layer.msg(result.msg, {icon: 1});
                        window.parent.cashScriptList.search()

                    } else {
                        layer.alert(result.msg, {icon: 5});
                    }
                }
            );
        }

        cashScriptConf.multiSetting = {
            multiple: false,
            buttonWidth: '75%',
            nonSelectedText: "--请选择--",
            selectAllNumber: false,
            maxHeight: 350,
            enableFiltering: true
        };

        // 加载角色选择框,使用可筛选的控件
        var $roleId = $("#roleId");
        $('#editForm').render({
            success: function (res) {

                $('#roleId').multiselect(cashScriptConf.multiSetting);
                // 将下拉菜单宽度设为100%
                $("ul.multiselect-container.dropdown-menu").eq(0).css("width", "100%");

                $roleId.on('change', function () {
                    // 获取选中的选项的文本值
                    const selectedOptionText = $roleId.find('option:selected').text();
                    // 将选中的选项的文本值赋给 roleName 元素的值
                    $('#roleName').val(selectedOptionText);
                });

                $("#prodCode").on('change', function () {
                    // 获取选中的选项的文本值
                    const selectedOptionText = $("#prodCode").find('option:selected').text();

                    // 将选中的选项的文本值赋给 roleName 元素的值
                    $('#prodName').val(selectedOptionText);
                })
            }
        })
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
