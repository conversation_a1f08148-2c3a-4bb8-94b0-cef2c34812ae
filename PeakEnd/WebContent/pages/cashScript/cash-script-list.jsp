<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>坐席补偿上限配置</title>
    <style>
        #scriptListData th {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        #scriptListData td {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form name="cashScriptListForm" class="form-inline" id="cashScriptListForm" method="post" onsubmit="return false"
          autocomplete="off">
        <div class="ibox">
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <h5><span class="glyphicon glyphicon-list"></span> 现金补偿规则查询</h5>
                </div>
                <hr style="margin:5px -15px">
                <input type="hidden" name="realPageIndex" id="hid_pageIndexV"/>
                <div class="form-group">
                    <div class="input-group width-36">
                        <span class="input-group-addon">角色</span>
                        <select name="ROLE_ID"
                                id="roleId"
                                class="form-control input-sm"
                                data-mars="Dict.getRoleList"
                                readonly>
                            <option value="">--请选择--</option>
                        </select>
                    </div>

                    <select name="enabled"
                            id="enabled"
                            class="form-control input-sm"
                            style="display: none">
                        <option value="Y">启用</option>
                        <option value="N">禁用</option>
                    </select>
                    <div class="input-group width-36" style="display: none">
                        <span class="input-group-addon">是否</span>
                        <select class="form-control input-sm width-70" name="sf_yn" id="sf_yn" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('SF_YN')">
                            <option value="" selected="selected">--请选择--</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group input-group-sm pull-right mr-10">
                        <button type="button" class="btn btn-sm btn-success" onclick="cashScriptList.add()"> +新增
                        </button>
                    </div>
                    <div class="input-group input-group-sm pull-right ml-10">
                        <button type="button" class="btn btn-sm btn-default" onclick="cashScriptList.search()">
                            <span class="glyphicon glyphicon-search"></span> 搜索
                        </button>
                    </div>
                </div>
            </div>
            <div class="ibox-content ">
                <div class="row table-responsive">
                    <table style="border: 0px solid #fff;" class="table table-bordered" id="cashScriptListData">
                        <thead>
                        <tr>
                            <th class="text-c">序号</th>
                            <th class="text-c">名称</th>
                            <th class="text-c">角色</th>
                            <th class="text-c">委托方</th>
                            <th class="text-c">用户单次现金补偿最大金额</th>
                            <th class="text-c">用户累计现金补偿最大金额</th>
                            <th class="text-c">用户累计补偿次数上限</th>
                            <th class="text-c">坐席自然月现金补偿金额上限</th>
                            <th class="text-c">是否启用</th>
                            <th class="text-c">是否扩展服务请求</th>
                            <th class="text-c">操作</th>
                        </tr>
                        </thead>
                        <tbody id="dataList">

                        </tbody>
                        <script id="list-template" type="text/x-jsrender">
                            {{for list}}
                                 <tr>
                                      <td class="text-c">{{:#index+1}}</td>
                                      <td class="text-c">{{:SCRIPT_NAME}}</td>
                                      <td class="text-c">{{:ROLE_NAME}}</td>
                                      <td class="text-c">{{:PROD_NAME}}</td>
                                      <td class="text-c">{{:SINGLE_USER_MAX_AMOUNT}}</td>
                                      <td class="text-c">{{:USER_MONTHLY_LIMIT}}</td>
                                      <td class="text-c">{{:CURRENT_USER_REPETITION_COUNT}}</td>
                                      <td class="text-c">{{:ACC_MONTHLY_LIMIT}}</td>
                                      <td class="text-c">{{getText:ENABLED 'enabled'}}</td>
                                      <td class="text-c">{{getText:ALLOW_ALL_CASH 'sf_yn'}}</td>
                                      <td class="text-c">
                                           <a href="javascript:cashScriptList.add('{{:ID}}')">编辑</a>
                                           <a href="javascript:cashScriptList.del('{{:ID}}')">删除</a>
                                      </td>
                                 </tr>
                            {{/for}}

                        </script>
                    </table>
                </div><!--  row table-responsive -->
                <div class="row paginate" id="page">
                    <jsp:include page="/pages/common/pagination.jsp">
                        <jsp:param value="10" name="pageSize"/>
                    </jsp:include>
                </div>
            </div>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <link type="text/css" rel="stylesheet"
          href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css"/>
    <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
    <script type="text/javascript"
            src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
    <script type="text/javascript">
        jQuery.namespace("cashScriptList");

        $(function () {
            $("#cashScriptListData").attr("data-mars", "roleCashScriptDao.getRoleCashScriptList");
            cashScriptList.search();
        });

        cashScriptList.search = function () {
            $("#cashScriptListForm").searchData();
        }

        cashScriptList.add = function (id) {
            let title = "新增现金补偿规则";
            let url = "${ctxPath}/pages/cashScript/cash-script-edit.jsp";
            if (id) {
                title = "编辑现金补偿规则";
                url += "?id=" + id;
            }

            popup.layerShow({
                type: 2,
                title: title,
                // area: ['930px', '750px'],
                area: ['900px', '800px'],
                offset: '60px'
            }, url);
        }

        cashScriptList.del = function (id) {
            if (confirm("确认要删除吗？")) {
                ajax.remoteCall("${ctxPath}/servlet/roleCashScript?action=cashScriptDelete", {id: id}, function (result) {
                    if (result.state == 1) {
                        layer.msg(result.msg, {icon: 1});
                        cashScriptList.search();
                    } else {
                        layer.alert(result.msg, {icon: 5});
                    }
                });
            }
        }

        cashScriptList.multiSetting = {
            multiple: false,
            nonSelectedText: "--请选择--",
            selectAllNumber: false,
            maxHeight: 350,
            enableFiltering: true
        };

        // 加载角色选择框,使用可筛选的控件
        $("#roleId")
            .render({
                success: function (result) {
                    $('#roleId').multiselect(cashScriptList.multiSetting);
                }
            });
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
