	<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>使用明细</title>
	<style>
		.info-title p:before {
		    content: '';
		    position: absolute;
		    background-color: #337ab7;
		    left: -1px;
		    height: 15px;
		    width: 3px;
		    top: 50%;
		    margin-top: -5px;
		 }
		.info-title p {
		    border-bottom: none;
		    position: relative;
		    height: 35px
		 }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	   <form class="ibox-content" id="recordForm" style="margin:15px" data-mars="orgIntergalDao.getOrgIntergalSummary" method="post" data-text-model="true" data-mars-prefix="">
	        <input type="hidden" name="intergalId" value="${param.ID }">
	        <input  type="hidden"  id="orgCode" data-cust-mars="comm.sysCode('ORG_CODE')" data-cust-context-path="/neworder">     
	        <div class="info-title">
		  		<p style="width:100%">汇总（更新时间：<span name="updateTime" style="color:green"></span>）</p>
		    </div>
		    <table class="table table-edit">
                   <tbody>
                   		<tr>
	                        <td class="text-r">事业部</td>
	                        <td><span id="orgName"></span></td>
	                        <td class="text-r">提醒</td>
	                        <td><span name="warnCount">0</span></td>
	                     	<td class="text-r">金额有效时间</td>
	                        <td colspan="3"><span name="validBeginTime"></span> ~ <span name="validEndTime"></span></td>
	                     </tr>
	                     <tr>
	                     	<td class="text-r">总额</td>
	                        <td><h2 name="limimtAount"></h2></td>
	                        <td class="text-r">剩余</td>
	                        <td><h2 name="suprAmount" style="color:red"></h2></td>
	                        <td class="text-r">阈值</td>
	                        <td><h2 name="thresholdAmount"></h2></td>
	                        <td class="text-r">已使用</td>
	                        <td><h2 name="useAmount"></h2></td>
	                     </tr>
                  </tbody>
            </table>
	        <div class="info-title">
		  		<p>使用明细</p>
		    </div>
		 	<div class="table-responsive">
		    <table class="table table-auto table-bordered table-hover table-condensed text-c" data-mars="compensateRecordDao.recordList" data-template="record-list-template" data-container="#recordList">
	       	    <thead>
	        			<tr>
	        				<th class="text-c">序号</th>
	        				<th class="text-c">工单id</th>
	        				<th class="text-c">补偿类型</th>
	        				<th class="text-c">补偿方式</th>
	        				<th class="text-c">商品id/卡券id</th>
	        				<th class="text-c">卡编号/商品编号</th>
	        				<th class="text-c">卡券名称/商品名称</th>
	        				<th class="text-c">卡券金额/商品金额</th>
	        				<th class="text-c">是否使用/是否收货</th>
	        				<th class="text-c">备注</th>
	        				<th class="text-c">坐席</th>
	        				<th class="text-c">赠送时间</th>
	        				<th class="text-c">用户手机号</th>
	        				<th class="text-c">用户姓名</th>
	        				<th class="text-c">地址</th>
	        				<th class="text-c">区域</th>
	        				<th class="text-c">产品品类</th>
	        			</tr>
	       		</thead>
	       		<tbody id="recordList">
				      <script id="record-list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
			                             	<td>{{:#index+1}}</td>
											<td>{{:ORDER_ID}}</td>
											<td>{{dictFUN:COMPENSATE_TYPE "PEAKEND_COMPENSATE_TYPE"}}</td>
											<td>{{dictFUN:COMPENSATE_MODE "PEAKEND_COMPENSATE_MODE"}}</td>
											<td>{{:COMPENSATE_ID}}</td>
											<td>{{:COMPENSATE_NO}}</td>
											<td>{{:COMPENSATE_NAME}}</td>
											<td>{{:COMPENSATE_AMOUNT}}</td>
											<td>{{:ACTIVATION_FLAG}}</td>
											<td>{{:CONTENT}}</td>
											<td>{{:CREATE_ACC}}</td>
											<td>{{:CREATE_TIME}}</td>
											<td>{{:PHONE}}</td>
											<td>{{:CUSTOMER_NAME}}</td>
											<td>{{:ADDRESS}}</td>
											<td>{{:AREA_NAME}}</td>
											<td>{{:PROD_NAME}}</td>
									    </tr>
								   {{/for}}					         
					 </script>
	   			</tbody>
	        </table>
	        </div>
	        <div class="row paginate" id="page">
            		<jsp:include page="/pages/common/pagination.jsp">
            			<jsp:param value="10" name="pageSize"/>
            		</jsp:include>
            </div> 
	   </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript">
		jQuery.namespace("record");
		$(function(){
			$("#recordForm").render({success:function(result){
				pageLoader.init('#pageLoader');
				if(result["orgIntergalDao.getOrgIntergalSummary"]){
					var orgCode = result["orgIntergalDao.getOrgIntergalSummary"].data.orgCode;
					var sysCodeData = getSysCodeDatas("ORG_CODE");
					$("#orgName").text(sysCodeData[sysCodeControls].data[orgCode]);
				}
			}});

			$(".ibox-panel-title").css("margin-left","15px").css("margin-right","15px");
		    $(".container-fluid").css("padding-left","0px").css("padding-right","0px");
		})
		
		$.views.converters("RECORD_PLAY", function(val) {
			if(val){
				return "<a href='javascript:detail.recordPlay(\""+val+"\");'>播放</a>"
			}else{
				return '';
			}
		});
		
		record.recordPlay = function(sessionId){
			if(sessionId){
				var data ={id:sessionId}
				ajax.remoteCall("${ctxPath}/servlet/revisit?action=getTel",data,function(result) { 
						if(result.state == 1){
							var called=result.data.called;
							var iccBusiId=result.data.icc_busi_id;
					    	popup.layerShow({
					    		type:1,
					    		title:'播放录音('+called+')',
					    		offset:'20px',area:['640px','280px'],
					    		end: function(){ 
									  player.pause();
									  return false; 
									},
					    		shade:0,maxmin: true,move:'.mine-move'
					    	}
					    	,"/PerformExamWeb/pages/record/record-play.jsp",{vid: iccBusiId,luryin:true});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					}
				);
			}else{
				layer.alert("录音会话id为空",{icon:5});
			}
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>