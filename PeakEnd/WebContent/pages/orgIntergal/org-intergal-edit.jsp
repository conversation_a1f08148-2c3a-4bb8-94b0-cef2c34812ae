<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>事业部金额配置</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars="orgIntergalDao.getOrgIntergalConfigDetail"  method="post"  autocomplete="off" data-mars-prefix="orgIntergal." >
			<input class="hidden" value="${param.ID }" name="orgIntergal.ID">
				  <table class="table table-edit table-vzebra mt-10" >
	                    <tbody>
		                     <tr>
			                        <td>事业部</td>
			                        <td>
										  <select data-rules="required"  class="form-control input-sm width-70" name="orgIntergal.ORG_CODE" id = "orgCode" data-cust-context-path="/neworder" data-cust-mars="comm.sysCode('ORG_CODE')" data-mars-top="true" >
                                          		<option value="" selected="selected">--请选择--</option>
                                      	  </select>			                        
                                    </td>
			                        <td>是否有效</td>
			                        <td>
			                            <label class="radio-inline"><input type="radio" name="orgIntergal.STATUS"  value="Y" checked="checked">是</label>
			                            <label class="radio-inline"><input type="radio" name="orgIntergal.STATUS" value="N" >否</label>
			                        </td>
		                     </tr>
		                     <tr>
		                     		<td width="40px">金额上限</td>
			                        <td><input type="number" data-rules="digits|required" name="orgIntergal.INTEGRAL_LIMIT" class="form-control input-sm" ></td>
		                     		<td width="40px">阈值</td>
			                        <td><input type="number" data-rules="digits|required" name="orgIntergal.INTEGRAL_THRESHOLD" class="form-control input-sm"></td>
			                 </tr>
			                 <tr> 
			                        <td>金额有效开始时间</td>
			                        <td><input type="text" data-rules="required" onFocus="WdatePicker({onpicked:function(){this.blur();},maxDate:'#F{$dp.$D(\'validEndTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" name="orgIntergal.VALID_BEGIN_TIME" id="validBeginTime" class="form-control input-sm"   ></td>
			                        <td>金额有效结束时间</td>
			                        <td> <input type="text" data-rules="required"  ID="validEndTime" name="orgIntergal.VALID_END_TIME" class="form-control input-sm"  onclick="WdatePicker({onpicked:function(){;this.blur();},minDate:'#F{$dp.$D(\'validBeginTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})"></td>
		                     </tr>
		                     <tr>
			                        <td>开启提醒</td>
			                        <td>
			                            <label class="radio-inline"><input type="radio" name="orgIntergal.OPEN_WARN"  value="Y" checked="checked">是</label>
			                            <label class="radio-inline"><input type="radio" name="orgIntergal.OPEN_WARN" value="N" >否</label>
			                        </td>
		                     		<td width="40px">提醒人邮箱</td>
			                        <td><input type="text" name="orgIntergal.WARN_EMAIL" class="form-control input-sm"></td>
			                 </tr>
		                     <tr>
		                            <td>提醒内容</td>
			                        <td colspan="3"><textarea name="orgIntergal.WARN_CONTENT" maxlength="600"  class="form-control input-sm" rows="16"></textarea></td>
			                 </tr>
	                   </tbody>
	              </table>
				  <div class="layer-foot text-c">
				   		<button class="btn btn-sm btn-primary"  type="button" onclick="orgIntergalConfig.ajaxSubmitForm()">保存</button>
				   		<button class="btn btn-sm btn-default ml-20"  type="button"  onclick="popup.layerClose()">取消</button>
				  </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript">
	    jQuery.namespace("orgIntergalConfig")
	    $(function() {
	    	$("#editForm").render({success : function(result){
	    		var result = result["orgIntergalDao.getOrgIntergalConfigDetail"];
	    		if(result&&result.data.ORG_CODE){
	    			setTimeout(function(){
		    			$("#orgCode").val(result.data.ORG_CODE);
	    			},3000);
	    		}
			}}); 
		});
	    orgIntergalConfig.ajaxSubmitForm = function(){
			if(!form.validate("#editForm")){
				return;
			};
			
			 var data = form.getJSONObject("editForm");
				ajax.remoteCall("${ctxPath}/servlet/orgIntergal?action=orgIntergalSave",data,function(result) { 
						if(result.state == 1){
							window.parent.layer.closeAll();
							window.parent.layer.msg(result.msg,{icon: 1});
							window.parent.orgIntergalList.search()

						}else{
							layer.alert(result.msg,{icon: 5});
						}
					}
				);
	 	}
	    
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>