<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>事业部金额配置列表</title>
	<style>
	    #orgIntergalListData th{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
	    #orgIntergalListData td{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form name="orgIntergalListForm" class="form-inline" id="orgIntergalListForm" method="post" onsubmit="return false" autocomplete="off">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 事业部金额配置查询</h5>
	             		 </div>
	             		 <hr style="margin:5px -15px">
	             		 <input type="hidden" name = "realPageIndex" id="hid_pageIndexV" />
	             		 <div class="form-group">
							   <div class="input-group width-18">
								      <span class="input-group-addon" style="width:30%">事业部</span>	
									  <select class="form-control input-sm width-70" name="orgCode" id = "orgCode"  data-cust-context-path="/neworder" data-cust-mars="comm.sysCode('ORG_CODE')" >
                                          <option value="" selected="selected">--请选择--</option>
                                      </select>
							   </div>
							   <div class="input-group width-36">
							    	  <span class="input-group-addon">提醒人邮箱</span>	
			                          <input type="text" name="warnEmail" class="form-control input-sm" >
							   </div>
							   <div class="input-group width-36">
							    	  <span class="input-group-addon">是否有效</span>	
								        <select class="form-control input-sm width-70" name="status" id="status">
                                          <option value="" selected="selected">--请选择--</option>
                                          <option value="Y">是</option>
                                          <option value="N">否</option>
                                      </select>
							   </div>
							   <div class="input-group width-36">
							    	  <span class="input-group-addon">是否开启提醒</span>	
								        <select class="form-control input-sm width-70" name="openWarn" id="openWarn">
                                          <option value="" selected="selected">--请选择--</option>
                                          <option value="Y">是</option>
                                          <option value="N">否</option>
                                      </select>
							   </div>
						  </div>
						  <div class="form-group">
							   <div class="input-group input-group-sm pull-left">
									  <button type="button" class="btn btn-sm btn-success mr-10" onclick="orgIntergalList.reset()"><span class="glyphicon glyphicon-repeat"></span> 重置</button>
							   </div>
							   <div class="input-group input-group-sm pull-right mr-10">
									  <button type="button" class="btn btn-sm btn-success" onclick="orgIntergalList.add()"> +新增</button>
							   </div>
						 	   <div class="input-group input-group-sm pull-right ml-10">
									  <button type="button" class="btn btn-sm btn-default" onclick="orgIntergalList.search()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
						  </div>
             	    </div> 
	              	<div class="ibox-content ">
	              		<div class="row table-responsive" ><!-- style="max-height:700px;" -->
	              		  <table style="border: 0px solid #fff;" class="table table-bordered" id="orgIntergalListData" data-mars="orgIntergalDao.orgIntergalConfigList">
	              		  		<thead>
	                         	 <tr>
	                         	      <th class="text-c" >序号</th>
	                         	      <th class="text-c" >事业部</th>
								      <th class="text-c" >金额上限</th>
								      <th class="text-c" >阈值</th>
								      <th class="text-c" >金额有效开始时间</th>
								      <th class="text-c" >金额有效结束时间</th>
								      <th class="text-c" >提醒人邮箱</th>
								      <th class="text-c" style="width:300px;max-width:400px;" >提醒内容</th>
								      <th class="text-c" >提醒次数</th>
								      <th class="text-c" >开启提醒</th>
								      <th class="text-c" >是否有效</th>
								      <th class="text-c" >创建账号</th>
								      <th class="text-c" >创建时间</th>
								      <th class="text-c" >更新账号</th>
								      <th class="text-c" >更新时间</th>
								      <th class="text-c">操作</th>
		   						 </tr>
                             </thead>
							 <tbody id="dataList">
							 
							 </tbody>
							 <script id="list-template" type="text/x-jsrender">
												   {{for list}}
														<tr>
			                             					 <td>{{:#index+1}}</td>
															 <td class="text-c">{{sysCodeFUN:ORG_CODE "ORG_CODE"}}</td>
															 <td class="text-c">{{:INTEGRAL_LIMIT}}</td>
															 <td class="text-c">{{:INTEGRAL_THRESHOLD}}</td>
															 <td class="text-c">{{:VALID_BEGIN_TIME}}</td>
															 <td class="text-c">{{:VALID_END_TIME}}</td>
															 <td class="text-c">{{:WARN_EMAIL}}</td>
															 <td class="text-c" style="width:300px;max-width:400px;"><p title="{{:WARN_CONTENT}}">{{:WARN_CONTENT}}</p></td>
															 <td class="text-c">{{:WARN_COUNT}}</td>
															 <td class="text-c">{{getText:OPEN_WARN 'openWarn'}}</td>
															 <td>
																{{getText:STATUS 'status'}}
															 </td>
															 <td class="text-c">{{:CREATE_ACC}}</td>
															 <td class="text-c">{{:CREATE_TIME}}</td>
															 <td class="text-c">{{:UPDATE_ACC}}</td>
															 <td class="text-c">{{:UPDATE_TIME}}</td>
															 <td>
                                          						<a href="javascript:orgIntergalList.showUseDetail('{{:ID}}')">查看使用明细</a>&nbsp;
                                          						<a href="javascript:orgIntergalList.edit('{{:ID}}')">编辑</a>&nbsp;
                                          						<a href="javascript:orgIntergalList.del('{{:ID}}')">删除</a>
                                      						 </td>
														</tr>
												   {{/for}}
																		 
							</script>
	              		  </table>
	              		</div><!--  row table-responsive -->
	                    <div class="row paginate" id="page">
	                    		<jsp:include page="/pages/common/pagination.jsp">
	                    			<jsp:param value="10" name="pageSize"/>
	                    		</jsp:include>
	                    </div> 
	                 </div>
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type ="text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type = "text/javascript">
		jQuery.namespace("orgIntergalList");
	    requreLib.setplugs("wdate");
	    $(function(){
	    	orgIntergalList.init();
	    	$("#orgIntergalListForm").render({success : function(result){
				orgIntergalList.search();
	    	}});
	    });
		$('#orgCode').render({});

	    orgIntergalList.init = function() {
    		$("#createTimeStar").val(getTodayDate(-7)+" 00:00:00");
	    	$("#createTimeEnd").val(getTodayDate()+" 23:59:59");
	    }
	    orgIntergalList.search = function(){
    		$("#orgIntergalListForm").searchData();
	    }
	    
	    /*重置*/
		orgIntergalList.reset = function() {
			document.orgIntergalListForm.reset();
			$("#createTimeStar").val(getTodayDate(-7)+" 00:00:00");
	    	$("#createTimeEnd").val(getTodayDate()+" 23:59:59");
		}
	    
		orgIntergalList.add = function(){
		    popup.layerShow({type:2,title:'新增维护',area:['830px','650px'],offset:'20px'},"${ctxPath}/pages/orgIntergal/org-intergal-edit.jsp");
		}
		
		orgIntergalList.edit = function(id){
		    popup.layerShow({type:2,title:'编辑维护',area:['830px','650px'],offset:'20px'},"${ctxPath}/pages/orgIntergal/org-intergal-edit.jsp",{ID:id});
		}
		
		orgIntergalList.showUseDetail = function(id){
		    popup.layerShow({type:2,title:'使用明细',area:['1130px','750px'],offset:'20px'},"${ctxPath}/pages/orgIntergal/org-intergal-record.jsp",{ID:id});
		}
		
		orgIntergalList.del=function(id){
			var ids=[id];
			if(confirm("删除将丢失该配置的明细关联，确认要删除吗？")){
				ajax.remoteCall("${ctxPath}/servlet/orgIntergal?action=orgIntergalDelete",{ids:ids},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1});
						orgIntergalList.search();
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			} 
		}
		
		function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$(".moreSearch").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon glyphicon-menu-up")
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>