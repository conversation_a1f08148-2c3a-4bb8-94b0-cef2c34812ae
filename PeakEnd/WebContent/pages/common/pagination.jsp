<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
	<div class="_pagination" style="display: inline-block;position: relative;float: left;margin-left: 15px;">
			<input type="hidden" name="pageIndex" class="pageIndexV" value="-1">
			<input type="hidden" class="totalPageV" value="0">
			<input type="hidden" class="totalRowV" value="0">
			<c:set var="pageSize" value="${param.pageSize}"></c:set>
			<c:choose>
				<c:when test="${empty param.pageSizes}">
					<c:set var="pageSizes" value="5,10,15,25,50,100"></c:set>
				</c:when>
				<c:otherwise>
					<c:set var="pageSizes" value="${param.pageSizes}"></c:set>
				</c:otherwise>
			</c:choose>
			<c:if test="${empty param.pageSize }">
					<c:set var="pageSize" value="15"></c:set>
			</c:if>
			<div style="display: inline-block;margin-right: 10px;">跳转至 <input class="form-control input-sm jumpPageNo" type="number" style="width: 70px;"/> 页</div>
			 <select name="pageSize" class="form-control input-sm pageSizeV" style="width: 100px;display: inline-block;height: 28px;padding: 2px 5px">
					<c:forEach items="${fn:split(pageSizes,',')}"  var="val" varStatus="vs">
	                         <option value="${val}"  <c:if test="${pageSize==val}">selected="selected"</c:if>>${val} 条/页</option>
	                </c:forEach>
			</select>&nbsp;
			 共 <span class="totalRow"></span>&nbsp;条 ,&nbsp;<span class="totalPage"></span> 页
	</div>
	 <ul class="pagination pagination-sm pageNumV"  style="margin: 0px"></ul>
