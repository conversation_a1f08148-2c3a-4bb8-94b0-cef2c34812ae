<template>
  <!-- <el-badge :value="goodItemNum" class="item"> -->
  <div class="gooditemContainer" :class="{ choose: goodItemNum }">
    <div class="num" v-show="goodItemNum">{{ goodItemNum }}</div>
    <div class="shadow" v-if="goodItemNum"></div>
    <div class="img-container">
      <img :src="item.main_pic_infos[0].pic_url" alt="" srcset="" />
    </div>
    <div class="goodName">
      {{ item.dis_sku_title }}
    </div>
    <div class="detail">
      <div class="price">
        <span style="font-size: 12px">￥</span>
        <span>{{ item.sale_price }}</span>
      </div>
      <div class="btns">
        <el-button
          circle
          size="mini"
          icon="el-icon-minus"
          style="margin-right: 5px"
          @click="handleReduce"
        ></el-button>
        <el-button
          circle
          size="mini"
          icon="el-icon-plus"
          @click="handleIncrease"
        ></el-button>
      </div>
    </div>
  </div>
  <!-- </el-badge> -->
</template>

<script>
module.exports = {
  props: ["item"],
  data() {
    return {
      goodItemNum: "",
    };
  },
  methods: {
    handleReduce() {
      this.goodItemNum--;
      if (this.goodItemNum < 1) {
        this.goodItemNum = "";
        this.$emit("updategoodselected", this.item, this.goodItemNum, "del");
      } else {
        this.$emit("updategoodselected", this.item, this.goodItemNum, "add");
      }
    },
    handleIncrease() {
      if (this.goodItemNum === "") {
        this.goodItemNum = 1;
      } else {
        this.goodItemNum++;
      }
      this.$emit("updategoodselected", this.item, this.goodItemNum, "add");
    },
  },
};
</script>

<style scoped>
.gooditemContainer {
  position: relative;
  width: 140px;
  height: 170px;
  padding: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  transition: all 0.2s;
  cursor: pointer;
}
.gooditemContainer:hover {
  border: 1px solid rgba(0, 0, 0, 0.2);
}
.gooditemContainer.choose:hover {
  border-color: #f5b3b3;
}
.img-container {
  width: 130px;
  height: 100px;
}
.img-container img {
  width: 100%;
  height: 100%;
  /* object-fit: cover; */
}
.detail {
  position: absolute;
  display: flex;
  bottom: 5px;
  left: 0px;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  padding: 0px 10px;
  font-size: 14px;
}
.detail .price {
  display: flex;
  align-items: center;
  color: orange;
}
.detail .btns {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.btns .el-button {
  margin: 0;
}
.goodName {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-break: break-all;
  color: black;
}
.choose {
  position: relative;
  /* background-color: #f8ebeb; */
  /* background-color: #ebf5f8; */
  border-color: #f0b3b3;
  /* border-radius: 0px; */
}
.shadow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 5px;
}
.num {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 25px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  border-radius: 50%;
  color: white;
  background-color: #f46c6c;
  z-index: 9999;
}
</style>
