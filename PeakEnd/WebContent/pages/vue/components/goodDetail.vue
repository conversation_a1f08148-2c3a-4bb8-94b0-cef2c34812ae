<template>
  <el-dialog
    title="物流详情"
    :visible.sync="dialogvis"
    :fullscreen="true"
    :before-close="handleClose"
    class="goodDetail"
    v-loading="loading"
  >
    <el-tabs
      v-if="detailvolist.length"
      class="content"
      v-model="activeName"
      @tab-click="handleClick"
    >
      <el-tab-pane
        v-for="(item, index) in detailvolist"
        :label="`包裹${index + 1}`"
        :name="index + 1 + ''"
      >
        <div class="googDetails">
          <div class="img">
            <!-- <img :src="gooddetails.main_pic_infos[0].pic_url" alt="" /> -->
          </div>
          <div class="googDetails-info flex-col-evenly">
            <span class="googDetails-name fs16 colorBlack fs-b">{{
              item.disSkuTitle
            }}</span>
            <div class="googDetails-orderNo googDetails-item">
              <span class="color86">补偿单号: </span>
              <span class="colorBlack">{{
                good.compensateOrderNo || "无"
              }}</span>
            </div>
            <div class="googDetails-dealId googDetails-item">
              <span class="color86">订单编号: </span>
              <span class="colorBlack">{{ item.dealId || "无" }}</span>
            </div>
            <div class="googDetails-giveTime googDetails-item">
              <span class="color86">赠送时间: </span>
              <span class="colorBlack">{{
                good.pubCreateDate ? timestampToDate(good.pubCreateDate) : "无"
              }}</span>
            </div>
            <div class="googDetails-salePrice googDetails-item">
              <span class="color86">金额: </span>
              <span class="colorBlack">{{ item.salePrice || "无" }}</span>
            </div>
            <div class="googDetails-purchaseNum googDetails-item">
              <span class="color86">单量: </span>
              <span class="colorBlack">{{ item.purchaseNum || "无" }}</span>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <div v-else class="content fs-b fs16 color86">暂无包裹</div>
    <div class="content timelineContainer">
      <div class="expressCompany" v-if="gooddetails[0].dvolist">
        <div class="info flex-col-evenly" ref="copyContent">
          <span class="companyName fs16">{{
            "物流公司：" +
            (gooddetails[0].kdiooHvo.comName
              ? gooddetails[0].kdiooHvo.comName
              : "无")
          }}</span>
          <div class="num fs16">
            <span class="">运单编号：</span>
            <span class="code color11" ref="code">{{
              gooddetails[0].kdiooHvo.nu
            }}</span>
            <span class="copyBtn color11" @click="handleCopy">复制</span>
          </div>
        </div>
      </div>
      <!-- <ul class="timeline">
        <li
          class="timeline-item"
          v-for="(activity, index) in activities"
          :key="index"
        ></li>
      </ul> -->
      <el-timeline class="timeline" v-if="gooddetails[0].dvolist">
        <el-timeline-item
          v-for="(activity, index) in gooddetails[activeName - 1].dvolist"
          :key="index"
          :size="activity.size"
          :type="activity.type"
          :color="index === 0 ? '#10a8ee' : ''"
          placement="top"
          :hide-timestamp="true"
        >
          <div class="timestamp color86" :class="{ color11: index === 0 }">
            <!-- <span class="status fs16 fs-b">已签收</span> -->
            <span class="time">{{ activity.actDateStr }}</span>
          </div>
          <span class="color86" :class="{ color11: index === 0 }">{{
            activity.describe
          }}</span>
        </el-timeline-item>
      </el-timeline>
      <div class="recipients">
        <div class="status color11">收</div>
        <div class="info flex-col-evenly">
          <span class="name fs16 colorBlack">{{ good.customerName }}</span>
          <div class="detail fs12">
            <span class="phone color86">{{ good.customerPhone }}</span>
            <span class="address color86">{{ good.customerAddress }}</span>
          </div>
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <!-- <el-button type="primary" @click="handleClose">确 定</el-button> -->
    </span>
  </el-dialog>
</template>

<script>
module.exports = {
  props: ["dialogvis", "gooddetails", "detailvolist", "good", "loading"],
  data() {
    return {
      activeName: "1",
    };
  },
  methods: {
    handleClose() {
      this.$emit("closedialog");
    },
    // tabs点击回调
    handleClick(tab, event) {},
    // 时间戳转为时间
    timestampToDate(Timestamp) {
      createTime = new Date(Timestamp);
      console.log(createTime, "Timestamp");
      (y = createTime.getFullYear()),
        (m = createTime.getMonth() + 1),
        (d = createTime.getDate());
      return (
        y +
        "-" +
        (m < 10 ? "0" + m : m) +
        "-" +
        (d < 10 ? "0" + d : d) +
        " " +
        createTime.toTimeString().substr(0, 8)
      );
    },
    // 复制
    handleCopy() {
      /* 获取文本内容 */
      var copyContent = this.$refs.copyContent;
      console.log(copyContent, "copyText");

      //   /* 选择复制内容 */
      //   copyText.select();
      //   copyText.setSelectionRange(0, 99999); /* 为移动设备设置 */
      var copyVal = copyContent.innerText;
      var reg1 = new RegExp(" 复制", "g"); // 加'g'，删除字符串里所有的"a"
      var val = copyVal.replace(reg1, "");

      /* 复制内容到文本域 */
      //   navigator.clipboard.writeText(copyText.innerText);
      navigator.clipboard.writeText(val);

      /* 弹出已复制的内容 */
      this.$message.success("复制成功");
    },
  },
  mounted() {
    if (this.gooddetails) {
      this.gooddetails.dvolist[0].color = "#10a8ee";
      this.gooddetails.dvolist[0].curr = true;
    }
  },
};
</script>

<style scoped>
.goodDetail .el-dialog {
  background-color: #f2f4f7;
}
.content {
  padding: 16px;
  background-color: #ffffff;
  border-radius: 5px;
}
.timelineContainer {
  margin-top: 15px;
  padding: 0px;
}
.expressCompany {
  display: flex;
  align-items: center;
  height: 50px;
  padding: 15px;
}
.expressCompany .img {
  width: 50px;
  height: 50px;
  margin-right: 10px;
}
.expressCompany .img img {
  width: 100%;
  height: 100%;
}
.expressCompany .info {
  height: 50px;
}
.copyBtn {
  cursor: pointer;
}

.timeline {
  width: 100%;
  padding-top: 22px;
  padding-left: 16px;
  border-top: 1px solid #e8e8e8;
  border-bottom: 1px solid #e8e8e8;
}
.timestamp {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.timestamp .status {
  margin-right: 5px;
}
.recipients {
  display: flex;
  align-items: center;
  height: 50px;
  padding: 15px;
}
.recipients .status {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  line-height: 50px;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  background-color: #e5f4fb;
  margin-right: 10px;
}
.recipients .info {
  height: 50px;
}
.googDetails-info {
  height: auto;
}
.googDetails-item {
  margin-top: 5px;
}
/* 修改elementui样式 */
.el-dialog__header {
  padding-bottom: 5px;
}
.el-dialog__body {
  border: 0;
}
.el-timeline-item .el-timeline-item__tail {
  border-color: #e4e7ed !important ;
}
.el-tabs--top {
  padding-top: 0px;
}
/* 公共样式 */
.color86 {
  color: #868686;
}
.color11 {
  color: #10a8ee;
}
.fs-b {
  font-weight: bold;
}
.colorBlack {
  color: black;
}
.fs12 {
  font-size: 12px;
}
.fs14 {
  font-size: 14px;
}
.fs16 {
  font-size: 16px;
}
.fs20 {
  font-size: 20px;
}
.flex-col-evenly {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
</style>
