<!DOCTYPE html>
<html>

<head>
    <title>坐席赠送记录</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport"
        content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <!-- 基础的 css js 资源 -->
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css" />
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0" />
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0" />
    <style>
        .vue-box {
            overflow: scroll;
            height: auto;
        }
        .flex {
            height: auto;
        }
        .drawer-content .el-date-editor {
            width: 100%;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header .btns .el-button {
            padding: 10px 30px;
        }

        .search-form .el-row {
            margin-bottom: 10px;
        }

        .btn {
            cursor: pointer;
        }

        .el-table_1_column_25 .cell {
            width: 100%;
            display: flex;
            justify-content: space-evenly;
            align-items: center;
        }

        .demo-drawer__content {
            padding: 16px 24px;
        }

        .demo-drawer__footer {
            width: 100%;
            display: flex;
            justify-content: space-evenly;
            align-items: center;
        }

        .goodItemList {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(162px, 1fr));
            row-gap: 10px;
            justify-items: center;
            align-items: center;
            margin-bottom: 16px;
        }

        .searchGoodInput {
            display: flex;
            align-items: center;
        }

        .paginationContainer {
            display: flex;
            justify-content: flex-end;
        }

        .goodSalePrefis {
            margin-left: 10px;
        }

        .desLabel {
            white-space: nowrap;
        }

        .showInfo {
            display: inline-block;
            cursor: pointer;
            width: 14px;
            height: 14px;
            object-fit: cover;
        }

        .showInfo img {
            width: 14px;
            height: 100%;
        }

        .btn.disBtn {
            display: none;
        }
        .el-table__body-wrapper {
            height: 500px;
        }

        .el-table__body-wrapper::-webkit-scrollbar {
            height: 10px !important;
        }
        /* .el-scrollbar__wrap::-webkit-scrollbar {
                height: 10px;
        } */

        /* 解决滚动条被固定列盖住的问题 */
        .el-table .el-table__fixed-right,
        .el-table .el-table__fixed {
            height: auto !important;
            bottom: 10px !important;
        }

        .el-table__fixed-body-wrapper .el-table__body {
            padding-bottom: 10px;
            /* 6px为滚动条宽度 */
        }
    </style>
</head>

<body class="yq-page-full vue-box">
    <div id="giftRecord" class="flex yq-table-page" v-loading="loading" element-loading-text="加载中..." v-cloak>
        <div class="yq-card">
            <div class="card-header">
                <div class="head-title">坐席赠送记录</div>
                <div class="btns">
                    <el-button type="primary" icon="el-icon-receiving" @click="handleDownload">批量收货</el-button>
                    <el-button type="primary" plain icon="el-icon-refresh" @click="handleReset">重置</el-button>
                    <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
                    <el-button type="primary" plain :icon="show ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                        @click="handleAdvanSearch">{{ show ? "收起" : "展开" }}
                    </el-button>
                </div>
            </div>
            <div class="card-content" style="width: 100%; height: auto;">
                <!-- <senior-search :show.sync="show" style="position: static"> -->
                <el-form class="search-form" style="display: block; margin-bottom: 0px;" :inline="false"
                    :model="searchForm" ref="searchForm" size="small" label-width="85px">
                    <el-row>
                        <el-col :span="4">
                            <el-form-item label="用户电话" prop="customerPhone">
                                <el-input v-model.trim="searchForm.customerPhone" placeholder="请输入用户电话"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="委托方" prop="clientName">
                                <el-select v-model="searchForm.clientCode" placeholder="请选择" filterable clearable>
                                    <el-option v-for="(label, value) in clientDict" :key="value" :label="label"
                                        :value="value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="接入单号" prop="contactOrderNo">
                                <el-input v-model.trim="searchForm.contactOrderNo" placeholder="请输入接入单号"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="服务单号" prop="serviceOrderNo">
                                <el-input v-model.trim="searchForm.serviceOrderNo" placeholder="请输入服务单号"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="坐席账号" prop="operatePerson">
                                <el-input v-model="searchForm.operatePerson" placeholder="请输入坐席账号"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="赠送时间" prop="date">
                                <el-date-picker v-model="searchForm.date" type="datetimerange"
                                    value-format="yyyy-MM-dd HH:mm:ss" start-placeholder="开始时间" end-placeholder="结束时间"
                                    :picker-options="pickerOptions" :unlink-panels="true"
                                    :default-time="['00:00:00', '23:59:59']">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <!-- <el-col :span="4">
                            <el-form-item label="补偿项目" prop="compensateItems">
                                <el-select v-model="searchForm.compensateItems" placeholder="请选择" filterable clearable
                                    @change="changeApplyStatus">
                                    <el-option v-for="(label, value) in WOM_COMPENSATE_COMPENSATEITEMS" :key="value"
                                        :label="label" :value="value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col> -->
                        <el-col :span="4">
                            <el-form-item label="单据状态" prop="applyStatus">
                                <el-select v-model="searchForm.applyStatus" placeholder="请选择" filterable clearable>
                                    <el-option v-for="(label, value) in compensateApplyStatus" :key="value"
                                        :label="label" :value="value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="运营区域" prop="operatorArea">
                                <el-select v-model="searchForm.operatorArea" placeholder="请选择" filterable clearable
                                    @change="handleChange">
                                    <el-option v-for="(label, value) in dept" :key="value" :label="label"
                                        :value="value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="坐席班组" prop="staffName">
                                <el-select v-model="searchForm.staffName" placeholder="请选择" filterable clearable>
                                    <el-option v-for="(label, value) in deptList" :key="value" :label="label"
                                        :value="value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4"></el-col>
                    </el-row>
                    <el-row v-show="show">
                        <el-col :span="4">
                            <el-form-item label="补偿单号" prop="compensateOrderNo">
                                <el-input v-model.trim="searchForm.compensateOrderNo" placeholder="请输入补偿单号"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="EA单号" prop="eaCode">
                                <el-input v-model.trim="searchForm.eaCode" placeholder="请输入EA单号"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="补偿原因" prop="compensateCauseType">
                                <el-select v-model="searchForm.compensateCauseType" placeholder="请选择" filterable
                                    clearable>
                                    <el-option v-for="(label, value) in COMPENSATE_CAUSE_TYPE" :key="value"
                                        :label="label" :value="value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="委托方编码" prop="clientCode">
                                <el-input v-model.trim="searchForm.clientCode" placeholder="请输入委托方编码"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="物流单号" prop="logisticsNo">
                                <el-input v-model.trim="searchForm.logisticsNo" placeholder="请输入物流单号"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item label="发货状态" prop="shippingStatus">
                                <el-select v-model="searchForm.shippingStatus" placeholder="请选择" filterable clearable>
                                    <el-option v-for="(label, value) in WOM_SHIPPING_STATUS" :key="value" :label="label"
                                        :value="value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- <el-form-item label="委托方编码" prop="clientCode" v-show="show">
                                <el-input
                                v-model="searchForm.clientCode"
                                placeholder="请输入委托方编码"
                                ></el-input>
                            </el-form-item> -->
                </el-form>
                <!-- </senior-search> -->
                <div class="yq-table">
                    <el-table stripe :data="tableData.data.data" v-loading="!tableData.data.data"
                        data-v-mars:url="/PeakEnd/webcall?action=peakEnd.queryMaterialList"
                        data-v-mars:model="tableData" data-v-mars:search="searchForm" height="100%" fit ref="table"
                        style="width: 100%" @selection-change="handleSelectionChange" :header-cell-style="{'text-align':'center'}" :cell-style="{'text-align':'center'}">
                        <el-table-column type="selection" width="55">
                        </el-table-column>
                        <el-table-column label="序号" align="center" type="index" fixed="left" width="60px">
                            <template slot-scope="scope">
                                {{ (tableData.pageIndex - 1) * tableData.pageSize +
                                scope.$index + 1 }}
                            </template>
                        </el-table-column>
                        <!--  v-if="ORG_CODE !== null" -->
                        <el-table-column prop="clientName" width="140px" label="委托方" show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ scope.row.clientName }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="prodName" label="产品品类" width="140px" show-overflow-tooltip>
                            <!-- <template slot-scope="scope">
                                {{ scope.row.prodName }}
                            </template> -->
                        </el-table-column>
                        <el-table-column prop="brandName" label="产品品牌" width="140px" show-overflow-tooltip>
                            <!-- <template slot-scope="scope">
                                {{ scope.row.brandName }}
                            </template> -->
                        </el-table-column>
                        <el-table-column prop="serviceOrderNo" label="服务单号" width="140px" show-overflow-tooltip>
                            <!-- <template slot-scope="scope">
                                {{ scope.row.serviceOrderNo }}
                            </template> -->
                        </el-table-column>
                        <el-table-column prop="contactOrderNo" label="接入单号" width="140px" show-overflow-tooltip>
                            <!-- <template slot-scope="scope">
                                {{ scope.row.contactOrderNo }}
                            </template> -->
                        </el-table-column>
                        <el-table-column prop="areaCode" label="区号" width="140px" show-overflow-tooltip>
                            <!-- <template slot-scope="scope">
                                {{ scope.row.areaCode }}
                            </template> -->
                        </el-table-column>
                        <!-- <el-table-column prop="customerName" label="用户姓名" show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ showInfo[`${scope.row.compensateOrderNo}`] === true ? scope.row.customerName :
                                scope.row.customerName.replace(/^(.).+$/,"$1**") }}
                            </template>
                        </el-table-column> -->
                        </el-table-column>
                        <el-table-column prop="customerPhone" label="用户电话" width="140px">
                            <template slot-scope="scope">
                                {{ showInfo[`${scope.row.compensateOrderNo}`] === true ? scope.row.customerPhone :
                                scope.row.customerPhone.replace(/^(.{3})(?:\d+)(.{4})$/, "$1****$2") }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="customerAddress" label="用户地址" width="140px" show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ showInfo[`${scope.row.compensateOrderNo}`] === true ? scope.row.customerAddress :
                                scope.row.customerAddress.replace(/^(.{6}).+$/,"$1***") }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="" label="查看" width="55px" align="center">
                            <template slot-scope="scope">
                                <span @click="handleShowInfo(scope.row)" class="showInfo"><img
                                        :src="`/PeakEnd/static/images/${showInfo[`${scope.row.compensateOrderNo}`] === true ? 'eyeOpen' : 'eyeClose'}.png`"
                                        alt=""></span>
                                <!-- <el-icon class="el-icon-view showPhone"
                                    @click.native="handleShowInfo(scope.row)"></el-icon> -->
                            </template>
                        </el-table-column>
                        <el-table-column prop="serviceLargeCategoryName" label="服务请求" width="200" show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ scope.row.serviceLargeCategoryName }}-{{
                                scope.row.serviceSmallCategoryName }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="compensateItems" label="补偿项目" width="160" show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ scope.row.compensateItems === "GIFT" ? "实物补偿" :
                                "现金补偿" }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="detailVOList" label="礼品内容" width="160" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span v-for="(item, index) in scope.row.detailVOList" :key="item.disSkuId">
                                    {{`${index === 0 ? '' : '、'}` + item.disSkuTitle}}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="COMPENSATE_APPLY_STATUS !== null" prop="applyStatus" label="单据状态">
                            <template slot-scope="scope">
                                {{ COMPENSATE_APPLY_STATUS[scope.row.applyStatus] }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="operatorArea" label="运营区域" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="staffName" label="坐席班组" width="120" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="pubCreateDate" label="赠送时间" width="180" show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ timestampToDate(scope.row.pubCreateDate) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="remark" label="申请备注" width="140px" show-overflow-tooltip>
                        </el-table-column>
                        <!-- <el-table-column prop="clientName" label="委托方" width="160" show-overflow-tooltip>
                        </el-table-column> -->
                        <el-table-column prop="compensateExplain" label="补偿说明" show-overflow-tooltip width="160">
                        </el-table-column>
                        <el-table-column prop="branchName" label="分中心名称" width="120" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="unitName" label="网点名称" width="140px" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="engineerName" label="工程师">
                        </el-table-column>

                        <el-table-column prop="compensateOrderNo" label="补偿单号" width="150px" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="salePrice" label="补偿金额" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="compensateCauseType" label="补偿原因" width="140px" show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ COMPENSATE_CAUSE_TYPE[scope.row.compensateCauseType] }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="compensateFailCaus" label="补偿失败原因" width="140px" show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ scope.row.compensateFailCaus }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="cancelCaus" label="取消原因" width="140px" show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ scope.row.cancelCaus }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="cancelTime" label="取消时间" width="180px" show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ scope.row.cancelTime ? timestampToDate(scope.row.cancelTime) : "" }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="clientCode" label="委托方编码" width="120" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="applyPerson" label="申请人" width="120" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="shippingStatus" label="发货状态" width="140px" show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ WOM_SHIPPING_STATUS[scope.row.shippingStatus] }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="cancelOperatePerson" label="取消人" width="120" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column label="操作" align="center" fixed="right" width="220">
                            <template slot-scope="scope">
                                <span class="btn" style="color: #0555ce; margin-right: 3px"
                                    @click="handleDetail(scope.$index, scope.row)">详情</span>
                                <span class="btn"
                                    :class="{ disBtn: (scope.row.applyStatus === '15' && (!scope.row.shippingStatus || scope.row.shippingStatus === '10' || scope.row.shippingStatus === '11' || scope.row.shippingStatus === '12' || scope.row.shippingStatus === '13')) || scope.row.applyStatus === '18' || scope.row.applyStatus === '19' || scope.row.applyStatus === '20'}"
                                    style="color: #0555ce; margin-right: 3px"
                                    @click="handleEdit(scope.$index, scope.row)">修改</span>
                                <span class="btn"
                                    :class=" { disBtn: (scope.row.applyStatus === '15' && (!scope.row.shippingStatus || scope.row.shippingStatus === '10' || scope.row.shippingStatus === '11' || scope.row.shippingStatus === '12' || scope.row.shippingStatus === '13')) || scope.row.applyStatus === '18' || scope.row.applyStatus ===  '19' || scope.row.applyStatus === '20'} "
                                    style="color: #fb4e51; margin-right: 3px"
                                    @click="handleCancel(scope.$index, scope.row)">取消</span>
                                <span class="btn" style="color: #fb4e51; margin-right: 3px"
                                    :class="{ disBtn: scope.row.applyStatus === '10' || (scope.row.applyStatus === '15' && !scope.row.shippingStatus) || scope.row.applyStatus === '18' || scope.row.applyStatus === '19' || scope.row.applyStatus === '20' }"
                                    @click="handleFeedback(scope.$index, scope.row)">退货</span>
                                <span class="btn" style="color: #0555ce"
                                    :class="{ disBtn: scope.row.applyStatus === '10' || (scope.row.applyStatus === '15' && (!scope.row.shippingStatus || scope.row.shippingStatus === '10' || scope.row.shippingStatus === '13')) || scope.row.applyStatus === '18' || scope.row.applyStatus === '19' || scope.row.applyStatus === '20' || scope.row.shippingStatus === 10 }"
                                    @click="handleConfirm(scope.$index, scope.row)">确认收货</span>
                                    <span class="btn" style="color: #0555ce" @click="handleReCompensate(scope.$index, scope.row)">重新补偿</span> 
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination background @current-change="onPageChange" @size-change="onPageSizeChange"
                        :current-page="tableData.pageIndex" :page-size="tableData.pageSize"
                        :page-sizes="[15, 30, 50, 100]" layout="total, prev, pager, next, jumper,sizes"
                        :total="tableData.data.totalRow">
                    </el-pagination>
                </div>
            </div>
            <el-drawer title="修改" :before-close="handleEditDrawerClose" :wrapper-closable="false"
                :visible.sync="editDrawerVis" direction="rtl" custom-class="demo-drawer" ref="drawer" size="40%">
                <div class="demo-drawer__content">
                    <el-form :model="editForm" :rules="editRules" ref="editForm" label-width="110px"
                        label-position="right">
                        <el-form-item label="补偿项目" prop="compensateItems">
                            <el-select style="width: 90%"
                                v-model="WOM_COMPENSATE_COMPENSATEITEMS[editForm.compensateItems]" placeholder="请选择"
                                filterable clearable disabled>
                                <!-- <el-option
                    v-for="(label, value) in WOM_COMPENSATE_COMPENSATEITEMS"
                    :key="value"
                    :label="label"
                    :value="value"
                  ></el-option> -->
                            </el-select>
                        </el-form-item>
                        <el-form-item label="用户姓名" prop="customerName">
                            <el-input style="width: 90%" v-model="editForm.customerName" placeholder="请输入用户姓名">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="电话号码" prop="phone">
                            <el-input style="width: 90%" v-model="editForm.phone" placeholder="请输入电话号码"
                                suffix-icon="el-icon-search" @change="searchArea">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="区号" prop="areaCode">
                            <el-input style="width: 90%" v-model="editForm.areaCode" placeholder="请输入区号"
                                suffix-icon="el-icon-search" @change="searchArea">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="区域名称" prop="areaName" v-loading="areaNameLoading">
                            <el-cascader style="width: 90%" v-model="editForm.areaName" :options="options"
                                @change="cascChange" @focus="cascFocus" :props="optionProps"></el-cascader>
                        </el-form-item>
                        <el-form-item v-if="editForm.regionCode !== ''" label="四级区域编码" prop="regionCode">
                            <el-input style="width: 90%" v-model="editForm.regionCode" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="用户地址" prop="customerAddress">
                            <el-input style="width: 90%" v-model="editForm.customerAddress" prop="customerAddress"
                                placeholder="请输入详细地址">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="搜索商品" prop="detailGood">
                            <div class="searchGoodInput">
                                <el-tag type="info" style="margin-right: 8px">价格区间</el-tag>
                                <el-input style="width: 100px" v-model="searchGoodForm.priceFloor" prop="salePrice2"
                                    @input="searchGoodForm.priceFloor=searchGoodForm.priceFloor.replace(/[^\d.]/g,'')"
                                    @blur="prefix2 = searchGoodForm.priceFloor === '' ? !prefix2 : false"
                                    @focus="prefix2 = searchGoodForm.priceFloor === '' ? !prefix2 : false">
                                    <i slot="prefix" class="goodSalePrefis" v-show="prefix2">￥</i>
                                </el-input>
                                <span style="margin: 0px 5px">-</span>
                                <el-input style="width: 100px" v-model="searchGoodForm.priceCeiling" prop="salePrice1"
                                    @input="searchGoodForm.priceCeiling=searchGoodForm.priceCeiling.replace(/[^0-9]/g,'')"
                                    @blur="prefix1 = searchGoodForm.priceCeiling === '' ? !prefix1 : false"
                                    @focus="prefix1 = searchGoodForm.priceCeiling === '' ? !prefix1 : false">
                                    <i slot="prefix" class="goodSalePrefis" v-show="prefix1">￥</i>
                                </el-input>

                                <el-button icon="el-icon-search" circle style="margin-left: 10px"
                                    @click="getGoodList"></el-button>
                            </div>
                        </el-form-item>
                        <div class="goodItemList" v-loading="goodLoading">
                            <good-item v-if="goodList.length" v-for="item in goodList" :key="item.dissku_id"
                                :item="item" @updategoodselected="getCompensateDeatils"></good-item>
                        </div>
                        <div class="paginationContainer">
                            <el-pagination background @current-change="onGoodPageChange"
                                @size-change="onGoodPageSizeChange" :current-page="goodPageIndex"
                                :page-size="goodPageSize" :page-sizes="[10, 30, 50, 100]"
                                layout="total, prev, pager, next, jumper,sizes" :total="goodTotalRow"
                                style="margin-bottom: 28px">
                            </el-pagination>
                        </div>
                    </el-form>
                    <div class="demo-drawer__footer">
                        <el-button @click="handleEditDrawerClose">取 消</el-button>
                        <el-button type="primary" @click="submitEdit" :loading="loading">{{ loading ? '修改中 ...' : '修改'
                            }}</el-button>
                    </div>
                </div>
            </el-drawer>
            <el-drawer title="取消" :before-close="handleCancelDrawerClose" :visible.sync="cancelDrawerVis"
                :wrapper-closable="false" direction="rtl" label-width="120px" custom-class="demo-drawer" ref="drawer">
                <div class="demo-drawer__content">
                    <el-form :model="cancelForm" label-position="left" :rules="cancelRules" ref="cancelForm">
                        <el-form-item label="取消原因" prop="cancelCaus">
                            <el-input type="textarea" maxlength="280" show-word-limit v-model="cancelForm.cancelCaus"
                                style="width: 70%"></el-input>
                        </el-form-item>
                    </el-form>
                    <div class="demo-drawer__footer">
                        <el-button @click="handleCancelDrawerClose">取 消</el-button>
                        <el-button type="danger" @click="submitCancel" :loading="loading">{{ loading ? '取消中 ...' : '确定'
                            }}</el-button>
                    </div>
                </div>
            </el-drawer>
            <el-drawer title="退货" :before-close="handleFeedbackDrawerClose" :visible.sync="feedbackDrawerVis"
                :wrapper-closable="false" direction="rtl" label-width="120px" custom-class="demo-drawer" ref="drawer">
                <div class="demo-drawer__content">
                    <el-form :model="feedbackForm" :rules="feedbackRules" ref="feedbackForm" label-position="left">
                        <el-form-item label="退货原因" prop="applyReasonId">
                            <el-select v-model="feedbackForm.applyReasonId" placeholder="请选择" style="width: 65%"
                                filterable clearable>
                                <el-option v-for="(label, value) in COMPENSTATE_APPLY_REASON" :key="value"
                                    :label="label" :value="value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="异常描述" prop="feedbackDesc">
                            <el-input style="width: 65%" type="textarea" maxlength="280" show-word-limit
                                v-model="feedbackForm.feedbackDesc"></el-input>
                        </el-form-item>
                    </el-form>
                    <div class="demo-drawer__footer">
                        <el-button @click="handleFeedbackDrawerClose">取 消</el-button>
                        <el-button type="danger" @click="submitFeedback" :loading="loading">{{ loading ? '退货中 ...' :
                            '退货' }}</el-button>
                    </div>
                </div>
            </el-drawer>
            <detail-dialog v-if="detailDialogVis" :dialogvis="detailDialogVis" :detailvolist="detailVOList" :gooddetails="goodDetails" :loading="detailLoading" :good="good" @closedialog="closeDialog"></detail-dialog>
        </div>
    </div>
</body>
<script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
<script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
<script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
<script>
    // 每个页面的容器id和实例变量要不一样
    var appPage = new Vue({
        el: "#giftRecord",
        watch: {
            "searchForm.date": function (newVal, oldVal) {
                if (newVal && newVal.length) {
                    this.searchForm.pubCurrentDate = newVal[0];
                    this.searchForm.pubCurrentDateEnd = newVal[1];
                } else {
                    this.searchForm.pubCurrentDate = "";
                    this.searchForm.pubCurrentDateEnd = "";
                }
            },
            "tableData.data.data": function () {
                this.$nextTick(() => {
                    console.log("watch");
                    this.$refs.table.doLayout(); // 解决fixed固定行错行问题
                });
            },
        },
        components: {
            "good-item": httpVueLoader(
                "/PeakEnd/pages/vue/components/gooditem.vue"
            ),
            "detail-dialog": httpVueLoader(
                "/PeakEnd/pages/vue/components/goodDetail.vue"
            )
        },
        data: function () {
            return {
                loading: false,
                show: false,
                editDrawerVis: false, // 修改弹窗
                cancelDrawerVis: false, // 取消弹窗
                feedbackDrawerVis: false, // 退货弹窗
                areaNameLoading: false, // 区域名称加载
                tableLoading: false, // 表格loading
                detailDialogVis: false, // 详情弹窗
                goodLoading: false,
                size: "",
                prefix1: true,
                prefix2: true,
                currEditItem: null,
                goodPageIndex: 1,
                goodPageSize: 10,
                goodTotalRow: 0,
                goodDetails: null, // 商品详情信息
                detailVOList: [], // 商品列表
                detailLoading: false,
                good: null, // 商品
                phoneTest: /^[1][0-9]{10}$/, // 11位手机号正则验证
                materialSelection: [], // 选中的记录
                showInfo: {},
                detailListDict: {
                    AfterSaleState: {
                        type: "number",
                        descriptionName: "售后状态",
                        description: {
                            0: "入库",
                            1: "退换成功",
                            2: "驳回",
                            3: "审核中",
                            4: "审核通过",
                            5: "取消售后",
                            6: "其他",
                        },
                    },
                    AftersaleType: {
                        type: "number",
                        descriptionName: "售后类型",
                        description: {
                            1: "发货前退款",
                            2: "退货(对应美福退货退款)",
                            3: "换货",
                        },
                    },
                    ItemReturnBackType: {
                        type: "number",
                        description: { 0: "待确认", 1: "买家寄回", 2: "上门取件" },
                    },
                    CancelButton: {
                        type: "number",
                        descriptionName: "是否能取消售后",
                        description: { 1: "可以", 0: "不可以" },
                    },
                },
                searchGoodForm: {
                    priceCeiling: "",
                    priceFloor: "",
                },
                goodList: [],
                optionProps: {
                    label: "name",
                    value: "id",
                },
                pickerOptions: {
                    shortcuts: [
                        {
                            text: "最近一周",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                                picker.$emit("pick", [start, end]);
                            },
                        },
                        {
                            text: "最近一个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                                picker.$emit("pick", [start, end]);
                            },
                        },
                        {
                            text: "最近三个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                                picker.$emit("pick", [start, end]);
                            },
                        },
                    ],
                },
                searchForm: {
                    pageType: "3",
                    compensateOrderNo: "",
                    contactOrderNo: "",
                    serviceOrderNo: "",
                    operatePerson: "",
                    phone: "",
                    applyStatus: "",
                    operatorArea: "",
                    clientName: "",
                    staffName: "",
                    compensateItems: "GIFT",
                    compensateCauseType: "",
                    clientCode: "",
                    eaCode: "",
                    logisticsNo: "",
                    // clientCode: "",
                    shippingStatus: "",
                    date: [],
                    pubCurrentDate: "",
                    pubCurrentDateEnd: "",
                    customerPhone: "",
                },
                tableData: {
                    pageIndex: 1,
                    pageSize: 15,
                    totalRow: 0,
                    data: [],
                },
                goodSelected: [],
                editForm: {
                    compensateItems: "GIFT", // 补偿项目
                    customerAddress: "", // 用户地址
                    regionCode: "", // 四级区域编码
                    areaCode: "",
                    areaName: [],
                    customerPhone: "",
                    // customerName: "",
                    phone: "",
                    detailList: [
                        //   {
                        //     disSkuId: "", // 商品SKU
                        //     compensateOrderNo: "", // 补偿单号
                        //     disSkuTitle: "", // 商品名称
                        //     salePrice: "", // 销售价
                        //     taxRate: "", // 税率
                        //     nakedPrice: "", // 不含税价
                        //     purchaseNum: "1", // 购买数量
                        //   },
                    ],
                },
                cancelForm: {
                    compensateOrderNo: "",
                    cancelCaus: "",
                    cancelTime: "",
                    // operatePerson: "",
                },
                feedbackForm: {
                    feedbackDesc: "",
                    applyReasonId: "",
                    applyReasonDesc: "",
                },
                editRules: {
                    // compensateItems: [
                    //   { required: true, message: "请选择补偿项目", trigger: "change" },
                    // ],
                    customerAddress: [
                        { required: true, message: "请输入详细地址", trigger: "blur" },
                    ],
                    // customerName: [
                    //     { required: true, message: "请输入用户姓名", trigger: "blur" },
                    // ],
                    // regionCode: [
                    //   {
                    //     required: true,
                    //     message: "请输入四级区域编码",
                    //     trigger: "blur",
                    //   },
                    // ],
                    phone: [
                        { required: true, message: "请输入电话号码", trigger: "blur" },
                    ],
                    // areaName: [
                    //   { required: true, message: "请选择所在区域", trigger: "change" },
                    // ],
                },
                cancelRules: {
                    cancelCaus: [
                        { required: true, message: "请输入取消原因", trigger: "blur" },
                    ],
                },
                feedbackRules: {
                    feedbackDesc: [
                        { required: true, message: "请填写异常描述", trigger: "blur" },
                    ],
                    applyReasonId: [
                        { required: true, message: "请选择退货原因", trigger: "change" },
                    ],
                },
                options: [],
                COMPENSATE_APPLY_STATUS: null,
                compensateApplyStatus: {},
                COMPENSATE_CAUSE_TYPE: {},
                dept: {},
                deptList: {},
                queryMaterialList: {},
                WOM_SHIPPING_STATUS: {},
                WOM_COMPENSATE_COMPENSATEITEMS: {},
                COMPENSTATE_APPLY_REASON: {},
                clientDict: {},
                ORG_CODE: null,
            };
        },
        methods: {
            // 列表增删改查 分页查询
            onPageChange: function (page) {
                this.tableData.pageIndex = page;
                this.tableData.data.data = null;
                this.getList();
            },
            onPageSizeChange: function (size) {
                this.tableData.pageSize = size;
                this.tableData.pageIndex = 1;
                this.tableData.data.data = null;
                this.getList();
            },
            handleReset: function () {
                this.$refs["searchForm"].resetFields();
            },
            // ajax接口调用
            getList: function () {
                let _this = this;
                this.tableLoading = true;
                if (
                    this.searchForm.compensateOrderNo === "" &&
                    this.searchForm.customerPhone === "" &&
                    this.searchForm.serviceOrderNo === "" &&
                    this.searchForm.contactOrderNo === "" &&
                    this.searchForm.eaCode === "" &&
                    !this.searchForm.date
                ) {
                    this.$message.warning(
                        "补偿单号、用户电话、服务单号、接入单号、ea单号、物流单号、创建时间，请至少填写一项"
                    );
                    this.tableLoading = false;
                    console.log(this.tableLoading, "tableLoading");
                } else {
                    yq.marsRender(this.$refs.table);
                    // this.tableData.data.data = [
                    //     {
                    //         clientName: "CS002",
                    //         prodName: "阿斯顿",
                    //         brandName: "阿三大苏打",
                    //         serviceOrderNo: "adasd111",
                    //         contactOrderNo: "ASDsad444",
                    //         areaCode: "adadq23423423",
                    //         customerName: "林朝颖",
                    //         customerPhone: "13411111111",
                    //         customerAddress: "广东省江门市江海区神奇三路100号",
                    //         compensateOrderNo: "asdasd1312313123123",
                    //         applyStatus: 18,
                    //     }
                    // ]
                }
            },
            getNewOrderDict: function () {
                let _this = this;
                yq.daoCall(
                    {
                        controls: [
                            "comm.sysCode('COMPENSATE_APPLY_STATUS')",
                            "comm.sysCode('COMPENSATE_CAUSE_TYPE')",
                            "comm.sysCode('WOM_SHIPPING_STATUS')",
                            "comm.sysCode('WOM_COMPENSATE_COMPENSATEITEMS')",
                            "comm.sysCode('COMPENSTATE_APPLY_REASON')",
                            "comm.sysCode('ORG_CODE')"
                        ],
                        params: {},
                    },
                    (data) => {
                        this.COMPENSATE_APPLY_STATUS =
                            data["comm.sysCode('COMPENSATE_APPLY_STATUS')"].data;
                        // console.log(this.COMPENSATE_APPLY_STATUS, "COMPENSATE_APPLY_STATUS");
                        this.COMPENSATE_CAUSE_TYPE =
                            data["comm.sysCode('COMPENSATE_CAUSE_TYPE')"].data;
                        this.WOM_SHIPPING_STATUS =
                            data["comm.sysCode('WOM_SHIPPING_STATUS')"].data;
                        this.WOM_COMPENSATE_COMPENSATEITEMS =
                            data["comm.sysCode('WOM_COMPENSATE_COMPENSATEITEMS')"].data;
                        this.COMPENSTATE_APPLY_REASON =
                            data["comm.sysCode('COMPENSTATE_APPLY_REASON')"].data;
                        this.ORG_CODE =
                            data["comm.sysCode('ORG_CODE')"].data;
                        this.changeApplyStatus();
                    },
                    { contextPath: "neworder" }
                );
                yq.daoCall({
                    params: {'param[0]': "CLIENT_NAME"},
                    controls: ['dict.getDictList']
                }, function(result) {
                    _this.clientDict = result["dict.getDictList"].data;
                }, {
                    contextPath: '/yq_common',
                    async: false
                });
            },
            getPeakEndDict: function () {
                yq.daoCall(
                    {
                        controls: ["common.getDept"],
                        params: {},
                    },
                    (data) => {
                        this.dept = data["common.getDept"].data;
                    },
                    { contextPath: "PeakEnd" }
                );
            },
            // 获取商品列表
            getGoodList: function () {
                if (
                    this.searchGoodForm.priceCeiling === "" ||
                    this.searchGoodForm.priceFloor === ""
                ) {
                    this.$message.warning("请完整输入价格区间");
                } else {
                    this.searchGoodForm.priceCeiling = Number(
                        this.searchGoodForm.priceCeiling
                    );
                    this.searchGoodForm.priceFloor = Number(
                        this.searchGoodForm.priceFloor
                    );
                    this.searchGoodForm.pageIndex = this.goodPageIndex;
                    this.searchGoodForm.pageSize = this.goodPageSize;
                    this.goodLoading = true;
                    yq.remoteCall(
                        "/PeakEnd/servlet/peakEnd?action=compensateProductList",
                        this.searchGoodForm,
                        (res) => {
                            if (res.state === 1) {
                                this.editForm.detailList = [];
                                this.goodSelected = [];
                                this.goodList = res.data.data;
                                this.goodTotalRow = res.data.totalRow;
                            } else {
                                this.$message.warning(`${res.msg}`);
                            }
                            this.goodLoading = false;
                        }
                    );
                }
            },
            handleChange(value) {
                if (!value) {
                    this.deptList = {};
                    return;
                }
                yq.daoCall(
                    {
                        controls: ["common.getDeptAll('5','" + value + "')"],
                        params: {},
                    },
                    (data) => {
                        this.deptList =
                            data["common.getDeptAll('5','" + value + "')"].data;
                    },
                    { contextPath: "PeakEnd" }
                );
            },
            handleDetail(index, row) {
                let data = {
                    detailVOList: row.detailVOList,
                };
                // this.goodDetails = null;
                // this.good = null;
                this.detailVOList = row.detailVOList
                yq.remoteCall(
                    "/PeakEnd/servlet/peakEnd?action=getMaterialDetail",
                    data,
                    (res) => {
                        if (res.state === 1) {
                            this.goodDetails = res.data;
                            console.log(res.data, "this.goodDetails");
                            this.good = row
                            console.log(row, "row");
                            this.detailLoading = false;
                        } else {
                            this.$message.error(res);
                        }
                    }
                );
                this.detailLoading = true
                this.detailDialogVis = true;
            },
            handleEdit(index, row) {
                this.editDrawerVis = true;
                this.currEditItem = row;
                this.editForm.phone = row.customerPhone;
                // this.editForm.customerName = row.customerName
                this.editForm.customerAddress = row.customerAddress;
                this.searchGoodForm.priceCeiling = 100;
                this.searchGoodForm.priceFloor = 1;
                this.getGoodList();
                this.searchArea();
            },
            handleCancel(index, row) {
                this.cancelDrawerVis = true;
                this.cancelForm.compensateOrderNo = row.compensateOrderNo;
                this.cancelForm.serviceOrderNo = row.serviceOrderNo;
                this.cancelForm.contactOrderNo = row.contactOrderNo;
            },
            handleFeedback(index, row) {
                this.feedbackDrawerVis = true;
                this.feedbackForm.compensateOrderNo = row.compensateOrderNo;
            },
            handleConfirm(index, row) {
                this.loading = true;
                this.$confirm("是否确认收货？", "提示")
                    .then((_) => {
                        let data = {
                            compensateOrderNo: row.compensateOrderNo,
                        };
                        yq.remoteCall(
                            "/PeakEnd/servlet/peakEnd?action=confirmReceipt",
                            data,
                            (res) => {
                                if (res.state === 1) {
                                    this.$message.success("收货成功");
                                } else {
                                    this.$message.error(`${res.msg}`);
                                }
                                this.loading = false;
                            }
                        );
                    })
                    .catch((_) => {
                        this.loading = false;
                    });
            },
            // 重新补偿
            handleReCompensate(index, row) {
                this.$confirm("是否重新补偿？", "提示")
                    .then((_) => {
                        let data = {
                            row: row,
                        };
                        yq.remoteCall(
                            "/PeakEnd/servlet/peakEnd?action=reSubmit",
                            data,
                            (res) => {
                                if (res.state === 1) {
                                    this.$message.success("重新补偿成功");
                                } else {
                                    this.$message.error(res.message);
                                }
                                this.loading = false;
                            }
                        );
                    })
                    .catch((_) => {
                        this.loading = false;
                    });
            },
            handleEditDrawerClose() {
                this.editDrawerVis = false;
                this.resetEditForm();
            },
            handleCancelDrawerClose() {
                this.cancelDrawerVis = false;
                this.resetCancel();
            },
            handleFeedbackDrawerClose() {
                this.feedbackDrawerVis = false;
                this.resetFeedback();
            },
            closeDialog() {
                this.detailDialogVis = false;
            },
            submitEdit() {
                this.$refs["editForm"].validate((valid) => {
                    if (valid) {
                        console.log(this.editForm.customerAddress, "customerAddress");
                        if (!this.phoneTest.test(this.editForm.phone)) {
                            this.$message.warning("请输入11位有效手机号");
                            return;
                        }
                        this.loading = true;
                        let { areaCode, areaName, phone, ...newEditForm } = this.editForm;
                        newEditForm.command = "compensateApplyModify";
                        //   newEditForm.compensateItems = "GIFT"
                        phone
                            ? (newEditForm.customerPhone = phone)
                            : (newEditForm.customerPhone = this.currEditItem.customerPhone);
                        newEditForm.compensateOrderNo =
                            this.currEditItem.compensateOrderNo;
                        console.log(newEditForm, "news1");
                        if (!newEditForm.regionCode || newEditForm.regionCode === "") {
                            newEditForm.regionCode = this.currEditItem.regionCode;
                        }
                        if (
                            !newEditForm.customerAddress ||
                            newEditForm.customerAddress === ""
                        ) {
                            newEditForm.customerAddress = this.currEditItem.customerAddress;
                        }
                        // newEditForm.detailList[0].compensateOrderNo =
                        //   this.currEditItem.compensateOrderNo;
                        console.log(this.currEditItem, "currEditItem");
                        //   this.loading = false;
                        this.$confirm("确定修改吗？")
                            .then((_) => {
                                let dep = 0;
                                if (newEditForm.detailList.length === 0) {
                                    dep = 1;
                                    for (let item of this.currEditItem.detailVOList) {
                                        newEditForm.detailList.push({
                                            disSkuId: item.disSkuId,
                                            // compensateOrderNo: this.currEditItem.compensateOrderNo,
                                            disSkuTitle: item.disSkuTitle,
                                            salePrice: Math.round(item.salePrice * 100),
                                            taxRate: item.taxRate,
                                            nakedPrice: Math.round(item.nakedPrice * 100),
                                            purchaseNum: item.purchaseNum,
                                        });
                                    }
                                }
                                console.log(newEditForm, "news");
                                yq.remoteCall(
                                    "/PeakEnd/servlet/peakEnd?action=DoCompensateService",
                                    newEditForm,
                                    (res) => {
                                        if (res.state === 1) {
                                            this.editDrawerVis = false;
                                            this.resetEditForm();
                                            this.$message.success("修改成功");
                                            this.loading = false;
                                            this.getList();
                                        } else {
                                            this.$message.error(res.msg);
                                            if (dep) {
                                                this.editForm.detailList = [];
                                            }
                                            // this.goodList = [];
                                            // this.goodSelected = [];
                                            // this.searchGoodForm = {
                                            //   priceCeiling: "",
                                            //   priceFloor: "",
                                            // };
                                            this.loading = false;
                                        }
                                    }
                                );
                            })
                            .catch((_) => {
                                this.loading = false;
                            });
                    }
                });
            },
            submitCancel() {
                this.loading = true;
                this.$confirm("确定取消该记录吗？")
                    .then((_) => {
                        this.$refs["cancelForm"].validate((valid) => {
                            if (valid) {
                                this.cancelForm.cancelTime = this.formatDate(new Date());
                                this.cancelForm.command = "compensateApplyCancel";
                                console.log(this.cancelForm, "this.cancelForm");
                                yq.remoteCall(
                                    "/PeakEnd/servlet/peakEnd?action=DoCompensateService",
                                    this.cancelForm,
                                    (res) => {
                                        if (res.state === 1) {
                                            this.cancelDrawerVis = false;
                                            this.$message.success("取消成功");
                                        } else {
                                            this.$message.error(`${res.msg}`);
                                        }
                                        this.loading = false;
                                    }
                                );
                            } else {
                                this.$message({
                                    message: "请填写取消原因",
                                });
                                this.loading = false;
                            }
                        });
                    })
                    .catch((_) => {
                        this.loading = false;
                    });
            },
            submitFeedback() {
                this.loading = true;
                this.$confirm("确定退货吗？")
                    .then((_) => {
                        this.$refs.feedbackForm.validate((valid) => {
                            if (valid) {
                                this.feedbackForm.applyReasonDesc =
                                    this.COMPENSTATE_APPLY_REASON[
                                    this.feedbackForm.applyReasonId
                                    ];
                                yq.remoteCall(
                                    "/PeakEnd/servlet/peakEnd?action=FeedbackAnomaly",
                                    this.feedbackForm,
                                    (res) => {
                                        if (res.state === 1) {
                                            this.feedbackDrawerVis = false;
                                            this.$message.success("退货成功");
                                        } else {
                                            this.$message.error(`${res.msg}`);
                                        }
                                        this.loading = false;
                                    }
                                );
                            } else {
                                this.$message({
                                    message: "请选择退货原因",
                                });
                                this.loading = false;
                            }
                        });
                    })
                    .catch((_) => {
                        this.loading = false;
                    });
            },
            cascChange() {
                this.editForm.regionCode =
                    this.editForm.areaName[this.editForm.areaName.length - 1];
            },
            cascFocus() {
                if (this.editForm.areaCode === "") {
                    this.$message("请先输入电话区号");
                }
            },
            searchArea() {
                let _this = this;
                if (!this.phoneTest.test(this.editForm.phone)) {
                    this.$message.warning("请输入11位有效手机号");
                    return;
                }
                if (this.editForm.areaCode !== "") {
                    let data2 = {
                        params: {
                            areaNum: _this.editForm.areaCode,
                            regionDesc: "",
                        },
                        controls: ["comm.regionByParam"],
                    };
                    _this.areaNameLoading = true;
                    yq.remoteCall(
                        "/neworder/webcall",
                        data2,
                        (res) => {
                            let data = res["comm.regionByParam"].data;
                            _this.options = data.length
                                ? _this.buildTree(data, data[0].id)
                                : data;
                            _this.areaNameLoading = false;
                        }
                    );
                } else {
                    let data1 = {
                        phone: this.editForm.phone,
                    };
                    yq.remoteCall(
                        "/neworder/servlet/config?query=telRegion",
                        data1,
                        (res) => {
                            _this.editForm.areaCode = res.data.areaNum;
                            // _this.editForm.regionCode = res.data.regionCode;
                            let data2 = {
                                params: {
                                    areaNum: _this.editForm.areaCode,
                                    regionDesc: "",
                                },
                                controls: ["comm.regionByParam"],
                            };
                            _this.areaNameLoading = true;
                            yq.remoteCall(
                                "/neworder/webcall",
                                data2,
                                (res) => {
                                    let data = res["comm.regionByParam"].data;
                                    _this.options = data.length
                                        ? _this.buildTree(data, data[0].id)
                                        : data;
                                    _this.areaNameLoading = false;
                                }
                            );
                        }
                    );
                }
            },
            handleAdvanSearch() {
                this.show = !this.show;
            },
            onGoodPageChange(newVal) {
                this.goodPageIndex = newVal;
                this.getGoodList();
            },
            onGoodPageSizeChange(newVal) {
                this.goodPageSize = newVal;
                this.getGoodList();
            },
            getCompensateDeatils(item, num, type) {
                if (type === "add") {
                    if (this.goodSelected.includes(item.dissku_id)) {
                        for (let oldItem of this.editForm.detailList) {
                            if (oldItem.disSkuId === item.dissku_id) {
                                oldItem.purchaseNum = num;
                            }
                        }
                    } else {
                        this.editForm.detailList.push({
                            disSkuId: item.dissku_id,
                            // compensateOrderNo: this.currEditItem.compensateOrderNo,
                            disSkuTitle: item.dis_sku_title,
                            salePrice: Math.round(item.sale_price * 100),
                            taxRate: item.tax_rate,
                            nakedPrice: Math.round(item.naked_price * 100),
                            purchaseNum: num,
                        });
                        this.goodSelected.push(item.dissku_id);
                    }
                } else if (type === "del") {
                    for (let i = 0; i < this.editForm.detailList.length; i++) {
                        if (this.editForm.detailList[i].disSkuId === item.dissku_id) {
                            this.goodSelected.splice(i, 1);
                            this.editForm.detailList.splice(i, 1);
                        }
                    }
                }
            },
            handleSearch() {
                this.tableData.data.data = null;
                this.tableData.pageIndex = 1;
                if (this.searchForm.operatorArea !== "") {
                    this.searchForm.operatorArea =
                        this.dept[this.searchForm.operatorArea];
                }
                if (this.searchForm.staffName !== "") {
                    this.searchForm.staffName =
                        this.deptList[this.searchForm.staffName];
                }
                // this.searchForm.compensateOrderNo = this.searchForm.compensateOrderNo.trim()
                this.getList();
            },
            // 重置editForm
            resetEditForm() {
                this.editForm = {
                    compensateItems: "GIFT", // 补偿项目
                    customerAddress: "", // 用户地址
                    regionCode: "", // 四级区域编码
                    areaCode: "",
                    phone: "",
                    customerPhone: "",
                    // customerName: "",
                    areaName: [],
                    detailList: [],
                };
                this.goodList = [];
                this.goodSelected = [];
                this.searchGoodForm = {
                    priceCeiling: "",
                    priceFloor: "",
                };
            },
            resetFeedback() {
                this.feedbackForm = {
                    feedbackDesc: "",
                    applyReasonId: "",
                    applyReasonDesc: "",
                };
            },
            resetCancel() {
                this.cancelForm = {
                    compensateOrderNo: "",
                    cancelCaus: "",
                    cancelTime: "",
                    // operatePerson: "",
                };
            },
            handleShowInfo(row) {
                const orderNo = `${row.compensateOrderNo}`;
                if (this.showInfo[orderNo] === undefined) {
                    this.$set(this.showInfo, orderNo, true);
                } else {
                    this.showInfo[orderNo] = !this.showInfo[orderNo];
                }
                console.log(this.showInfo, "showInfo");
            },
            handleSelectionChange(val) {
                this.materialSelection = val;
                console.log(this.materialSelection, "materialSelection");
            },
            // 点击导出时
            handleDownload() {
                // this.$message("导出功能开发中...")
                let data = {
                    list: this.materialSelection
                }
                yq.remoteCall("/PeakEnd/servlet/peakEnd?action=ConfirmReceiptList", data, (res) => {
                    if(res.state === 1) {
                        this.message.success("操作成功 !")
                    }else {
                        this.message.error(`${res.msg}`)
                    }
                })
            },
            changeApplyStatus() {
                this.compensateApplyStatus = {};
                if (this.searchForm.compensateItems === "GIFT") {
                    for (let key in this.COMPENSATE_APPLY_STATUS) {
                        if (key == 10 || key == 15 || key == 18 || key == 19 || key == 20) {
                            this.compensateApplyStatus[key] = this.COMPENSATE_APPLY_STATUS[key];
                        }
                    }
                } else {
                    for (let key in this.COMPENSATE_APPLY_STATUS) {
                        if (key == 11 || key == 12 || key == 13 || key == 14 || key == 16 || key == 17) {
                            this.compensateApplyStatus[key] = this.COMPENSATE_APPLY_STATUS[key];
                        }
                    }
                }
            },
            // 日期时间格式化封装函数
            // YYYY-MM-DD yyyy:mm:ss
            formatDate(timer) {
                const year = timer.getFullYear();
                const month = timer.getMonth() + 1; // 由于月份从0开始，因此需加1
                const day = timer.getDate();
                const hour = timer.getHours();
                const minute = timer.getMinutes();
                const second = timer.getSeconds();
                return `${this.pad(year, 4)}-${this.pad(month)}-${this.pad(
                    day
                )} ${this.pad(hour)}:${this.pad(minute)}:${this.pad(second)}`;
            },
            // YYYY-MM-DD
            formatTime(timer) {
                const year = timer.getFullYear();
                const month = timer.getMonth() + 1; // 由于月份从0开始，因此需加1
                const day = timer.getDate();
                return `${this.pad(year, 4)}-${this.pad(month)}-${this.pad(day)} `;
            },
            // 时间戳转为时间(YYYY-MM-DD yyyy:mm:ss)
            timestampToDate(Timestamp) {
                console.log(Timestamp);
                createTime = new Date(Timestamp);
                (y = createTime.getFullYear()),
                    (m = createTime.getMonth() + 1),
                    (d = createTime.getDate());
                    return (
                        y +
                        "-" +
                        (m < 10 ? "0" + m : m) +
                        "-" +
                        (d < 10 ? "0" + d : d) +
                        " " +
                        createTime.toTimeString().substr(0, 8)
                    );
            },
            // 定义具体处理标准
            // timeEl 传递过来具体的数值：年月日时分秒
            // total 字符串总长度 默认值为2
            // str 补充元素 默认值为"0"
            pad(timeEl, total = 2, str = "0") {
                return timeEl.toString().padStart(total, str);
            },
            buildTree(data) {
                const map = {}; // 用于存储每个地区及其（可能的）子节点的引用
                const tree = []; // 最终的树形结构数组

                // 遍历所有地区，构建映射
                data.forEach((item) => {
                    map[item.id] = { ...item }; // 仅复制当前项，不初始化children数组

                    // 如果存在父级，并且父级已经在map中，则将当前项添加到父级的children数组中
                    // 注意：这里也检查了父级的children属性是否存在，如果不存在则初始化它
                    if (item.pId && map[item.pId]) {
                        if (!map[item.pId].children) {
                            map[item.pId].children = []; // 仅在需要时才初始化children数组
                        }
                        map[item.pId].children.push(map[item.id]);
                    } else {
                        // 如果没有父级（即顶级地区），则将其添加到树形结构数组中
                        tree.push(map[item.id]);
                    }
                });
                return tree;
            },
        },
        created: function () {
            this.getNewOrderDict();
            this.getPeakEndDict();
            this.searchForm.date = [
                this.formatTime(
                    new Date(new Date().getTime() - 3600 * 1000 * 24 * 30)
                ) + "00:00:00",
                this.formatTime(new Date()) + "23:59:59",
            ];
        },
        mounted: function () {
            this.tableLoading = true;
            this.$nextTick(() => {
                this.getList();
            });
        },
    });
</script>

</html>