// JavaScript Document
(function(){
	function Tip(){
		var g=this,d=document.createElement('div'),b,o=null,co=null;
		d.className='tipbox';
		d.id='tipbox';
		d.innerHTML='<div class="tipinner"></div>';
		b=d.querySelector('.tipinner');
		this.content=function(html){
			b.innerHTML=html;
		};
		this.load=function(url,w,h){
			b.innerHTML='<iframe src="'+url+'" width="'+(w!=undefined?w:'100%')+'" height="'+(h!=undefined?h:'100%')+'" frameborder=0 scrolling="yes"></iframe>'
		};
		this.close=function(){
			if(o) {
				document.body.removeChild(o);
				o=null;
			}
		};
		this.init=function(node){
			//if(node.style.position=='') node.style.position='relative';
			var c=getAttr(node);
			console.log(c);
			if(c.type=='hover'){
				addEvent('mouseenter',node,function(e){
					e.stopPropagation();
					showTip(e);
					var tm=null;
					addEvent('mouseleave',node,function(e){
						tm=setTimeout(function(){
							g.close();
							if(tm) clearTimeout(tm);
						},500);
					});
					addEvent('mouseenter',d,function(e){
						e.stopPropagation();
						if(tm) clearTimeout(tm);
					});
					addEvent('mouseleave',d,function(e){
						g.close();
					});
				});
			}else{
				addEvent('click',node,function(e){
					showTip(e);
					var isEnter=false;
					addEvent('mouseenter',o,function(e){
						e.stopPropagation();
						isEnter=true;
					});
					addEvent('mouseleave',o,function(e){
						e.stopPropagation();
						isEnter=false;
					});
					removeEvent('resize',window,initSize);
					addEvent('resize',window,initSize);
					addEvent('mousewheel',window,function(){
						if(!isEnter) g.close();
					});
				});
			}
		};
		addEvent('click',window,function(e){
			if(co&&g){
				var c=getAttr(co);
				if(c.hideByWindow&&c.hideByWindow.toLowerCase()=='true') g.close();
			}
		});
		function showTip(e){
			e.stopPropagation();
			var n=document.getElementById('tipbox');
			var c=getAttr(e.target);
			if(b.children.length>0){
				var dom=b.children[0];
				dom.style.display='none';
				document.body.appendChild(dom);
			}
			if(n&&co==this){
				removeEvent('resize',window,initSize);
				document.body.removeChild(o);
				g.onToggle&&g.onToggle.call(this,false,c);
				return;
			}else document.body.appendChild(d);
			g.onToggle&&g.onToggle.call(this,true,c);
			o=d;
			co=e.target;
			if(c.url) g.load(c.url,c.width,c.height);
			else if(c.dom) {
				if(typeof c.dom=='string') c.dom=document.getElementById(c.dom);
				b.innerHTML='';
				b.appendChild(c.dom);
				c.dom.style.display='block';
			}else if(c.fn&&c.fn.length>0){
				g.content(eval(c.fn)||c.content);	
			}else g.content(c.content);
			if(c.url==''&&c.width!=-1) b.style.width=c.width+'px';
			else b.style.width='auto';
			if(c.url==''&&c.height!=-1) b.style.height=c.height+'px';
			else b.style.height='auto';
			intiStyle(this,e);	
		}
		function initSize(e){
			if(!co) return;
			var r=co.getClientRects()[0];
			var l=(r.left+co.offsetWidth/2-d.offsetWidth/2);
			if(l<5){
				l=5;	
			}else if(l+d.offsetWidth>window.innerWidth-10){
				l=window.innerWidth-5-d.offsetWidth;
			}
			d.style.left=l+'px';
			console.log(document.body.scrollTop);
			d.style.top=(r.top+co.offsetHeight+10+window.scrollY)+'px';
		}
		function addEvent(type,dom,fn){
			if(window.addEventListener){
				dom.addEventListener(type,fn,false);
			}else if(window.attachEvent){
				dom.attachEvent('on'+type,fn);
			}else dom['on'+type]=fn;
		}
		function removeEvent(type,dom,fn){
			if(window.removeEventListener){
				dom.removeEventListener(type,fn,false);
			}else if(window.detachEvent){
				dom.detachEvent('on'+type,fn);
			}else dom['on'+type]=null;
		}
		function getAttr(node){
			var a={url:'',content:'',fn:null,dom:null,width:-1,height:-1,hideByWindow:false,type:'click'};
			for(var k in a){
				var d=node.getAttribute('data-'+k);
				if(d){
					a[k]=d;	
				}else if(k=='content'){
					a[k]=node.innerHTML;
				}
			}
			return a;
		}
		function intiStyle(node,e){
			var cs='.tipbox{position:absolute; top:100px; left:100px; z-index:99999;}\
					.tipbox:before,.tipbox:after{position:absolute; content:""; font-size:0; left:50%; top:-5px; margin-left:-5px; width:10px; height:10px; background-color:#fff; transform:rotate(45deg); z-index:2;}\
					.tipbox:before{box-shadow:0 0 8px rgba(0,0,0,.35); z-index:-1;}\
					.tipbox .tipinner{background-color:#fff; box-shadow:0 0 8px rgba(0,0,0,.35);}';
			var s=document.getElementById('tipstyle');
			if(!s){
				s=document.createElement('style');
				s.id='tipstyle';
				s.type='text/css';
				s.innerHTML=cs;
				document.getElementsByTagName('head').item(0).appendChild(s);
			}
			initSize(e);
			var r=co.getClientRects()[0];
			s.innerHTML+='.tipbox:before,.tipbox:after{left:'+(r.left+node.offsetWidth/2-d.offsetLeft)+'px;}';
		}
	}
	window.myTip=new Tip();
	document.addEventListener('readystatechange',function(e){
		if(this.readyState=='interactive'){
			setTimeout(function(){
				var arr=document.all;
				if(arr.length>0){
					for(var i=0,d=null; i<arr.length; i++){
						d=arr[i];
						var att=d.getAttribute('data-tip');
						if(att&&att.toLowerCase()=='true') myTip.init(d);
					}	
				}
			},0);
		}
	});
	
})();