/**
 * @Title form表单获取内容以及绑定数据
 * @Description 将form表单数据转为JSON或者将JSON对象赋值给表单
 * 				获取表单的数据：$("#formid").serializeObject();
 *				绑定数据到表单：$("#formid").setForm(json);
 * @company 佳都新太科技股份有限公司
 * @copyright copyright (c) pci-suntektech.com Ltd.2016
 * <AUTHOR>
 * @return
 */

/**
 * 将form里面的内容序列化成json
 * @param {dom} 指定的选择器
 * @param {obj} 需要拼接在后面的json对象
 * @method serializeJson
 * 
 * */
$.fn.serializeObject = function(){    
   var o = {};    
   var a = this.serializeArray();  
   $.each(a, function() {    
       if (o[this.name]) {    
           if (!o[this.name].push) {    
               o[this.name] = [o[this.name]];    
           }    
           o[this.name].push(this.value || '');    
       } else {    
           o[this.name] = this.value || '';    
       }    
   });    
   return o;    
}; 

/**
* 将josn对象赋值给form
* @param {dom} 指定的选择器
* @param {obj} 需要给form赋值的json对象
* @method serializeJson
* */
$.fn.setForm = function(jsonValue){
 var obj = this;
 $.each(jsonValue,function(name,ival){
	 var $span = obj.find("span[name="+name+"]");
		 for(var j=0;j<$span.length;j++){
			 $span.html(ival);
	   }
   var $oinput = obj.find("input[name="+name+"]");
   if($oinput.attr("type")=="checkbox"){
     if(ival !== null){
       var checkboxObj = $("[name="+name+"]");
       var checkArray = ival.split(";");
       for(var i=0;i<checkboxObj.length;i++){
         for(var j=0;j<checkArray.length;j++){
           if(checkboxObj[i].value == checkArray[j]){
             checkboxObj[i].click();
           }
         }
       }
     }
   }else if($oinput.attr("type")=="radio"){
     $oinput.each(function(){
       var radioObj = $("[name="+name+"]");
       for(var i=0;i<radioObj.length;i++){
         if(radioObj[i].value == ival){
           radioObj[i].click();
         }
       }
     });
   }else if($oinput.attr("type")=="textarea"){
     obj.find("[name="+name+"]").html(ival);
   }else{
	   var onclickHtml = obj.find("[name="+name+"]").attr("onclick");
	   if(typeof(onclickHtml)!="undefined"&&onclickHtml.indexOf("WdatePicker")>=0){
		   if(ival!=null&&typeof(ival)!="undefined"){
			   if(ival.toString().indexOf("-")!=-1){
				   obj.find("[name="+name+"]").val(ival);
			   }else if(ival.toString()!=""){
				   obj.find("[name="+name+"]").val(format(ival,'yyyy-MM-dd')); 
			   }
		   }
	   }else{
		   obj.find("[name="+name+"]").val(ival);
	   }
   }
 });
}

