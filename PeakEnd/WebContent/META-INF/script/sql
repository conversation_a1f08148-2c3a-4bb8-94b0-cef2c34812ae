drop table C_NO_SCRIPT_CONFIG cascade constraints;

/*==============================================================*/
/* Table: C_NO_SCRIPT_CONFIG                                    */
/*==============================================================*/
create table C_NO_SCRIPT_CONFIG 
(
   ID                   VARCHAR(32)          not null,
   BRAND_NAME           VARCHAR(32),
   BRAND_CODE           VARCHAR(32),
   PROD_NAME            VARCHAR(32),
   PROD_CODE            VARCHAR(32),
   SERVICE_TYPE_NAME    VARCHAR(32),
   SERVICE_TYPE_CODE    VARCHAR(32),
   SERVICE_ITEM1_NAME   VARCHAR(32),
   SERVICE_ITEM1_CODE   VARCHAR(32),
   SERVICE_ITEM2_NAME   VARCHAR(32),
   SERVICE_ITEM2_CODE   VARCHAR(32),
   COMPENSATE_TYPE      VARCHAR(32),
   GUIDED_SPEECH        VARCHAR(500),
   REPEAT_CONTENT       VARCHAR(500),
   STATUS               VARCHAR(32),
   CREATE_ACC           VARCHAR(32),
   CREATE_TIME          VARCHAR(20),
   UPDATE_ACC           VARCHAR(32),
   UPDATE_TIME          VARCHAR(20),
   constraint PK_C_NO_SCRIPT_CONFIG primary key (ID)
);

comment on table C_NO_SCRIPT_CONFIG is
'补偿脚本配置';

comment on column C_NO_SCRIPT_CONFIG.ID is
'ID';

comment on column C_NO_SCRIPT_CONFIG.BRAND_NAME is
'品牌';

comment on column C_NO_SCRIPT_CONFIG.BRAND_CODE is
'1 事中补偿 2事后补偿';

comment on column C_NO_SCRIPT_CONFIG.PROD_NAME is
'1 延保 2 洗悦家 3 商城优惠劵';

comment on column C_NO_SCRIPT_CONFIG.PROD_CODE is
'品类编码';

comment on column C_NO_SCRIPT_CONFIG.SERVICE_TYPE_NAME is
'服务请求';

comment on column C_NO_SCRIPT_CONFIG.SERVICE_TYPE_CODE is
'服务请求编码';

comment on column C_NO_SCRIPT_CONFIG.SERVICE_ITEM1_NAME is
'服务请求小类';

comment on column C_NO_SCRIPT_CONFIG.SERVICE_ITEM1_CODE is
'服务请求小类编码';

comment on column C_NO_SCRIPT_CONFIG.SERVICE_ITEM2_NAME is
'服务请求大类';

comment on column C_NO_SCRIPT_CONFIG.SERVICE_ITEM2_CODE is
'服务请求大类编码';

comment on column C_NO_SCRIPT_CONFIG.COMPENSATE_TYPE is
'补偿类型';

comment on column C_NO_SCRIPT_CONFIG.GUIDED_SPEECH is
'话术脚本';

comment on column C_NO_SCRIPT_CONFIG.REPEAT_CONTENT is
'重复提醒脚本';

comment on column C_NO_SCRIPT_CONFIG.STATUS is
'启用状态';

comment on column C_NO_SCRIPT_CONFIG.CREATE_ACC is
'创建人';

comment on column C_NO_SCRIPT_CONFIG.CREATE_TIME is
'创建时间';

comment on column C_NO_SCRIPT_CONFIG.UPDATE_ACC is
'最后修改人';

comment on column C_NO_SCRIPT_CONFIG.UPDATE_TIME is
'最后修改时间';




drop table C_NO_ORG_INTEGRAL cascade constraints;

/*==============================================================*/
/* Table: C_NO_ORG_INTEGRAL                                     */
/*==============================================================*/
create table C_NO_ORG_INTEGRAL 
(
   ID                   VARCHAR(32)          not null,
   ORDER_ID             VARCHAR(32),
   COMPENSATE_TYPE      INT,
   COMPENSATE_MODE      INT,
   COMPENSATE_ID        VARCHAR(500),
   COMPENSATE_NO        VARCHAR(2000),
   COMPENSATE_NAME      INT,
   COMPENSATE_AMOUNT    VARCHAR(1),
   COMPENSATE_AMOUNT    VARCHAR(1),
   BEGIN_TIME           VARCHAR(20),
   END_TIME             VARCHAR(20),
   STATUS               VARCHAR(2),
   CREATE_ACC           VARCHAR(32),
   CREATE_TIME          VARCHAR(20),
   UPDATE_ACC           VARCHAR(32),
   UPDATE_TIME          VARCHAR(20),
   constraint PK_C_NO_ORG_INTEGRAL primary key (ID)
);

comment on table C_NO_ORG_INTEGRAL is
'事业部积分配置';

comment on column C_NO_ORG_INTEGRAL.ID is
'ID';

comment on column C_NO_ORG_INTEGRAL.ORDER_ID is
'事业部';

comment on column C_NO_ORG_INTEGRAL.COMPENSATE_TYPE is
'1 事中补偿 2事后补偿';

comment on column C_NO_ORG_INTEGRAL.COMPENSATE_MODE is
'1 延保 2 洗悦家 3 商城优惠劵';

comment on column C_NO_ORG_INTEGRAL.COMPENSATE_ID is
'提醒人的邮箱';

comment on column C_NO_ORG_INTEGRAL.COMPENSATE_NO is
'提醒内容';

comment on column C_NO_ORG_INTEGRAL.COMPENSATE_NAME is
'提醒次数';

comment on column C_NO_ORG_INTEGRAL.COMPENSATE_AMOUNT is
'开启提醒';

comment on column C_NO_ORG_INTEGRAL.BEGIN_TIME is
'积分有效开始日期';

comment on column C_NO_ORG_INTEGRAL.END_TIME is
'积分有效结束日期';

comment on column C_NO_ORG_INTEGRAL.STATUS is
'是否有效';

comment on column C_NO_ORG_INTEGRAL.CREATE_ACC is
'创建人';

comment on column C_NO_ORG_INTEGRAL.CREATE_TIME is
'创建时间';

comment on column C_NO_ORG_INTEGRAL.UPDATE_ACC is
'最后修改人';

comment on column C_NO_ORG_INTEGRAL.UPDATE_TIME is
'最后修改时间';

