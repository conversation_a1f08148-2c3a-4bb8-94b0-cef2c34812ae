package com.yunqu.cc.PeakEnd.utils;

import org.apache.commons.codec.digest.DigestUtils;

public class PwdUtils {
	  //顺序表
    static String orderStr = "";
    static {
        for (int i = 33; i < 127; i++) {
            orderStr += Character.toChars(i)[0];
        }
    }
    //判断是否有顺序
    public static boolean isOrder(String str) {
        if (!str.matches("((\\d)|([a-z])|([A-Z]))+")) {
            return false;
        }
        return orderStr.contains(str);
    }
    //判断是否相同
    public static boolean isSame(String str) {
        String regex = str.substring(0, 1) + "{" + str.length() + "}";
        return str.matches(regex);
    }
    
    
    //sha1加密
    public static String stringToSHA1(String str) {
    	return DigestUtils.sha1Hex(str);
    }
}
