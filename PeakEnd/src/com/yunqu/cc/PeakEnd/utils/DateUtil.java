package com.yunqu.cc.PeakEnd.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;


public class DateUtil {
	
	private static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	
	//计算n个月前的时间
	public static String getBeforeDateTime(int n) {
		Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date); // 设置为当前时间
        calendar.add(Calendar.MONTH, -n);
        return dateFormat.format(calendar.getTime());
	}
	
	//计算date 之前的n个月的时间
	public static String getBeforeDateTime(int n,Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date); // 设置为当前时间
        calendar.add(Calendar.MONTH, -n);
        return dateFormat.format(calendar.getTime());
	}
	
	//获取格式化时间
	public static String getFormatCurrTime(Date date) {
        return dateFormat.format(date.getTime());
	}
	
	//计算n个(月/日)前的时间
	public static String getBeforeDateTime(int n, int type) {
		Date date = new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date); // 设置为当前时间
		calendar.add(type, -n);
		return dateFormat.format(calendar.getTime());
	}
}
