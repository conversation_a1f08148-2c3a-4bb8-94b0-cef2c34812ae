package com.yunqu.cc.PeakEnd.utils;

import org.easitline.common.core.context.AppContext;

import com.yunqu.cc.PeakEnd.base.Constants;



public class ConfigUtil {
	public static String getString(String key,String defaultVal){
		return AppContext.getContext(Constants.APP_NAME).getProperty(key,defaultVal);	
	}
	
	public static String getString(String key){
		return getString(key,"");
	}
	
	public static int getInt(String key,int defaultVal){
		String value = getString(key);
		if(value!=null && !"".equals(value)){
			return Integer.parseInt(value);	
		}else{
			return defaultVal;
		}
	}
	
	public static int getInt(String key){
		return getInt(key,-1);
	}
}
