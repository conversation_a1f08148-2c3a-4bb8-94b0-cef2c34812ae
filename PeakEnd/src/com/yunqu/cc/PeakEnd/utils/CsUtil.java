package com.yunqu.cc.PeakEnd.utils;

import java.util.ArrayList;
import java.util.List;

import org.easitline.common.annotation.Types;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yunqu.cc.PeakEnd.enums.ErrorBack;


public class CsUtil {

	/**
	 * IService返回格式化
	 * @param data 原始数据
	 * @param objs key-value 数组
	 * @return
	 */
	public static JSONObject getData(JSONObject data,Types type){
		String respCode = data.getString("respCode");
		if(GWConstants.RET_CODE_SUCCESS.equals(respCode)){
			//System.out.println("返回数据="+data.getJSONObject("respData"));
			JSONObject o =data.getJSONObject("respData");
			o.put("msg", "\u8BF7\u6C42\u6210\u529F!");
			o.put("state", Integer.valueOf(1));
			o.put("pageType", Integer.valueOf(1));
			o.put("pageNumber", o.get("pageIndex"));
			o.put("type", type);
			return o;
		}else {
			switch(type){
			case LIST:
				data = JSONObject.parseObject(ErrorBack.OK_LIST.getValue());
				break;
			case RECORD:
				data = JSONObject.parseObject(ErrorBack.OK_RECORD.getValue());
				break;
			case DICT:
				data = JSONObject.parseObject(ErrorBack.OK_DISC.getValue());
				break;
			default:
				break;
			}
			
		}
		return data;
	}
	
	public static JSONObject getData(JSONObject data){
		return getData(data,Types.DICT);
	}
	
	public static JSONObject getDataTotal(JSONObject data){
		String respCode = data.getString("respCode");
		if(GWConstants.RET_CODE_SUCCESS.equals(respCode)){
			//System.out.println("返回数据="+data.getJSONObject("respData"));
			JSONObject o =new JSONObject();
			o.put("total", data.getJSONObject("respData").getString("total"));
			o.put("msg", "\u8BF7\u6C42\u6210\u529F!");
			o.put("state", Integer.valueOf(1));
			return o;
		}
		return data;
	}
	
	/**
	 * IService返回树取值
	 * @param data 原始数据
	 * @param objs key-value 数组
	 * @return
	 */
	public static JSONArray getTreeData(JSONObject data){
		String respCode = data.getString("respCode");
		if(GWConstants.RET_CODE_SUCCESS.equals(respCode)){
			//System.out.println(data.getJSONObject("respData"));
			return data.getJSONObject("respData").getJSONArray("data");
		}
		return new JSONArray();
	}
	
	/**
	 * 格式化服务请求的返回数据
	 * @param obj
	 */
	public static void formatServiceRequire(JSONObject obj,boolean isParent){
		JSONArray arr = obj.getJSONArray("data");
		JSONArray resArr = new JSONArray();
		List<Object> check = new ArrayList<Object>();
		for(int i=0;i<arr.size();i++){
			JSONObject ztree = new JSONObject();
			Object id = arr.getJSONObject(i).get("serviceRequireItemCode");
			if(check.contains(id)){
				continue;
			}else{
				check.add(id);
				ztree.put("id", id);
				ztree.put("name", arr.getJSONObject(i).get("serviceRequireItemName"));
				ztree.put("pId", arr.getJSONObject(i).get("parentServiceRequireCode"));
				ztree.put("isParent", isParent);
				resArr.add(ztree);
			}
		}
		obj.put("totalRow", resArr.size());
		obj.put("total", resArr.size());
		obj.put("data", resArr);
	}
	
	/**
	 * 接入单列表查询请求体删除多余数据
	 * @param obj
	 * @return
	 */
	public static JSONObject deleteBase(JSONObject obj){
		if(obj!=null){
			obj.remove("container");
			obj.remove("template");
			obj.remove("mars");
			obj.remove("autoFill");
			obj.remove("toggle");
			obj.remove("marsDone");
			obj.remove("data-mars");
			obj.remove("param[0]");
			obj.remove("textModel");
		}
		return obj;
	}
	
	public static void main(String[] a ){

	}
}
