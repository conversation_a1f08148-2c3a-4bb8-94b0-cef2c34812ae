package com.yunqu.cc.PeakEnd.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Collection;
import java.util.regex.Pattern;

public class ValueUtil
{
  private static DecimalFormat DecimalFormatter = new DecimalFormat("#.######");

  public static String toStrNull(Object value)
  {
    if (null == value) {
      return null;
    }
    if (((value instanceof Double)) || ((value instanceof Float))) {
      return DecimalFormatter.format(value);
    }
    return value.toString().trim();
  }

  public static String toStr(Object value)
  {
    if (null == value) {
      return "";
    }
    if (((value instanceof Double)) || ((value instanceof Float))) {
      return DecimalFormatter.format(value);
    }
    return value.toString().trim();
  }

  public static Byte toByte(Object value) {
    if (null == value) {
      return Byte.valueOf((byte)0);
    }
    Byte val = Byte.valueOf((byte)0);
    if ((value instanceof String)) {
      try {
        val = Byte.valueOf(value.toString());
      } catch (Exception e) {
      }
      if (null == val) {
        try {
          val = Byte.valueOf(Float.valueOf(value.toString()).byteValue());
        } catch (Exception er) {
          return Byte.valueOf((byte)0);
        }
      }
      return val;
    }
    if ((value instanceof BigDecimal)) {
      val = Byte.valueOf(((BigDecimal)value).byteValue());
      return val;
    }
    if ((value instanceof Number)) {
      val = Byte.valueOf(((Number)value).byteValue());
      return val;
    }

    return Byte.valueOf((byte)0);
  }

  public static Byte toByteNull(Object value) {
    if (null == value) {
      return null;
    }
    Byte val = Byte.valueOf((byte)0);
    if ((value instanceof String)) {
      try {
        val = Byte.valueOf(value.toString());
      } catch (Exception e) {
      }
      if (null == val) {
        try {
          val = Byte.valueOf(Float.valueOf(value.toString()).byteValue());
        } catch (Exception er) {
          return null;
        }
      }
      return val;
    }
    if ((value instanceof BigDecimal)) {
      val = Byte.valueOf(((BigDecimal)value).byteValue());
      return val;
    }
    if ((value instanceof Number)) {
      val = Byte.valueOf(((Number)value).byteValue());
      return val;
    }

    return null;
  }

  public static Integer toIntNull(Object value)
  {
    if (null == value) {
      return null;
    }
    Integer val = null;
    if ((value instanceof String)) {
      try {
        val = Integer.valueOf(value.toString());
      } catch (Exception e) {
      }
      if (null == val) {
        try {
          val = Integer.valueOf(Float.valueOf(value.toString()).intValue());
        } catch (Exception er) {
          return null;
        }
      }
      return val;
    }
    if ((value instanceof BigDecimal)) {
      val = Integer.valueOf(((BigDecimal)value).intValue());
      return val;
    }
    if ((value instanceof Number)) {
      val = Integer.valueOf(((Number)value).intValue());
      return val;
    }

    return null;
  }

  public static Integer toInt(Object value)
  {
    if (null == value) {
      return Integer.valueOf(0);
    }
    Integer val = Integer.valueOf(0);
    if ((value instanceof String)) {
      try {
        val = Integer.valueOf(value.toString());
      } catch (Exception e) {
      }
      if (null == val) {
        try {
          val = Integer.valueOf(Float.valueOf(value.toString()).intValue());
        } catch (Exception er) {
          return Integer.valueOf(0);
        }
      }
      return val;
    }
    if ((value instanceof BigDecimal)) {
      val = Integer.valueOf(((BigDecimal)value).intValue());
      return val;
    }
    if ((value instanceof Number)) {
      val = Integer.valueOf(((Number)value).intValue());
      return val;
    }

    return Integer.valueOf(0);
  }

  public static Float toFloatNull(Object value) {
    if (null == value) {
      return null;
    }
    Float val = null;
    if ((value instanceof String)) {
      try {
        val = Float.valueOf(value.toString());
      } catch (Exception e) {
        return null;
      }
      return val;
    }
    if ((value instanceof BigDecimal)) {
      val = Float.valueOf(((BigDecimal)value).floatValue());
      return val;
    }
    if ((value instanceof Number)) {
      val = Float.valueOf(((Number)value).floatValue());
      return val;
    }

    return null;
  }

  public static Double toDoubleNull(Object value) {
    if (null == value) {
      return null;
    }
    Double val = null;
    if ((value instanceof String)) {
      try {
        val = Double.valueOf(value.toString());
      } catch (Exception e) {
      }
      return val;
    }
    if ((value instanceof BigDecimal)) {
      val = Double.valueOf(((BigDecimal)value).doubleValue());
      return val;
    }
    if ((value instanceof Number)) {
      val = Double.valueOf(((Number)value).doubleValue());
      return val;
    }

    return val;
  }

  public static Double toDouble(Object value) {
    if (null == value) {
      return Double.valueOf(0.0D);
    }
    Double val = Double.valueOf(0.0D);
    if ((value instanceof String)) {
      try {
        val = Double.valueOf(value.toString());
      } catch (Exception e) {
      }
      return val;
    }
    if ((value instanceof BigDecimal)) {
      val = Double.valueOf(((BigDecimal)value).doubleValue());
      return val;
    }
    if ((value instanceof Number)) {
      val = Double.valueOf(((Number)value).doubleValue());
      return val;
    }

    return val;
  }

  public static Long toLongNull(Object value) {
    if (null == value) {
      return null;
    }
    Long val = null;
    if ((value instanceof String)) {
      try {
        val = Long.valueOf(value.toString());
      } catch (Exception e) {
      }
      if (null == val)
        try {
          val = Long.valueOf(Double.valueOf(value.toString()).longValue());
        }
        catch (Exception er) {
        }
      return val;
    }
    if ((value instanceof BigDecimal)) {
      val = Long.valueOf(((BigDecimal)value).longValue());
      return val;
    }
    if ((value instanceof Number)) {
      val = Long.valueOf(((Number)value).longValue());
      return val;
    }

    return val;
  }

  public static Long toLong(Object value) {
    if (null == value) {
      return Long.valueOf(0L);
    }
    Long val = Long.valueOf(0L);
    if ((value instanceof String)) {
      try {
        val = Long.valueOf(value.toString());
      } catch (Exception e) {
      }
      if (null == val)
        try {
          val = Long.valueOf(Double.valueOf(value.toString()).longValue());
        }
        catch (Exception er) {
        }
      return val;
    }
    if ((value instanceof BigDecimal)) {
      val = Long.valueOf(((BigDecimal)value).longValue());
      return val;
    }
    if ((value instanceof Number)) {
      val = Long.valueOf(((Number)value).longValue());
      return val;
    }

    return val;
  }

  public static BigDecimal toBigDecimalNull(Object value)
  {
    if (null == value) {
      return null;
    }
    try
    {
      return new BigDecimal(new StringBuilder().append(value).append("").toString());
    }
    catch (Exception e) {
    }
    return null;
  }

  public static boolean equal(Object value1, Object value2)
  {
    if ((null == value1) || (null == value2)) {
      return false;
    }
    return value1.equals(value2);
  }

  public static boolean isDecimal(String str)
  {
    if ((null == str) || ("".equals(str)))
      return false;
    Pattern pattern = Pattern.compile("[0-9]*(\\.?)[0-9]*");
    return pattern.matcher(str).matches();
  }

  public static boolean isInteger(String str)
  {
    if (null == str)
      return false;
    Pattern pattern = Pattern.compile("[0-9]+");
    return pattern.matcher(str).matches();
  }

  public static String lpad(String str, int n, String padStr) {
    if (str.length() >= n) {
      return str;
    }
    StringBuilder sb = new StringBuilder();
    while (sb.length() < n - str.length()) {
      sb.append(padStr);
    }
    sb.append(str);
    return sb.toString();
  }

  public static String rpad(String str, int n, String padStr) {
    if (str.length() >= n) {
      return str;
    }
    StringBuilder sb = new StringBuilder(str);
    while (sb.length() < n) {
      sb.append(padStr);
    }
    return sb.toString();
  }
  @SuppressWarnings("rawtypes")
public static boolean isEmpty(Object obj)
  {
    if (null == obj) {
      return true;
    }
    if ((obj instanceof String)) {
      return ((String)obj).trim().length() == 0;
    }
    if ((obj instanceof Collection)) {
      return ((Collection)obj).size() == 0;
    }
    if ((obj instanceof Object[])) {
      return ((Object[])obj).length == 0;
    }
    return false;
  }
  public static int compare(Object value1, Object value2)
  {
    if ((value1 instanceof String)) {
      return toStr(value1).compareTo(toStr(value2));
    }
    if ((value1 instanceof Number)) {
      return toDouble(value1).compareTo(toDouble(value2));
    }
    return 0;
  }

}
