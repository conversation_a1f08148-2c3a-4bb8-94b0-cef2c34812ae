package com.yunqu.cc.PeakEnd.utils;

import java.util.ArrayList;
import java.util.List;

public class TestReadUtilExcel extends ReadUtilExcel{
	protected List<List> list = new ArrayList<List>();
	int cols;
	/**
	 * @param args
	 */
	public static void main(String[] args) {
		long start = System.currentTimeMillis();
		String path = "D:/文档/test.xlsx";
		TestReadUtilExcel util = new TestReadUtilExcel();
		try {
			//util.process(path);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		long end = System.currentTimeMillis();
		
		System.out.println("一共读取了行数：" + util.getCurRow());
		System.out.println("读取数据用时：" + (end - start) / 1000 );
		System.out.println("列数：" + util.cols );
		//System.out.println("List的行数："+util.getList().size());
	}

	@Override
	public void optRow(int sheetIndex, int curRow, List<String> rowList) {
		// TODO Auto-generated method stub
		if(curRow==0){
			cols = rowList.size();
			
		}
		String temp = "";  
        for(String str : rowList) {  
            temp += str + "_";
        }  
       // System.out.println(cols);
	}

	public List<List> getList() {
		return list;
	}

	public void setList(List<List> list) {
		this.list = list;
	}

}
