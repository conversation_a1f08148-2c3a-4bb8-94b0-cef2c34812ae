/**
 * <html>
 * <body>
 *  <P> Copyright 广州云趣信息科技有限公司</p>
 *  <p> All rights reserved.</p>
 *  <p> Created on 2018年5月31日 上午11:23:23</p>
 *  <p> Created by wubin</p>
 *  </body>
 * </html>
 */
package com.yunqu.cc.PeakEnd.utils;

import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Date;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

/**     
* @Package：com.yunqu.cc.online.utils   
* @ClassName：ValidateHelper   
* @Description：   <p> 判断对象、字符串、集合是否为空、不为空</p>
* @Author： - wubin   
* @CreatTime：2018年5月31日 上午11:23:23   
* @Modify By：   
* @ModifyTime：  2018年5月31日
* @Modify marker：   
* @version    V1.0
*/
public class ValidateHelper {
	
	
	/**
	 * 判断数组是否为空
	 * @param array
	 * @return
	 */
	@SuppressWarnings("unused")
	private static <T> boolean isEmptyArray(T[] array){
		if (array == null || array.length == 0){
			return true;
		}else{
			return false;
		}
	}
	
	/**
	 * 判断数组是否不为空
	 * @param array
	 * @return
	 */
	public static <T> boolean isNotEmptyArray(T[] array){
		if (array != null && array.length > 0){
			return true;
		}else{
			return false;
		}
	}
	
	/**
	 * 判断字符串是否为空
	 * @param string
	 * @return
	 */
	public static boolean isEmptyString(String string){
		if (string == null || string.length() == 0){
			return true;
		}else{
			return false;
		}
	}
	
	/**
	 * 判断字符串是否不为空
	 * @param string
	 * @return
	 */
	public static boolean isNotEmptyString(String string){
		if (string != null && string.length() > 0){
			return true;
		}else{
			return false;
		}
	}
	
	/**
	 * @Description:  判断集合是否为空
	 * @param collection
	 * @return boolean
	 * @Autor: wubin - <EMAIL>
	 */
	public static boolean isEmptyCollection(Collection<?> collection){
		if (collection == null || collection.isEmpty()){
			return true;
		}else{
			return false;
		}
	}
	
	/**
	 * 判断集合是否不为空
	 * @param collection
	 * @return
	 */
	public static boolean isNotEmptyCollection(Collection<?> collection){
		if (collection != null && !collection.isEmpty()){
			return true;
		}else{
			return false;
		}
	}
	
	/**
	 * @Description: 判断map集合是否不为空
	 * @param map
	 * @return boolean
	 * @Autor: wubin - <EMAIL>
	 */
	@SuppressWarnings("rawtypes")
	public static boolean isNotEmptyMap(Map map){
		if (map != null && !map.isEmpty()){
			return true;
		}else{
			return false;
		}
	}
	
	/**
	 * @Description:  判断map集合是否为空
	 * @param map
	 * @return boolean
	 * @Autor: wubin - <EMAIL>
	 */
	@SuppressWarnings("rawtypes")
	public static boolean isEmptyMap(Map map){
		if (map == null || map.isEmpty()){
			return true;
		}else{
			return false;
		}
	}
	
	/**
	 * @Description: 检验对象是否为空,String 中只有空格在对象中也算空.
	 * @param object
	 * @return boolean
	 * @Autor: wubin - <EMAIL>
	 */
	@SuppressWarnings("rawtypes")
	public static boolean isEmpty(Object object) {
		if (null == object)
			return true;
		else if (object instanceof String)
			return "".equals(object.toString().trim());
		else if (object instanceof Iterable)
			return !((Iterable) object).iterator().hasNext();
		else if (object.getClass().isArray())
			return Array.getLength(object) == 0;
		else if (object instanceof Map)
			return ((Map) object).size() == 0;
		else if (Number.class.isAssignableFrom(object.getClass()))
			return false;
		else if (Date.class.isAssignableFrom(object.getClass()))
			return false;
		else
			return false;
	}

	/**
	 * 格式化空值 from wangyi for dict_dao_DICT_SYS_CODE
	 */
	public static String formatNull(Object str,String defaultValue){
		if(str==null ||"null".equals(str) ||"undefined".equals(str)){
			return defaultValue;
		}
		return str.toString().replaceAll(" ", "");
	}
	
	/**
	 * @Description:   判断字母、数字（适用于密码校验）
	 * @param str      传入的字符串
	 * @return boolean 是整数返回true,否则返回false capital
	 * @Autor: wubin - <EMAIL>
	 */
	public static boolean isAlphanumeric(String str) {
		Pattern pattern = Pattern.compile("^[A-Za-z0-9]+$");
		return pattern.matcher(str).matches();
	}
	
	/**
	 * @Description:   是否为合法手机号
	 * @param mobile   13 14 15 17 18 号段
	 * @return boolean 
	 * @Autor: wubin - <EMAIL>
	 */
	public static boolean isMobile(String mobile) {
		Pattern pattern = Pattern.compile("^1[3|4|5|7|8][0-9]{9}$");
		return pattern.matcher(mobile).matches();
	}
	
	/**
	 * @Description: 纯字母
	 * @param str
	 * @return boolean
	 * @Autor: wubin - <EMAIL>
	 */
	public static boolean isLettersOnly(String str) {
		Pattern pattern = Pattern.compile("^[A-Za-z]+$");
		return pattern.matcher(str).matches();
	}
	
	/**
	 * @Description: 纯数字
	 * @param str
	 * @return boolean
	 * @Autor: wubin - <EMAIL>
	 */
	public static boolean isNumeric(String str) {
		if (StringUtils.isBlank(str)) {
			return false;
		} else {
			return str.matches("\\d*");
		}
	}

}
