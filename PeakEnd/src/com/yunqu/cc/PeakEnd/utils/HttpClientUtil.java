package com.yunqu.cc.PeakEnd.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
public class HttpClientUtil {

	private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

	public static String post(String url, Map<String, Object> paramMap) {
		try {
			CloseableHttpClient httpclient = HttpClients.createDefault();
			HttpPost httppost = new HttpPost(url);
			List<NameValuePair> formParams = mapToList(paramMap);
			UrlEncodedFormEntity uefEntity = new UrlEncodedFormEntity(formParams, "UTF-8");
			httppost.setEntity(uefEntity);
			CloseableHttpResponse response = httpclient.execute(httppost);
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				String responseStr = EntityUtils.toString(entity, "UTF-8");
				System.out.println("--------------------------------------");
				System.out.println("Response content: " + responseStr);
				System.out.println("--------------------------------------");
				return responseStr;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	/*
	 * add by xdl
	 * 自检httppost工具类
	 */
	public static String postBody(String url, String json) throws Exception {
		CloseableHttpClient client = HttpClients.createDefault();
        HttpPost httppost = new HttpPost(url);
        httppost.addHeader("Accept", "application/json, text/javascript, */*; q=0.01");
        httppost.addHeader("Content-Type", "application/json");
        StringEntity se = new StringEntity(json, "UTF-8"); 
        httppost.setEntity(se);
        HttpResponse httpResponse = client.execute(httppost);
        String retSrc = EntityUtils.toString(httpResponse.getEntity());
//        System.out.println(retSrc);
//        System.out.println(FastJSONHelper.getJsonValue(retSrc, "errorCode"));
//        System.out.println(FastJSONHelper.getJsonValue(retSrc, "desc"));
//        System.out.println(FastJSONHelper.getJsonValue(retSrc, "bootStrapCode"));
        return retSrc;
	}
	/*
	 * add by pengjy
	 * 自检httppost工具类，返回字符集设置成UTF-8
	 */
	public static String postBody(String url, String json, String outPutCharset) throws Exception {
		CloseableHttpClient client = HttpClients.createDefault();
        HttpPost httppost = new HttpPost(url);
        httppost.addHeader("Accept", "application/json, text/javascript, */*; q=0.01");
        httppost.addHeader("Content-Type", "application/json");
        StringEntity se = new StringEntity(json, "UTF-8"); 
        httppost.setEntity(se);
        HttpResponse httpResponse = client.execute(httppost);
        String retSrc = EntityUtils.toString(httpResponse.getEntity(), Charset.forName(outPutCharset));
//        System.out.println(retSrc);
//        System.out.println(FastJSONHelper.getJsonValue(retSrc, "errorCode"));
//        System.out.println(FastJSONHelper.getJsonValue(retSrc, "desc"));
//        System.out.println(FastJSONHelper.getJsonValue(retSrc, "bootStrapCode"));
        return retSrc;
	}
	
	public static String get(String url, Map<String, Object> paramMap) {
		try {
			String param = getUrlParamsByMap(paramMap);
			CloseableHttpClient httpclient = HttpClients.createDefault();
			HttpGet httpget = new HttpGet(url + "?" + param);
			CloseableHttpResponse response = httpclient.execute(httpget);
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				String responseStr = EntityUtils.toString(entity, "UTF-8");
				System.out.println("--------------------------------------");
				System.out.println("Response content: " + responseStr);
				System.out.println("--------------------------------------");
				return responseStr;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	public static String get(String url) {
		try {
			CloseableHttpClient httpclient = HttpClients.createDefault();
			HttpGet httpget = new HttpGet(url);
			CloseableHttpResponse response = httpclient.execute(httpget);
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				String responseStr = EntityUtils.toString(entity, "UTF-8");
				System.out.println("--------------------------------------");
				System.out.println("Response content: " + responseStr);
				System.out.println("--------------------------------------");
				return responseStr;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	public static List<NameValuePair> mapToList(Map<String, Object> paramMap) throws Exception {
		List<NameValuePair> formParams = new ArrayList<NameValuePair>();
		for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
			formParams.add(new BasicNameValuePair(entry.getKey(), ValueUtil.toStr(entry.getValue())));
		}
		return formParams;
	}

	public static Map<String, String> parseParam(String params) throws Exception {
		if (ValueUtil.isEmpty(params)) {
			return null;
		}
		List<NameValuePair> list = URLEncodedUtils.parse(params, Charset.forName("UTF-8"));
		if (ValueUtil.isEmpty(list)) {
			return null;
		}
		Map<String, String> paramMap = new HashMap<String, String>();
		for (NameValuePair pair : list) {
			paramMap.put(pair.getName(), pair.getValue());
		}
		return paramMap;
	}

	/**
	 * @Description HttpPost请求
	 * <AUTHOR>
	 */
	public static String postHttp(String url, Map<String, Object> paramMap) {
		String responseData = "";
		try {
			if (url == null || "".equals(url)) {
				logger.info("-----------http请求的URL地址为空-------------");
			} else {
				String requestParas = "";
				if (paramMap != null && paramMap.size() > 0) {
					// 处理请求参数
					Set<String> keySet = paramMap.keySet();
					Iterator<String> it = keySet.iterator();
					while (it.hasNext()) {
						String key = (String) it.next();
						String value = (String) paramMap.get(key);
						if ("".equals(requestParas)) {
							requestParas = key + "=" + value;
						} else {
							requestParas = requestParas + "&" + key + "=" + value;
						}
					}
				}
				logger.info("\n----------Http请求的URL：" + url + "?" + requestParas + "----------");

				// 请求
				URL urlObj = new URL(url);
				HttpURLConnection con = (HttpURLConnection) urlObj.openConnection();
				con.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
				con.setRequestProperty("accept-language", "zh-cn");
				con.setRequestMethod("POST");
				con.setConnectTimeout(20000);
				con.setReadTimeout(20000);
				con.setDoOutput(true);
				con.setDoInput(true);
				OutputStreamWriter out = new OutputStreamWriter(con.getOutputStream(), "GBK");
				out.write(requestParas);
				out.flush();
				out.close();
				// 返回结果
				int code = con.getResponseCode();
				if (code >= 200 && code < 300) {
					BufferedReader bufRed = new BufferedReader(new InputStreamReader(con.getInputStream(), "UTF-8"));
					StringBuffer buffer = new StringBuffer();
					String line;
					while ((line = bufRed.readLine()) != null) {
						buffer.append(line);
					}
					bufRed.close();
					responseData = buffer.toString();
					logger.info("\n--------Http请求返回结果：" + responseData + "---------");
				} else {
					logger.info("\n--------Http请求失败,错误码:" + code + "----------");
				}
			}
		} catch (Exception e) {
			logger.error("\n---------Http请求异常----------");
			e.printStackTrace();
		}
		return responseData;
	}

	/**
	 * 将url参数转换成map
	 * @param param aa=11&bb=22&cc=33
	 * @return
	 */
	public static Map<String, Object> getUrlParams(String param) {
		Map<String, Object> map = new HashMap<String, Object>(0);
		if (StringUtils.isBlank(param)) {
			return map;
		}
		String[] params = param.split("&");
		for (int i = 0; i < params.length; i++) {
			String[] p = params[i].split("=");
			if (p.length == 2) {
				map.put(p[0], p[1]);
			}
		}
		return map;
	}

	/**
	 * 将map转换成url
	 * @param map
	 * @return
	 */
	public static String getUrlParamsByMap(Map<String, Object> map) throws Exception {
		if (map == null) {
			return "";
		}
		StringBuffer sb = new StringBuffer();
		for (Map.Entry<String, Object> entry : map.entrySet()) {
			sb.append(entry.getKey() + "=" + URLEncoder.encode(entry.getValue().toString(), "utf-8"));
			sb.append("&");
		}
		String s = sb.toString();
		if (s.endsWith("&")) {
			s = org.apache.commons.lang3.StringUtils.substringBeforeLast(s, "&");
		}
		return s;
	}

	public static void main(String[] args) {
//		Map<String, Object> paramMap=new HashMap<String, Object>();
//		paramMap.put("age", "中文!@#");
//		paramMap.put("name", "123");
//		paramMap.put("code", "2345");
//		get("http://localhost:8280/ccss-ipms-wom-rpc/app/mmp/demo", paramMap);
		
		postJsonBody("http://localhost:8680/ccss-ipms-wom-rpc/api/weixin/createserviceorder","{'serviceOrderVO':{'serviceOrderId':'ewfwefew.','contactOrderId':'ewfffff','serviceOrderNo':'wef'},'contactUserRequireVOs':[{'contactUserRequireId':'33wf','contactProductInfoId':'fw'},{'contactUserRequireId':'wfwf','contactProductInfoId':'hew'},{'contactUserRequireId':'wef','contactProductInfoId':'wefwe'}]}");
	}
	/**
	 * http采用post请求，传入json字符串，后台用对象接收
	 * <AUTHOR> 2016年6月7日 上午9:24:36
	 * @param requestUrl post地址
	 * @param jsonStr json 字符串
	 * */
	public static String postJsonBody(String requestUrl,String jsonStr) {
		logger.info("HttpClientUtil——》httpPostBody()——》请求地址："+requestUrl+"，请求json字符串:" + jsonStr);
		// 接口地址
		CloseableHttpClient httpClient = HttpClients.createDefault();
		long startTime = 0L;
		long endTime = 0L;
		int status = 0;
		String body = null;
		if (requestUrl == null ){
			logger.error("请求地址为空,请求失败!");
			return "请求地址为空,请求失败!";
		}
		HttpPost method = new HttpPost(requestUrl);
		if (method != null & jsonStr != null
				&& !"".equals(jsonStr.trim())) {
			try {

				// 建立一个NameValuePair数组，用于存储欲传送的参数
				method.addHeader("Content-type","application/json; charset=utf-8");
				method.setHeader("Accept", "application/json");
				method.setEntity(new StringEntity(jsonStr, Charset.forName("UTF-8")));
				startTime = System.currentTimeMillis();

				HttpResponse response = httpClient.execute(method);
				
				endTime = System.currentTimeMillis();
				int statusCode = response.getStatusLine().getStatusCode();
				
//				System.out.println("statusCode:" + statusCode);
				System.out.println("调用API 花费时间(单位：毫秒)：" + (endTime - startTime));
				logger.info("调用API 花费时间(单位：毫秒)：" + (endTime - startTime));
				if (statusCode != HttpStatus.SC_OK) {
					System.out.println("Method failed:" + response.getStatusLine());
					status = 1;
				}

				// Read the response body
				body = EntityUtils.toString(response.getEntity());
//				System.out.println("-------------body:"+body);
				logger.info("请求结果：" + body);
			} catch (IOException e) {
				// 网络错误
				status = 3;
				logger.error("\n--------Http请求失败,错误码:" + status + "，异常："+e.getMessage());
			} finally {
				System.out.println("调用接口状态：" + status);
				logger.info("调用接口状态：" + status);
			}
		}
		return body;
	}

}
