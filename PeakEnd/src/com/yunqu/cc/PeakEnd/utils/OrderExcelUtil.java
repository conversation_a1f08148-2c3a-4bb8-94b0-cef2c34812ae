package com.yunqu.cc.PeakEnd.utils;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.SharedStringsTable;
import org.xml.sax.InputSource;
import org.xml.sax.XMLReader;

public class OrderExcelUtil extends ReadUtilExcel {
	protected List<List<String>> list = new ArrayList<List<String>>();
	int cols;
	private int sheetIndex = -1;

	@Override
	public void process(InputStream file) throws Exception {
		OPCPackage pkg = OPCPackage.open(file);
		XSSFReader r = new XSSFReader(pkg);
		SharedStringsTable sst = r.getSharedStringsTable();

		XMLReader parser = fetchSheetParser(sst);

		Iterator<InputStream> sheets = r.getSheetsData();
		while (sheets.hasNext()) {
			sheetIndex++;
			InputStream sheet = sheets.next();
			InputSource sheetSource = new InputSource(sheet);
			parser.parse(sheetSource);
			sheet.close();
		}

	}

	@Override
	public void optRow(int sheetIndex, int curRow, List<String> rowList) {
		if (curRow == 0) {
			cols = rowList.size();
		}
		List<String> list1 = new ArrayList<String>();
		for (String str : rowList) {
			list1.add(str);
		}
		list.add(list1);
	}

	public List<List<String>> getList() {
		return list;
	}
}
