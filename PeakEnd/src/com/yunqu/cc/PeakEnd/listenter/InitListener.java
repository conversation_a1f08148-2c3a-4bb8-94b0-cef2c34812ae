package com.yunqu.cc.PeakEnd.listenter;

import com.yunqu.openapi.filter.SignFilter;

import javax.servlet.FilterRegistration;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

/**
 * @Author: liao
 * @Description:
 * @DateTime: 2025/2/28 14:26
 **/
public class InitListener implements ServletContextListener {
    @Override
    public void contextInitialized(ServletContextEvent servletContextEvent) {
        FilterRegistration.Dynamic signFilter = servletContextEvent.getServletContext().addFilter("SignFilter", new SignFilter());
        signFilter.addMappingForUrlPatterns(null, false, "/apicall/*","/openServlet/*");
    }

    @Override
    public void contextDestroyed(ServletContextEvent servletContextEvent) {

    }
}
