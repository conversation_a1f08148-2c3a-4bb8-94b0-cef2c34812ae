package com.yunqu.cc.PeakEnd.listenter;

import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.openapi.filter.SignFilter;
import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import javax.servlet.FilterRegistration;
import javax.servlet.ServletContextEvent;
import javax.servlet.annotation.WebListener;
import java.util.ArrayList;
import java.util.List;


@WebListener
public class OrgInterfaceLinstener extends ServiceContextListener{

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		
		List<ServiceResource> list = new ArrayList<ServiceResource>();
		ServiceResource resource = new ServiceResource();
	    resource.appName = "peakEnd";
	    resource.className = "com.yunqu.cc.neworder.inf.SynPeakEndService";
	    resource.description = "峰终同步数据接口";
	    resource.serviceId = "PEAK_END_INTEFACE";
	    resource.serviceName = "峰终同步数据接口";
	    list.add(resource);
	    
	    //定时任务----间隔五分钟执行一次
		ServiceResource resource2 = new ServiceResource();
		resource2.appName = Constants.APP_NAME;
		resource2.className = "com.yunqu.cc.PeakEnd.inf.IntergalStatJobService"	;
		resource2.description = "峰终预警定时任务";
		resource2.serviceId = "PEAK-END-JOB";
		resource2.serviceName = "峰终预警定时任务";
		list.add(resource2);
		
		//定时任务----每分钟执行一次
		ServiceResource resource3 = new ServiceResource();
		resource3.appName = Constants.APP_NAME;
		resource3.className = "com.yunqu.cc.PeakEnd.inf.CommonService";
		resource3.description = "峰终定时分配任务";
		resource3.serviceId = "PEAKEND-DISTRIBUTE-JOB";
		resource3.serviceName = "峰终定时分配任务";
		list.add(resource3);
		

		return list;

	}

}
