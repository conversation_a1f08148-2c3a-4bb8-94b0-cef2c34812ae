package com.yunqu.cc.PeakEnd.dao;



import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.yq.busi.common.base.OrderCommand;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.OrgUtil;
import com.yq.busi.common.util.PrivacyUtil;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.enums.ErrorBack;
import com.yunqu.cc.PeakEnd.utils.CsUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.PeakEnd.base.AppDaoContext;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.sql.OrderDaoSql;
import com.yunqu.cc.PeakEnd.sql.PeakEndSql;

@WebObject(name="peakEnd")
public class PeakEndDao extends AppDaoContext {
	@WebControl(name="getSMSContent",type=Types.TEXT)
	public JSONObject getSMSContent(){
		String getEpCode= UserUtil.getUser(this.request).getEpCode();
		String sql="  select * from  C_SMS_TEMPLATE where MSG_CODE=? and TYPE_ID =(select id  from  C_SMS_TEMPLATE_TYPE  where  "
				+ "code=?  and EP_CODE=? and  STATUS='1') and EP_CODE=? ";
		JSONObject queryForRecord = this.queryForRecord(sql, new Object[]{Constants.PEAK_END_CLEAN_SMS,Constants.PEAK_END_SMS,getEpCode,getEpCode}, null);
	
		 JSONObject resultJson = new JSONObject();
		 String content = queryForRecord.getJSONObject("data").get("CONTENT").toString();
		 content=content.replace("${CARD_CODE}","");
		 resultJson.put("data", queryForRecord.getJSONObject("data").get("CONTENT"));
		return 		resultJson;

	}
	@WebControl(name = "recordList",type = Types.LIST)
	public JSONObject recordList() {
		EasySQL sql = PeakEndSql.recordListSql(param);
		JSONObject result = queryForPageList(sql.getSQL(), sql.getParams());
		JSONObject object = new JSONObject();
		object.put("PHONE", PrivacyUtil.phone);
		object.put("CUSTOMER_NAME", PrivacyUtil.userName);
		object.put("ADDRESS", PrivacyUtil.addr);

		return PrivacyUtil.desensitization(result,object);
	}
	@WebControl(name="getOrderDetail",type=Types.RECORD)
	public JSONObject getOrgIntergalConfigDetail(){
		JSONObject queryForRecord = this.queryForRecord(new EasyRecord("C_NO_PEAK_END_ORDER","ID").setPrimaryValues(param.getString("peakEnd.ID")));
		if(param.get("resultId")==null||param.getString("resultId").equals("")){
			return queryForRecord;
		}else{
			String resultId=param.getString("resultId");
			JSONObject result = this.queryForRecord(new EasyRecord("C_NO_PEAK_END_RESULT","ID").setPrimaryValues(resultId));
			JSONObject data = queryForRecord.getJSONObject("data");
			data.put("RESULT", result.getJSONObject("data").get("VISIT_RESULT"));
			data.put("RESPONSIBLE", result.getJSONObject("data").get("RESPONSIBLE"));
			data.put("RESULT_CONTENT", result.getJSONObject("data").get("VISIT_CONTENT"));
			data.put("SERVICE_NAME", result.getJSONObject("data").get("VISIT_ACC"));
			data.put("SERVICE_TIME", result.getJSONObject("data").get("VISIT_TIME"));
			data.put("CALL_ID", result.getJSONObject("data").get("CALL_ID"));
			queryForRecord.put("data", data);
			return queryForRecord;
		}
	}

	/**
	 * 现金补偿列表
	 * @return
	 */
	@WebControl(name="compensateList",type=Types.LIST)
	public JSONObject compensateList(){
		JSONObject params = new JSONObject();
		JSONObject obj =  CsUtil.deleteBase(this.param);
		String pageIndex = obj.getString("pageIndex");//前端页码
		if(StringUtils.isBlank(pageIndex)) {
			CommLogger.logger.error(CommonUtil.getClassNameAndMethod(this) + "获取不到页码参数，默认赋值为1");
			obj.put("pageIndex", "1");
		}
		params.put("command", "compensateQueryList");
		params.put("params", obj);
		JSONObject result = new JSONObject();
		try {
			IService service = ServiceContext.getService(Constants.COMPENSATE_SERVICE_ID);
			JSONObject resp = service.invoke(params);
			result = CsUtil.getData(resp,Types.LIST);
		} catch (ServiceException e) {
			CommLogger.logger.error("IService请求失败,请求参数"+ JSON.toJSONString(obj)+",原因"+e.getMessage());
			result = JSONObject.parseObject(ErrorBack.OK_LIST.getValue());
		}
		result.put("loginAcct",  UserUtil.getUser(request).getUserAcc());
		return result;
	}

	/**
	 * 实物补偿列表
	 * @return
	 */
	@WebControl(name="queryMaterialList",type=Types.LIST)
	public JSONObject queryMaterialList(){
		JSONObject params = new JSONObject();
		JSONObject obj =  CsUtil.deleteBase(this.param);
		String pageIndex = obj.getString("pageIndex");//前端页码
		if(StringUtils.isBlank(pageIndex)) {
			CommLogger.logger.error(CommonUtil.getClassNameAndMethod(this) + "获取不到页码参数，默认赋值为1");
			obj.put("pageIndex", "1");
		}
		params.put("command", "queryMaterialList");
		params.put("params", obj);
		JSONObject result = new JSONObject();
		try {
			IService service = ServiceContext.getService(Constants.COMPENSATE_SERVICE_ID);
			JSONObject resp = service.invoke(params);
			CommLogger.logger.info("compensateList请求成功,请求参数"+ JSON.toJSONString(params)+",返回结果"+ JSON.toJSONString(resp));
			result = CsUtil.getData(resp,Types.LIST);
//			CommLogger.logger.info("compensateList请求成功,请求参数"+ JSON.toJSONString(params)+",返回结果result"+ JSON.toJSONString(result));
			for (int i = 0; i < result.getJSONArray("data").size(); i++) {
				JSONObject data = result.getJSONArray("data").getJSONObject(i);
				data.put("salePrice", 0.00);
				for (int j = 0; j < data.getJSONArray("detailVOList").size(); j++) {
					JSONObject item = data.getJSONArray("detailVOList").getJSONObject(j);
					item.put("salePrice", item.getDoubleValue("salePrice") / 100);
					item.put("nakedPrice", item.getDoubleValue("nakedPrice") / 100);
					data.put("salePrice", item.getDoubleValue("salePrice") + data.getDoubleValue("salePrice"));
				}
			}
//			result = resp.getJSONObject("respData");
		} catch (ServiceException e) {
			CommLogger.logger.error("IService请求失败,请求参数"+ JSON.toJSONString(obj)+",原因"+e.getMessage());
			result = JSONObject.parseObject(ErrorBack.OK_LIST.getValue());
		}
		result.put("loginAcct",  UserUtil.getUser(request).getUserAcc());
		return EasyResult.ok(result);
	}

    /**
     * 商品列表查询
     * @return
     */
    @WebControl(name="compensateProductList",type=Types.LIST)
    public JSONObject compensateProductList(){
        JSONObject params = new JSONObject();
        JSONObject obj =  CsUtil.deleteBase(this.param);
        String pageIndex = obj.getString("pageIndex");//前端页码
        if(StringUtils.isBlank(pageIndex)) {
            CommLogger.logger.error(CommonUtil.getClassNameAndMethod(this) + "获取不到页码参数，默认赋值为1");
            obj.put("pageIndex", "1");
        }
        params.put("command", "compensateProductList");
        params.put("params", obj);
        JSONObject result = new JSONObject();
        try {
            IService service = ServiceContext.getService(Constants.COMPENSATE_SERVICE_ID);
            JSONObject resp = service.invoke(params);
            result = CsUtil.getData(resp,Types.LIST);
        } catch (ServiceException e) {
            CommLogger.logger.error("IService请求失败,请求参数"+ JSON.toJSONString(obj)+",原因"+e.getMessage());
            result = JSONObject.parseObject(ErrorBack.OK_LIST.getValue());
        }
        result.put("loginAcct",  UserUtil.getUser(request).getUserAcc());
        return result;
    }

}
