package com.yunqu.cc.PeakEnd.dao;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.PeakEnd.base.AppDaoContext;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

/**
 * 角色现金补偿规则Dao
 */
@WebObject(name = "roleCashScriptDao")
public class RoleCashScriptDao extends AppDaoContext {

    private static final Logger logger = CommLogger.logger;

    /**
     * 分页查询脚本配置列表
     */
    @WebControl(name = "getRoleCashScriptList", type = Types.LIST)
    public JSONObject getRoleCashScriptList() {
        logger.info("分页查询角色现金补偿规则配置列表，参数：{" + this.param + "}");
        String listSql = "select * " +
                "from C_NO_ROLE_CASH_SCRIPT t1 where 1=1 ";
        EasySQL easySQL = this.getEasySQL(listSql);

        if (StringUtils.isNotBlank(this.param.getString("ROLE_ID"))) {
            easySQL.append(this.param.getString("ROLE_ID"), "and ROLE_ID = ?");
        }
        logger.info("分页查询角色现金补偿规则配置列表，SQL：{" + easySQL.getSQL() + "}");
        return this.queryForPageList(easySQL.getSQL(), easySQL.getParams());
    }

    /**
     * 查询补偿脚本配置
     */
    @WebControl(name = "getRoleCashScript", type = Types.RECORD)
    public JSONObject getRoleCashScript() {
        String id = this.param.getString("cashScript.ID");
        logger.info("查询补偿脚本配置，参数：{" + id + "}");
        if (StringUtils.isBlank(id)) {
            logger.info("查询补偿脚本配置失败，缺失ID参数");
            return new JSONObject();
        }

        String listSql = "select * " +
                "from C_NO_ROLE_CASH_SCRIPT t1";
        EasySQL easySQL = this.getEasySQL(listSql).append(id, " where ID = ?");

        JSONObject result = this.queryForRecord(easySQL.getSQL(), easySQL.getParams(), null);
        logger.info("查询补偿脚本配置，SQL：{" + easySQL.getSQL() + "}，结果：{" + result + "}");
        return result;
    }
}
