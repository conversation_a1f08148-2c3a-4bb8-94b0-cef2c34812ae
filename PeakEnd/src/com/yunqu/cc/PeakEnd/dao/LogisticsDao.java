package com.yunqu.cc.PeakEnd.dao;

import com.yq.busi.common.util.PrivacyUtil;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.PeakEnd.base.AppDaoContext;
import com.yunqu.cc.PeakEnd.sql.OrderDaoSql;

/**
 * 物流信息DAO
 */
@WebObject(name = "logistics")
public class LogisticsDao extends AppDaoContext {

	@WebControl(name = "list",type = Types.LIST)
	public JSONObject list() {
		EasySQL sql = new EasySQL("SELECT * FROM C_NO_LOGISTICS_INFORMATION WHERE 1=1 ");
		sql.append(param.getString("mideaOrderNo")," AND MIDEA_ORDER_NO = ? ");
		sql.append(param.getString("postName")," AND POST_NAME = ? ");
		sql.append(param.getString("postTel")," AND POST_TEL = ? ");
		sql.append(param.getString("deliveryNo")," AND DELIVERY_NO = ? ");
		sql.append(param.getString("createTimeStar")," AND CREATE_TIME >= ? ");
		sql.append(param.getString("createTimeEnd")," AND CREATE_TIME <= ? ");
		sql.append(" ORDER BY CREATE_TIME DESC");
		JSONObject result = queryForPageList(sql.getSQL(), sql.getParams());
		JSONObject object = new JSONObject();
		object.put("POST_TEL", PrivacyUtil.phone);
		object.put("POST_NAME", PrivacyUtil.userName);
		object.put("POST_ADDRESS", PrivacyUtil.addr);

		return PrivacyUtil.desensitization(result,object);
	}
}
