/**
 * <html>
 * <body>
 * <P> Copyright 广州云趣信息科技有限公司</p>
 * <p> All rights reserved.</p>
 * <p> Created on 2018年5月30日 下午3:00:09</p>
 * <p> Created by wubin</p>
 * </body>
 * </html>
 */
package com.yunqu.cc.PeakEnd.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.OrderCommand;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.PeakEnd.base.AppDaoContext;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.utils.CsUtil;
import com.yunqu.cc.PeakEnd.utils.ValidateHelper;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version V1.0
 * @Package：com.yunqu.cc.online.dao
 * @ClassName：DictDao
 * @Description： <p> 字典相关</p>
 * @Author： - wubin
 * @CreatTime：2018年5月30日 下午3:00:09
 * @Modify By：
 * @ModifyTime： 2018年5月30日
 * @Modify marker：
 */
@WebObject(name = "Dict")
public class DictDao extends AppDaoContext {

    private Logger logger = CommLogger.logger;

    private EasyQuery getmarsQuery(){
        return EasyQuery.getQuery(Constants.APP_NAME, Constants.FRAME_DS);
    }

    /**
     * 基础配置数据查询
     *
     * @param codeType 根据编码类别查询该类别下所有的编码值及其对应的名称
     * @param orgCode  主体编码
     * @return JSONObject 分类结构数据
     */
    @WebControl(name = "DICT_SYS_CODE", type = Types.RECORD)
    public JSONObject getArchivesTypeDict() {
        JSONObject result = new JSONObject();
        JSONObject obj = new JSONObject();
        Map<String, Object> param = new HashMap<String, Object>();
        String codeType = ValidateHelper.formatNull(getMethodParam(0), "");
        String orgCode = ValidateHelper.formatNull(getMethodParam(1), "");
        param.put("codeType", codeType);
        param.put("orgCode", orgCode);
        obj.put("command", OrderCommand.COMM_SYSCODE);
        obj.put("params", param);
        try {
            IService service = ServiceContext.getService("CSSGW-COMM");
            result = service.invoke(obj);
        } catch (ServiceException e) {
            logger.error("IService请求失败,请求参数" + JSON.toJSONString(obj) + ",原因" + e.getMessage());
        }
        return CsUtil.getData(result);
    }

    @WebControl(name = "sysCodeInvert", type = Types.RECORD)
    public JSONObject getsysCodeInvertDict() {
        String depCode = UserUtil.getUser(request).getEpCode();
        JSONObject jsonEnableDictListByGroupCode = DictCache.getJsonEnableDictListByGroupCode(depCode, "ONLINE_CONSULT_AREA");

        return jsonEnableDictListByGroupCode;
    }


    /**
     * @return JSONObject
     * @Description: 用户level等级
     * @Autor: wubin - <EMAIL>
     */
    @WebControl(name = "USER_INFO_LEVEL", type = Types.RECORD)
    public JSONObject getUserInfoLevel() {
        String depCode = UserUtil.getUser(request).getEpCode();
        return DictCache.getJsonEnableDictListByGroupCode(depCode, "USER_INFO_LEVEL");
    }

    /**
     * @return JSONObject
     * @Description: 用户民族
     * @Autor: wubin - <EMAIL>
     */
    @WebControl(name = "USER_INFO_NATIONAL", type = Types.RECORD)
    public JSONObject getUserNational() {
        String depCode = UserUtil.getUser(request).getEpCode();
        return DictCache.getJsonEnableDictListByGroupCode(depCode, "USER_INFO_NATIONAL");
    }

    /**
     * @return JSONObject
     * @Description: 用户性别
     * @Autor: wubin - <EMAIL>
     */
    @WebControl(name = "USER_INFO_SEX", type = Types.RECORD)
    public JSONObject getUserGender() {
        String depCode = UserUtil.getUser(request).getEpCode();
        return DictCache.getJsonEnableDictListByGroupCode(depCode, "USER_INFO_SEX");
    }

    /**
     * @return JSONObject
     * @Description: 用户类型
     * @Autor: wubin - <EMAIL>
     */
    @WebControl(name = "USER_INFO_TYPE", type = Types.RECORD)
    public JSONObject getUserTags() {

        String depCode = UserUtil.getUser(request).getEpCode();
        return DictCache.getJsonEnableDictListByGroupCode(depCode, "USER_INFO_TYPE");
    }

    /**
     * @return JSONObject
     * @Description: 用户性别
     * @Autor: wubin - <EMAIL>
     */
    @WebControl(name = "SF_YN", type = Types.RECORD)
    public JSONObject getSF_YN() {
        String depCode = UserUtil.getUser(request).getEpCode();
        return DictCache.getJsonEnableDictListByGroupCode(depCode, "SF_YN");
    }

    /**
     * @return JSONObject
     * @Description: 用户性别
     * @Autor: wubin - <EMAIL>
     */
    @WebControl(name = "ORDER_TYPE", type = Types.RECORD)
    public JSONObject getOrderType() {
        String depCode = UserUtil.getUser(request).getEpCode();
        return DictCache.getJsonEnableDictListByGroupCode(depCode, "ORDER_TYPE");
    }

    /**
     * 查询系统角色列表字典
     */
    @WebControl(name = "getRoleList", type = Types.DICT)
    public JSONObject getRoleList() {
        EasySQL sql = new EasySQL();
        sql.append("SELECT T.ROLE_ID,T.ROLE_NAME FROM EASI_ROLE T ORDER BY T.IDX_ORDER");

        JSONObject result = new JSONObject();

        Map<String, String> roleMap = new HashMap<>();
        try {

            EasyQuery query = getmarsQuery();
            List<EasyRow> list = query.queryForList(sql.getSQL(), sql.getParams());
            list.forEach(role -> roleMap.put(role.getColumnValue("ROLE_ID"), role.getColumnValue("ROLE_NAME")));

            result.put("data", roleMap);
            result.put("state", 200);

        } catch (Exception e) {
            result.put("state", 500);
            result.put("msg", "查询失败，原因：" + e.getMessage());
            this.error("查询失败，原因：" + e.getMessage(), e);
        }
        return result;
    }
}
