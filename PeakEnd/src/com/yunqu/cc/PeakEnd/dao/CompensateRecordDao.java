package com.yunqu.cc.PeakEnd.dao;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.PeakEnd.base.AppDaoContext;
import com.yunqu.cc.PeakEnd.base.CommLogger;

/**
 * 补偿记录类
 * <AUTHOR>
 */
@WebObject(name = "compensateRecordDao")
public class CompensateRecordDao extends AppDaoContext{

	private Logger logger = CommLogger.logger;
	
	
	@WebControl(name="recordList",type=Types.LIST)
	public JSONObject recordList() {
		EasySQL sql = this.getEasySQL("select * from C_NO_PEAK_END_RECORD WHERE 1=1 ");
		sql.append(this.param.getString("orderId")," and ORDER_ID=? ");
		sql.append(this.param.getString("prodCode")," and PROD_CODE=? ");
		sql.append(this.param.getString("orgCode")," and ORG_CODE=? ");
		sql.append(this.param.getString("areaCode")," and AREA_CODE=? ");
		sql.append(this.param.getString("phone")," and PHONE=? ");
		sql.append(this.param.getString("customerName")," and CUSTOMER_NAME=? ");
		sql.append(this.param.getString("intergalId")," and ORG_INTEGRAL_ID=? ");
		sql.append(this.param.getString("compensateType")," and COMPENSATE_TYPE=? ");
		sql.append(this.param.getString("compensateMode")," and COMPENSATE_MODE=? ");
		sql.append(this.param.getString("createTimeStar")," and CREATE_TIME>= ? ");
		sql.append(this.param.getString("createTimeEnd")," and CREATE_TIME<=? ");
		sql.append(this.param.getString("createAcc")," and CREATE_ACC=? ");
		sql.append(" ORDER BY CREATE_TIME DESC");
		logger.debug("获取补偿记录列表,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		JSONObject obj=this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		return  obj;
	}
	
}
