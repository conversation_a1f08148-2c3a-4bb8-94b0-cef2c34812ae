package com.yunqu.cc.PeakEnd.dao;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yunqu.cc.PeakEnd.base.AppDaoContext;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.utils.StrUtilMarketing;
/**
 * 
 * <AUTHOR>
 *
 */
@WebObject(name="Wash")
public class WashDao extends AppDaoContext{
	
	private Logger logger = CommLogger.logger;
	/**
	 * @Description:获取洗悦家服务列表
	 * @return JSONObject
	 */
	@WebControl(name = "WASH_GOODS_LIST", type = Types.LIST)
	public JSONObject getWashGoodsList(){
		JSONObject respData = new JSONObject();
		JSONObject data = new JSONObject();
		JSONObject params = new JSONObject();
		params.put("command","orgCardList");
		params.put("customerCode","");
		try {
			IService service = ServiceContext.getService(ServiceID.MIXGW_WEIXINCS_INTEFACE);
			respData = service.invoke(params);
			data.put("pmlist", respData.getJSONArray("pmList"));
			data.put("data", respData.getJSONArray("respData"));
			data.put("type", "LIST");
		} catch (ServiceException e) {
			logger.error("IService - 洗悦家 - 请求失败,请求参数"+JSON.toJSONString(params)+",原因"+e.getMessage());
		}
		return data;
	}
	
	
	/**
	 * @Description: 营销资料查询 - 短信发送 - 优惠券
	 * @return JSONObject
	 * 
	 */
	@WebControl(name = "DICT_COUPON_CARD", type = Types.RECORD)
	public JSONObject getArchivesTypeDict(){
		JSONObject respDate = new JSONObject();
		JSONObject data = new JSONObject();
		JSONObject prams = new JSONObject();
		String agentCode = StrUtilMarketing.formatNull(getMethodParam(0), "");
		String cardId = StrUtilMarketing.formatNull(getMethodParam(1), "");
		prams.put("agentCode", agentCode);//员工号
		prams.put("cardId", cardId);//卡券id
		prams.put("command",ServiceCommand.MIXGW_WEIXINCS_GETCARD);//获取洗悦家优惠券接口
		try {
			IService service = ServiceContext.getService(ServiceID.MIXGW_WEIXINCS_INTEFACE);//洗悦家接口  Jdk 反射invoke
			respDate = service.invoke(prams);
			data.put("data", respDate);
		} catch (ServiceException e) {
			logger.error("IService - 洗悦家 - 获取优惠券失败,请求参数"+JSON.toJSONString(prams)+",原因"+e.getMessage());
		}
		return data;
	}
	/**
	 * @Description:获取洗悦家卡券类型
	 * @return
	 */
	@WebControl(name="DICT_COUPON_CARDTYPE",type=Types.DICT)
	public JSONObject getCardType(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		JSONObject data = new JSONObject();
		String skuCode = StrUtilMarketing.formatNull(getMethodParam(0), "");
		skuCode=skuCode.replaceAll(";", ",");
		obj.put("command",ServiceCommand.MIXGW_WEIXINCS_GETCARDTYPE);
//		obj.put("command","MIXGW_WEIXINCS_GETCARDLIST");//2021新接口
		obj.put("skuCodeList", skuCode);
		obj.put("orderAmount", "");
		obj.put("skuCnt", "");
		try {
			IService service = ServiceContext.getService(ServiceID.MIXGW_WEIXINCS_INTEFACE);
			result = service.invoke(obj);
			if(result.get("data")!=null&&result.getJSONObject("data").get("data")!=null){
				JSONObject jsonObject = result.getJSONObject("data");
				JSONArray jsonArray = jsonObject.getJSONArray("data");
				for(Object ob:jsonArray){
						JSONObject json = JSON.parseObject(ob.toString());
						if(json.get("cardId")!=null&&json.get("cardName")!=null){
							data.put(json.get("cardId").toString(), json.get("cardName").toString());
						}
					}
			}
		} catch (ServiceException e) {
			logger.error("IService - 洗悦家 - 获取卡券类型失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		 JSONObject resultJson = new JSONObject();
		 resultJson.put("data", data);
		return 		resultJson;
	}

}
