package com.yunqu.cc.PeakEnd.dao;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.PeakEnd.base.AppDaoContext;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.service.OrgIntergalStatService;

/**
 * 事业部金额配置类
 * <AUTHOR>
 */
@WebObject(name = "orgIntergalDao")
public class OrgIntergalDao extends AppDaoContext{

	private Logger logger = CommLogger.logger;
	
	
	@WebControl(name="orgIntergalConfigList",type=Types.LIST)
	public JSONObject orgIntergalConfigList() {
		EasySQL sql = this.getEasySQL("select * from C_NO_ORG_INTEGRAL WHERE 1=1 ");
		sql.append(this.param.getString("orgCode")," and ORG_CODE=? ");
		sql.append(this.param.getString("warnEmail")," and WARN_EMAIL=? ");
		sql.append(this.param.getString("openWarn")," and OPEN_WARN=? ");
		sql.append(this.param.getString("status")," and STATUS=? ");
		sql.append(this.param.getString("createTimeStar")," and CREATE_TIME>= ? ");
		sql.append(this.param.getString("createTimeEnd")," and CREATE_TIME<=? ");
		sql.append(this.param.getString("createAcc")," and CREATE_ACC=? ");
		sql.append(" ORDER BY VALID_END_TIME DESC,CREATE_TIME DESC");
		logger.debug("获取事业部金额配置列表,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		JSONObject obj=this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		return  obj;
	}
	
	@WebControl(name="getOrgIntergalConfigDetail",type=Types.RECORD)
	public JSONObject getOrgIntergalConfigDetail(){
		return this.queryForRecord(new EasyRecord("C_NO_ORG_INTEGRAL","ID").setPrimaryValues(param.getString("orgIntergal.ID")));
	}
	
	@WebControl(name="getOrgIntergalSummary",type=Types.RECORD)
	public JSONObject getOrgIntergalSummary(){
		String intergalId = param.getString("intergalId");
		OrgIntergalStatService statService = new OrgIntergalStatService();
		JSONObject data = statService.statOrgIntergalById(intergalId);
		JSONObject result = new JSONObject();
		result.put("data", data);
		return result;
	}
	

}
