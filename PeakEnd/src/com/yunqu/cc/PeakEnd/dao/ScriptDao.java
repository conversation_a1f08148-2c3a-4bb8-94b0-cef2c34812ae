package com.yunqu.cc.PeakEnd.dao;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.PeakEnd.base.AppDaoContext;
import com.yunqu.cc.PeakEnd.base.CommLogger;

/**
 * 脚本配置类
 * 
 * <AUTHOR>
 */
@WebObject(name = "scriptDao")
public class ScriptDao extends AppDaoContext {

    private Logger logger = CommLogger.logger;

    @WebControl(name = "scriptList", type = Types.LIST)
    public JSONObject scriptList() {
        EasySQL sql = this.getEasySQL("select * from C_NO_SCRIPT_CONFIG WHERE 1=1 ");
        sql.append(this.param.getString("brandCode"), " and BRAND_CODE=? ");
        sql.append(this.param.getString("ORG_CODE"), " and ORG_CODE=? ");
        sql.append(this.param.getString("prodCode"), " and PROD_CODE=? ");
        sql.append(this.param.getString("compensateType"), " and COMPENSATE_TYPE=? ");
        sql.append(this.param.getString("responsible"), " and RESPONSIBLE=? ");
        sql.append(this.param.getString("status"), " and STATUS=? ");
        sql.append(this.param.getString("createTimeStar"), " and CREATE_TIME>= ? ");
        sql.append(this.param.getString("createTimeEnd"), " and CREATE_TIME<=? ");
        sql.append(this.param.getString("createAcc"), " and CREATE_ACC=? ");
        sql.append(this.param.getString("serviceTypeCode"), " and SERVICE_TYPE_CODE=? ");
        sql.append(this.param.getString("serviceItem1Code"), " and SERVICE_ITEM1_CODE=? ");
        sql.append(this.param.getString("serviceItem2Code"), " and SERVICE_ITEM2_CODE=? ");
        sql.append(this.param.getString("sceneType"), " and SCENE_TYPE=? ");
        sql.append(" ORDER BY CREATE_TIME DESC");
        logger.info("获取脚本配置信息列表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
        JSONObject obj = this.queryForPageList(sql.getSQL(), sql.getParams(), null);
        return obj;
    }

    @WebControl(name = "getScriptDetail", type = Types.RECORD)
    public JSONObject getScriptDetail() {
        return this.queryForRecord(
                new EasyRecord("C_NO_SCRIPT_CONFIG", "ID").setPrimaryValues(param.getString("Script.ID")));
    }

}
