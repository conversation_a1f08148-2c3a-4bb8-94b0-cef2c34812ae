package com.yunqu.cc.PeakEnd.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.OrderCommand;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.DeptModel;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.user.DeptMgr;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.PeakEnd.base.AppDaoContext;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.enums.ErrorBack;
import com.yunqu.cc.PeakEnd.utils.CsUtil;
import com.yunqu.cc.PeakEnd.utils.StringUtil;

@WebObject(name = "common")
public class CommonDao extends AppDaoContext {

	private Logger logger = CommLogger.logger;
	/**
	 * 数据字典
	 * @return
	 */
	@WebControl(name = "getDict", type = Types.DICT)
	public JSONObject getDict() {
		String dictGroupCode = (String)this.getMethodParam(0);
		if(StringUtils.isBlank(dictGroupCode)){
			CommLogger.logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法查询数据字典,请求的数据字典code为空");
			return null;
		}
		String depCode = UserUtil.getUser(request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, dictGroupCode);
	}

	/**
	 * 品类查询
	 * @param level 层级（必填，表示需要查询的层级，有效的值集为1、2。1代表一级产品品类，2代表二级产品品类）
	 * @param parentProdCode 上一级品类编码（层级大于1的时候必填）
	 * @param orgCode 主体编码
	 * @return
	 */
	@WebControl(name="productCode",type=Types.DICT)
	public JSONObject productCode(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_PRODUCT_CODE_TREE);
		param.put("level",1);
		param.put("parentProdCode",StringUtil.formatNull(this.param.getString("parentProdCode")));
		param.put("orgCode",StringUtil.formatNull(this.param.getString("orgCode")));
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result);
	}
	
	/**
	 * 服务请求查询查询
	 * @param queryLevel 层级（必填，表示需要查询的层级，有效的值集为1、2。1代表一级产品品类，2代表二级产品品类）
	 * @param lastServiceRequireItem 上一级品类编码（层级大于1的时候必填）
	 * @param prodCode 输入了产品品类的情况下，根据服务请求和品类关系过滤服务请求，返回该品类对应的服务请求
	 * @param orgCode 主体编码
	 * @return
	 */
	@WebControl(name="serviceRequire",type=Types.DICT)
	public JSONObject serviceRequire(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_SERVICE_REQUIRE_TREE);
		param.put("queryLevel",StringUtil.formatNull(this.param.getString("queryLevel"),"1"));
		//param.put("laseServiceItem",StringUtil.formatNull(this.param.getString("laseServiceItem")));（原始）
		param.put("lastServiceRequireItem",StringUtil.formatNull(this.param.getString("laseServiceItem")));
		param.put("prodCode",StringUtil.formatNull(this.param.getString("prodCode")));
		param.put("orgCode",StringUtil.formatNull(this.param.getString("orgCode")));
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result);
	}
	
	/**
	 * 便民服务查询
	 * @param queryLevel 层级
	 * @param orgCode 主体编码
	 * @param prodCode 输入了产品品类的情况下，根据服务请求和品类关系过滤服务请求，返回该品类对应的服务请求
	 * @param areaCode 区域编码
	 * @param brandCode 品牌编码
	 * @param serviceSubTypeCode 二级服务编码
	 * @return
	 */
	@WebControl(name="getHomeServiceList",type=Types.LIST)
	public JSONObject getHomeServiceList(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command","HomeServiceList");
//			param.put("prodCode","1000");
//			param.put("areaCode","1440304004");
//			param.put("orgCode","CS006");
//			param.put("brandCode","MIDEA");
		param.put("prodCode",StringUtil.formatNull(this.param.getString("prodCode")));
		param.put("areaCode",StringUtil.formatNull(this.param.getString("areaCode")));
		param.put("orgCode",StringUtil.formatNull(this.param.getString("orgCode")));
		param.put("brandCode",StringUtil.formatNull(this.param.getString("brandCode")));
		param.put("serviceSubTypeCode",StringUtil.formatNull(this.param.getString("serviceSubTypeCode")));
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result);
	}
	
	/**
	 * 服务请求第一级查询查询
	 */
	@WebControl(name="serviceRequire1Level",type=Types.DICT)
	public JSONObject serviceRequire1Level(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_SERVICE_REQUIRE_TREE);
		param.put("queryLevel",StringUtil.formatNull(this.param.getString("queryLevel"),"1"));
		//param.put("laseServiceItem",StringUtil.formatNull(this.param.getString("laseServiceItem")));（原始）
		param.put("lastServiceRequireItem",StringUtil.formatNull(this.param.getString("laseServiceItem")));
		param.put("prodCode",StringUtil.formatNull(this.param.getString("prodCode")));
		param.put("orgCode",StringUtil.formatNull(this.param.getString("orgCode")));
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		JSONObject j =  CsUtil.getData(result);
		Map<String,Object> map = new LinkedHashMap<String,Object>();
		JSONArray arr = j.getJSONArray("data");
		System.out.println(this.param.get("contactOrderServTypeCodeState"));	
		if(this.param.get("contactOrderServTypeCodeState")!=null&&"Y".equals(this.param.get("contactOrderServTypeCodeState").toString())){
				for(int i=0; i < arr.size(); i++) {
					JSONObject o = arr.getJSONObject(i);
					if("TS".equals(o.getString("id"))||"BC".equals(o.getString("id"))||"CD".equals(o.getString("id"))){
						map.put(o.getString("id"), o.get("name"));
					}
				}
			}else{
				for(int i=0; i < arr.size(); i++) {
					JSONObject o = arr.getJSONObject(i);
					map.put(o.getString("id"), o.get("name"));
				}
			}
		j.put("data", map);
		return j;
	}
	
	/**
	 * 服务请求第一级查询查询（投诉，催办，补充）
	 */
	@WebControl(name="serviceRequire1Level2",type=Types.DICT)
	public JSONObject serviceRequire1Level2(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_SERVICE_REQUIRE_TREE);
		param.put("queryLevel",StringUtil.formatNull(this.param.getString("queryLevel"),"1"));
		//param.put("laseServiceItem",StringUtil.formatNull(this.param.getString("laseServiceItem")));（原始）
		param.put("lastServiceRequireItem",StringUtil.formatNull(this.param.getString("laseServiceItem")));
		param.put("prodCode",StringUtil.formatNull(this.param.getString("prodCode")));
		param.put("orgCode",StringUtil.formatNull(this.param.getString("orgCode")));
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		JSONObject j =  CsUtil.getData(result);
		Map<String,Object> map = new LinkedHashMap<String,Object>();
		JSONArray arr = j.getJSONArray("data");
		for(int i=0; i < arr.size(); i++) {
			JSONObject o = arr.getJSONObject(i);
			if("TS".equals(o.getString("id"))||"BC".equals(o.getString("id"))||"CD".equals(o.getString("id"))){
				map.put(o.getString("id"), o.get("name"));
			}
		}
		j.put("data", map);
		return j;
	}
	
	/**
	 * 服务请求第一级查询查询
	 */
	@WebControl(name="pageServiceRequire",type=Types.DICT)
	public JSONObject pageServiceRequire(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_PAGE_SERVICE_REQUIRE);
		param.put("queryLevel",StringUtil.formatNull(this.param.getString("queryLevel")));
		param.put("lastServiceRequireItem",StringUtil.formatNull(this.param.getString("laseServiceItem")));
		param.put("prodCode",StringUtil.formatNull(this.param.getString("prodCode")));
		param.put("orgCode",StringUtil.formatNull(this.param.getString("orgCode")));
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result);
	}
	
	/**
	 * 品牌品类关系
	 * @param 委托方编码			clientCode			
	 * @param 品牌编码			brandCode			
	 * @param 品类编码			prodCode			
	 * @param 品类名称			prodName			
	 * @param 主体编码			orgCode	
	 * @return
	 */
	@WebControl(name="productAndBrand",type=Types.LIST)
	public JSONObject productAndBrand(){
		JSONObject obj =  CsUtil.deleteBase(this.param);
		String orgCode =  obj.getString("orgCode");
		if(StringUtil.checkNull(orgCode)){
			orgCode = obj.getString("orgCodeHidden");
		}
		JSONObject result = new JSONObject();
		JSONObject json = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		json.put("command",OrderCommand.COMM_BRAND_PRODUCT_SHIP);
		param.put("clientCode",StringUtil.formatNull(obj.getString("clientCode")));
		param.put("brandCode",StringUtil.formatNull(obj.getString("brandCode")));
		param.put("prodCode",StringUtil.formatNull(obj.getString("prodCode")));
		param.put("prodName",StringUtil.formatNull(obj.getString("prodName")));
		if(StringUtil.formatNull(obj.getString("orgCode")).equals("")){
			param.put("orgCode",StringUtil.formatNull("CS001"));
		}else{
			param.put("orgCode",StringUtil.formatNull(obj.getString("orgCode")));
		}
		json.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(json);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result,Types.LIST);
	}
	
	
	/**
	 * 品牌品类关系
	 * @param 委托方编码			clientCode			
	 * @param 品牌编码			brandCode			
	 * @param 品类编码			prodCode			
	 * @param 品类名称			prodName			
	 * @param 主体编码			orgCode	
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@WebControl(name="productAndBrandToSelect",type=Types.RECORD)
	public JSONObject productAndBrandToSelect(){
		JSONObject obj =  CsUtil.deleteBase(this.param);
		String orgCode =  obj.getString("orgCode");
		if(StringUtil.checkNull(orgCode)){
			orgCode = obj.getString("orgCodeHidden");
		}
		JSONObject result = new JSONObject();
		JSONObject json = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		json.put("command",OrderCommand.COMM_BRAND_PRODUCT_SHIP);
		param.put("clientCode",StringUtil.formatNull(obj.getString("clientCode")));
		param.put("brandCode",StringUtil.formatNull(obj.getString("brandCode")));
		param.put("prodCode",StringUtil.formatNull(obj.getString("prodCode")));
		param.put("prodName",StringUtil.formatNull(obj.getString("prodName")));
		if(StringUtil.formatNull(obj.getString("orgCode")).equals("")){
			param.put("orgCode",StringUtil.formatNull("CS001"));
		}else{
			param.put("orgCode",StringUtil.formatNull(obj.getString("orgCode")));
		}
		json.put("params", param);
		JSONObject resultJson = new JSONObject();
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(json);
			
			Map<String, Object> m=new  HashMap<String, Object>();
			Map<String, String> brandMap=new  HashMap<String, String>();
			if("000".equals(result.get("respCode"))){
				JSONObject respData=(JSONObject) result.get("respData");
				JSONArray jsonArray = respData.getJSONArray("data");
				for(int i=0;i<jsonArray.size();i++){
					brandMap = (Map<String, String>) jsonArray.get(i);
					m.put(brandMap.get("brandCode"), brandMap.get("brandName"));
				}
				
			}
			
			resultJson.put("data", m);//001001001002
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return resultJson;
	}
	
	/**
	 * 【基础】根据上级品类编码获取品类[品牌]接口
	 * @return
	 */
	@WebControl(name="brandByProdCode",type=Types.LIST)
	public JSONObject brandByProdCode(){
		JSONObject obj =  CsUtil.deleteBase(this.param);
		String parentCode =  obj.getString("parentCode");
		String prodName =  obj.getString("prodName");
		if(StringUtil.checkNull(parentCode) && StringUtil.checkNull(prodName)){
			return JSONObject.parseObject(ErrorBack.OK_LIST.getValue());
		}
		String searchType =  obj.getString("searchType");//0 通过品类编号查询；1通过品类名称查询 
		JSONObject result = new JSONObject();
		JSONObject json = new JSONObject();
		if("0".equals(searchType)){
			//obj.put("orgCode","CS007");
			json.put("command",OrderCommand.COMM_BRAND_BY_PRODCODE);
			obj.remove("prodName");
		}else{
			json.put("command",OrderCommand.COMM_BRAND_BY_PRODNAME);
			obj.remove("parentCode");
		}
		obj.remove("searchType");
		json.put("params", obj);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(json);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result,Types.LIST);
	}
	
	/**
	 * 【基础】根据品类名称获取品类[品牌]接口
	 * @return
	 */
	@WebControl(name="brandByProdName",type=Types.LIST)
	public JSONObject listProdByProdName(){
		JSONObject obj =  CsUtil.deleteBase(this.param);
		String prodName =  obj.getString("prodName");
		if(StringUtil.checkNull(prodName)){
			return JSONObject.parseObject(ErrorBack.OK_LIST.getValue());
		}
		obj.remove("parentCode");
		JSONObject result = new JSONObject();
		JSONObject json = new JSONObject();
		//TODO 
		json.put("command",OrderCommand.COMM_BRAND_BY_PRODNAME);
		//json.put("command",OrderCommand.COMM_BRAND_PRODUCT_SHIP);
		json.put("params", obj);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(json);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result,Types.LIST);
	}
	
	///api/wom/sysbasedata/pagebrandlist
	/**
	 * 【基础】获取所有品牌接口
	 * @return
	 */
	@WebControl(name="getBrandlist",type=Types.DICT)
	public JSONObject getBrandlist(){
		JSONObject obj =  new JSONObject();
		JSONObject result = new JSONObject();
		JSONObject json = new JSONObject();
		//TODO 
		json.put("command",OrderCommand.COMM_BRAND_LIST);
		//json.put("command",OrderCommand.COMM_BRAND_PRODUCT_SHIP);
		json.put("params", obj);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(json);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		
		return  CsUtil.getData(result); 	
	}
	/**
	 * 业务类型查询
	 * @param level 层级（必填，表示需要查询的层级，有效的值集为1、2。1代表一级产品品类，2代表二级产品品类）
	 * @param parentProdCode 上一级业务类型编码（层级大于1的时候必填）
	 * @param orgCode 主体编码
	 * @return
	 */
	@WebControl(name="serviceType",type=Types.DICT)
	public JSONObject serviceType(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_SERVICETYPE_TREE);
		param.put("level",1);
		param.put("parentServiceTypeCode",StringUtil.formatNull(this.param.getString("parentServiceTypeCode")));
		param.put("orgCode",StringUtil.formatNull(this.param.getString("orgCode")));
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result);
	}
	
	/**
	 * 基础配置数据查询
	 * @param codeType 根据编码类别查询该类别下所有的编码值及其对应的名称
	 * @param orgCode 主体编码
	 * @return
	 */
	@WebControl(name = "sysCode", type = Types.DICT)
	public JSONObject sysCode(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		String codeType = StringUtil.formatNull(getMethodParam(0), "");
		String orgCode = StringUtil.formatNull(getMethodParam(1), "");
		param.put("codeType", codeType);
		param.put("orgCode", orgCode);
		obj.put("command",OrderCommand.COMM_SYSCODE);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result);
	}
	
	/**
	 * 基础配置数据查询(防止浏览器自动排序)
	 * @return
	 */
	@WebControl(name = "sysCodeSort", type = Types.RECORD)
	public JSONObject sysCodeSort(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		String codeType = StringUtil.formatNull(getMethodParam(0), "");
		String orgCode = StringUtil.formatNull(getMethodParam(1), "");
		param.put("codeType", codeType);
		param.put("orgCode", orgCode);
		obj.put("command",OrderCommand.COMM_SYSCODE);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			JSONObject r = service.invoke(obj);
			result = CsUtil.getData(r,Types.RECORD);
			JSONObject data = result.getJSONObject("data");
			JSONArray arr = new JSONArray();
			for(String key : data.keySet()){
				JSONObject o = new JSONObject();
				o.put("key", key);
				o.put("value", data.get(key));
				arr.add(o);
			}
			result.put("data", arr);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return result;
	}
	
	/**
	 * 产品型号模糊查询
	 * @param prodCode 产品品类
	 * @param brandCode 产品品牌
	 * @param productCode 产品编码
	 * @param productModel 产品型号【模糊】
	 * @param orgCode 主体编码
	 * @return
	 */
	@WebControl(name="proModel",type=Types.LIST)
	public JSONObject proModel(){
		JSONObject obj =  CsUtil.deleteBase(this.param);
		String pageIndex = obj.getString("pageIndex");
		if(!StringUtil.checkNull(pageIndex) && "-1".equals(pageIndex)){
			obj.put("pageIndex", "1");
		}
		JSONObject result = new JSONObject();
		JSONObject request = new JSONObject(); 
		request.put("command",OrderCommand.COMM_PROMODEL);
		request.put("params", obj);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(request);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		JSONObject data = CsUtil.getData(result);
		return CsUtil.getData(result);
	}
	
	/**
	 * 销售单位查询
	 * @return
	 */
	@WebControl(name="storeList",type=Types.LIST)
	public JSONObject storeList(){
		JSONObject obj =  CsUtil.deleteBase(this.param);
		String pageIndex = obj.getString("pageIndex");
		if(!StringUtil.checkNull(pageIndex) && "-1".equals(pageIndex)){
			obj.put("pageIndex", "1");
		}
		JSONObject result = new JSONObject();
		JSONObject request = new JSONObject(); 
		request.put("command",OrderCommand.COMM_STORE_LIST);
		request.put("params", obj);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(request);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result,Types.LIST);
	}
	
	/**
	 * 【地区查询】查询父级地区下的子地区
	 * @return
	 */
	@WebControl(name="regionByParentCode",type=Types.DICT)
	public JSONObject regionByParentCode(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_REGION_BY_PARENT_CODE);
		param.put("parentRegionCode",StringUtil.formatNull(this.param.getString("parentRegionCode")));
		param.put("orgCode",StringUtil.formatNull(this.param.getString("orgCode")));
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result);
	}
	
	/**
	 * 【地区查询】根据区号查询父级地区下的子地区
	 * @return
	 */
	@WebControl(name="regionByParam",type=Types.DICT)
	public JSONObject regionByParam(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_LIST_REGION_BY_PARAM);
		param.put("parentRegionCode",StringUtil.formatNull(this.param.getString("parentRegionCode")));
		param.put("areaNum",StringUtil.formatNull(this.param.getString("areaNum")));
		param.put("orgCode",StringUtil.formatNull(this.param.getString("orgCode")));
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result);
	}
	
	/**
	 * 【地区查询】模糊查询全级地区信息
	 * @return
	 */
	@WebControl(name="regionbykey",type=Types.DICT)
	public JSONObject regionbykey(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_LIST_REGION_BY_KEY);
		param.put("regionDesc",StringUtil.formatNull(this.param.getString("regionDesc")));
		param.put("areaNum",StringUtil.formatNull(this.param.getString("areaNum")));
		param.put("orgCode",StringUtil.formatNull(this.param.getString("orgCode")));
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result);
	}
	
	/**
	 * 网点查询
	 * @return
	 */
	@WebControl(name="website",type=Types.LIST)
	public JSONObject website(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		param.put("orgCode", StringUtil.formatNull(this.param.getString("orgCode")));
		param.put("areaNum", StringUtil.formatNull(this.param.getString("areaNum")));
		param.put("areaCode", StringUtil.formatNull(this.param.getString("areaCode")));
		String pageIndex = this.param.getString("pageIndex");
		if(!StringUtil.checkNull(pageIndex) && "-1".equals(pageIndex)){
			param.put("pageIndex", "1");
		}else{
			param.put("pageIndex", pageIndex);
		}
		param.put("pageSize", StringUtil.formatNull(this.param.getString("pageSize"),"10"));
		//网点信息
		Map<String,Object> unitAccredits = new HashMap<String,Object>();
		unitAccredits.put("prodCode", StringUtil.formatNull(this.param.getString("prodCode")));
		unitAccredits.put("serviceSubTypeCode", StringUtil.formatNull(this.param.getString("serviceType")));
		param.put("unitAccredits", unitAccredits);
		param.put("unitTel", StringUtil.formatNull(this.param.getString("unitTel")));
		param.put("unitName", StringUtil.formatNull(this.param.getString("unitName")));
		param.put("unitAddress", StringUtil.formatNull(this.param.getString("unitAddress")));
		param.put("isPublishFlag","Y");
		String sqfwsFlag = StringUtil.formatNull(this.param.getString("sqfwsFlag"));
		if(sqfwsFlag.equals("N")){
			param.put("sqfwsFlag", "N");
		}else{
			param.put("sqfwsFlag", "Y");
		}
		obj.put("command",OrderCommand.COMM_WEBSITE);
		obj.put("params", param);
		String userAcc = UserUtil.getUser(this.request).getUserAcc();
		logger.info("坐席"+userAcc+"查询网点"+JSON.toJSONString(obj));
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		//JSONObject result = Comm.getInstance().sysCode("1", "1000", codeType, orgCode);
		return CsUtil.getData(result,Types.LIST);
	}
	
	/**
	 * 用户指定网点查询
	 * @return
	 */
	@WebControl(name="userDesignatedWebsite",type=Types.LIST)
	public JSONObject userDesignatedWebsite(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		param.put("orgCode", StringUtil.formatNull(this.param.getString("orgCode")));
		param.put("areaNum", StringUtil.formatNull(this.param.getString("areaNum")));
		param.put("areaCode", StringUtil.formatNull(this.param.getString("areaCode")));
		String pageIndex = this.param.getString("pageIndex");
		if(!StringUtil.checkNull(pageIndex) && "-1".equals(pageIndex)){
			param.put("pageIndex", "1");
		}else{
			param.put("pageIndex", pageIndex);
		}
		param.put("pageSize", StringUtil.formatNull(this.param.getString("pageSize"),"10"));
		//网点信息
		Map<String,Object> unitAccredits = new HashMap<String,Object>();
		unitAccredits.put("prodCode", StringUtil.formatNull(this.param.getString("prodCode")));
		unitAccredits.put("serviceSubTypeCode", StringUtil.formatNull(this.param.getString("serviceType")));
		param.put("unitAccredits", unitAccredits);
		param.put("unitTel", StringUtil.formatNull(this.param.getString("unitTel")));
		param.put("unitName", StringUtil.formatNull(this.param.getString("unitName")));
		param.put("unitAddress", StringUtil.formatNull(this.param.getString("unitAddress")));
		param.put("isPublishFlag","Y");
		String sqfwsFlag = StringUtil.formatNull(this.param.getString("sqfwsFlag"));
		if(sqfwsFlag.equals("N")){
			param.put("sqfwsFlag", "N");
		}else{
			param.put("sqfwsFlag", "Y");
		}
		obj.put("command",OrderCommand.COMM_USERDESIGNATEDWEBSITE);
		obj.put("params", param);
		String userAcc = UserUtil.getUser(this.request).getUserAcc();
		logger.info("坐席"+userAcc+"查询网点"+JSON.toJSONString(obj));
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		//JSONObject result = Comm.getInstance().sysCode("1", "1000", codeType, orgCode);
		return CsUtil.getData(result,Types.LIST);
	}
	/**
	 * 分中心查询
	 * @return
	 */
	@WebControl(name="branchCode",type=Types.DICT)
	public JSONObject branchCode(){
		String orgCode = this.param.getString("orgCode");
		if(StringUtil.checkNull(orgCode)){
			return EasyResult.error(500,"orgCode为空");
		}
		JSONObject result = new JSONObject();
		JSONObject request = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		request.put("command",OrderCommand.COMM_BRANCH_CODE);
		param.put("branchCode",StringUtil.formatNull(this.param.getString("branchCode")));
		param.put("prodName",StringUtil.formatNull(this.param.getString("prodName")));
		param.put("orgCode",StringUtil.formatNull(orgCode));
		Map<String,Object> res = new LinkedHashMap<String,Object>();
		request.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = CsUtil.getData(service.invoke(request));
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(request)+",原因"+e.getMessage());
		}
		JSONArray arr = result.getJSONArray("branchVOs");
		if(arr!=null&&arr.size()>0){
			result.clear();
			result.put("total",arr.size());
			for(Object obj : arr){
				res.put(((JSONObject)obj).getString("branchCode"),((JSONObject)obj).get("branchName"));
			}
			result.put("data", res);
		}
		return result;
	}
	
	/**
	 * 品牌查询
	 * @return
	 */
	@WebControl(name="brandCodeList",type=Types.DICT)
	public JSONObject brandCodeList(){
		String orgCode = this.param.getString("orgCode");
		if(StringUtil.checkNull(orgCode)){
			return EasyResult.error(500,"orgCode为空");
		}
		JSONObject result = new JSONObject();
		JSONObject request = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		request.put("command",OrderCommand.COMM_BRAND_PRODUCT_SHIP);
		param.put("brandCode",StringUtil.formatNull(this.param.getString("brandCode")));
		param.put("prodName",StringUtil.formatNull(this.param.getString("prodName")));
		param.put("orgCode",StringUtil.formatNull(orgCode));
		Map<String,Object> res = new LinkedHashMap<String,Object>();
		request.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = CsUtil.getData(service.invoke(request));
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(request)+",原因"+e.getMessage());
		}
		JSONArray arr = result.getJSONArray("data");
		if(arr!=null&&arr.size()>0){
			List<Object> l = new ArrayList<Object>();
			for(Object obj : arr){
				if(l.contains(obj)){
					continue;
				}
				l.add(obj);
				res.put(((JSONObject)obj).getString("brandCode"),((JSONObject)obj).get("brandName"));
			}
			result.put("total",res.size());
			result.put("data", res);
		}
		return result;
	}
	
	/**
	 * 分中心查询
	 * @return
	 */
	@WebControl(name="getBranchCode",type=Types.DICT)
	public JSONObject getBranchCode(){
		String orgCode = (String) getMethodParam(0);
		if(StringUtil.checkNull(orgCode)){
			return EasyResult.error(500,"orgCode为空");
		}
		JSONObject result = new JSONObject();
		JSONObject request = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		request.put("command",OrderCommand.COMM_BRANCH_CODE);
		param.put("pageIndex",1);
		param.put("pageSize",1000);
		param.put("branchCode",StringUtil.formatNull(this.param.getString("branchCode")));
		param.put("prodName",StringUtil.formatNull(this.param.getString("prodName")));
		param.put("orgCode",StringUtil.formatNull(orgCode));
		Map<String,Object> res = new LinkedHashMap<String,Object>();
		request.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = CsUtil.getData(service.invoke(request));
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(request)+",原因"+e.getMessage());
		}
		JSONArray arr = result.getJSONArray("branchVOs");
		if(arr!=null&&arr.size()>0){
			result.clear();
			result.put("total",arr.size());
			for(Object obj : arr){
				res.put(((JSONObject)obj).getString("branchCode"),((JSONObject)obj).get("branchName"));
			}
			result.put("data", res);
		}
		return result;
	}
	/**
	 * 获取折算类型
	 * @return
	 */
	@WebControl(name="getConvert",type=Types.DICT)
	public JSONObject getConvert(){
		return getDictByQuery("select ID,NAME from C_NO_CONVERT WHERE DATA_TYPE = ? order by  CODE ","3");
	}	
	/**
	 * 自定义主体启用的字典获取（启用：Y）
	 */
	@WebControl(name="customOrgProdDetailsDictY",type=Types.DICT)
	public JSONObject customOrgProdDetailsDictY() {
		String sql="select ID,CUSTOM_ORG_NAME as name from C_NO_CUSTOM_ORG where 1=1 and SF_YN='Y' order by SORT_NUM ,ID";
		return this.getDictByQuery(sql, null);
	}
	/**
	 * 自定义主体字典获取
	 */
	@WebControl(name="customOrgProdDetailsDict",type=Types.DICT)
	public JSONObject customOrgProdDetailsDict() {
		String sql="select ID,CUSTOM_ORG_NAME as name from C_NO_CUSTOM_ORG where 1=1 order by SORT_NUM ,ID";
		return this.getDictByQuery(sql, null);}
	/**
	 * 获取折算类型通过条件
	 * @return
	 */
	@WebControl(name="getConvertByData",type=Types.DICT)
	public JSONObject getConvertByData(){
		String orgCode=getMethodParam(0)==null?"":getMethodParam(0).toString();
		String archiveType=getMethodParam(1)==null?"":getMethodParam(1).toString();
		StringBuffer sql=new StringBuffer("select ID,NAME from C_NO_CONVERT  where 1=1 ");
		List<Object> params = new ArrayList<Object>();
		if(!StringUtil.isNull(getMethodParam(0))){
			sql.append(" and  (ORG_CODE =? or ORG_CODE is null)");
			params.add(getMethodParam(0).toString());
		}
		if(!StringUtil.isNull(getMethodParam(1))){
			sql.append(" and  (ARCHIVES_TYPE =? or ARCHIVES_TYPE is null)");
			params.add(getMethodParam(1).toString());
		}
		sql.append(" order by  CODE ");
		System.out.println(sql.toString());
		JSONObject dictByQuery = getDictByQuery(sql.toString(),params.toArray(new Object[params.size()]));
		return dictByQuery;

	}
	
	/**
	 * 服务政策查询
	 * @return
	 */
	@WebControl(name="servicePolicy",type=Types.LIST)
	public JSONObject servicePolicy(){
		JSONObject result = new JSONObject();
		JSONObject request = new JSONObject(); 
		request.put("sender", Constants.APP_NAME);
		request.put("password", Constants.GW_PASSWORD);
		request.put("serialId",IDGenerator.getIDByCurrentTime(20));
		request.put("command",ServiceCommand.SLINFOGW_SRH_KNOWLEDGE);
		request.put("keyword", this.param.getString("productModel"));
		request.put("srhSource", this.param.getString("srhSource"));
		request.put("userAcc", UserUtil.getUser(this.request).getUserAcc());
		request.put("categoryName", this.param.getString("categoryName"));
		String pageIndex = this.param.getString("pageIndex");
		if(!StringUtil.checkNull(pageIndex) && "-1".equals(pageIndex)){
			request.put("pageNo", "1");
		}else{
			request.put("pageNo", pageIndex);
		}
		request.put("pageSize", this.param.getString("pageSize"));
		try {
			logger.error("服务政策查询,请求参数:"+JSON.toJSONString(request));
			IService service = ServiceContext.getService(ServiceID.SLINFOGW_INTERFACE);
			result = service.invoke(request);
		} catch (ServiceException e) {
			logger.error("IService请求失败,原因:"+e.getMessage(),e);
			return JSONObject.parseObject(ErrorBack.OK_LIST.getValue());
		}
		String respCode = result.getString("respCode");
		if(GWConstants.RET_CODE_SUCCESS.equals(respCode)){
			//System.out.println("返回数据="+result.getJSONObject("respData"));
			JSONObject o = new JSONObject();
			JSONArray arr =result.getJSONArray("content");
			o.put("data", arr);
			o.put("msg", "\u8BF7\u6C42\u6210\u529F!");
			o.put("state", Integer.valueOf(1));
			o.put("pageType", Integer.valueOf(1));
			o.put("pageNumber", result.get("pageNo"));
			o.put("pageSize", result.get("pageSize"));
			o.put("totalRow", result.get("total"));
			o.put("totalPage", result.get("totalPage"));
			o.put("type", Types.LIST );
			return o;
		}else {
			return JSONObject.parseObject(ErrorBack.OK_LIST.getValue());
		}
	}
	
	/**
	 * 获取当前用户折算类型
	 * @return
	 */
	@WebControl(name="getUserConvert",type=Types.DICT)
	public JSONObject getUserConvert(){
		String acc=UserUtil.getUser(this.request).getUserAcc();
		return getDictByQuery("select cnc.ID,cnc.NAME from C_NO_CONVERT_DRAW  cnod left join C_NO_CONVERT cnc  on cnod.CONVERT_ID=cnc.id where DRAW_ACC=? and DATA_TYPE=? and cnc.ID is not null order by  cnc.CODE ",acc,"3");
	}
	
	/**
	 * 获取短信通知模板下拉列表获取
	 * @return
	 */
	@WebControl(name="msgModelList",type=Types.DICT)
	public JSONObject msgModelList(){
		String code = StringUtil.formatNull(getMethodParam(0), "");
	    EasySQL sql=getEasySQL("SELECT b.ID,b.NAME from C_SMS_TEMPLATE_TYPE a");
	    sql.append(" LEFT JOIN C_SMS_TEMPLATE b ON a.ID = b.TYPE_ID")
	       .append(" WHERE a.CODE='"+code+"' ORDER BY b.CREATE_TIME DESC");
	    logger.info("获取短信通知模板下拉列表获取sql="+sql.getSQL());
		return this.getDictByQuery(sql.getSQL(),sql.getParams());
	}
	
	/**
	 * 获取邮件通知模板下拉列表获取
	 * @return
	 */
	@WebControl(name="emailModelList",type=Types.DICT)
	public JSONObject emailModelList(){
		String code = StringUtil.formatNull(getMethodParam(0), "");
	    EasySQL sql=getEasySQL("SELECT b.CODE,b.NAME from C_EMAIL_TEMPLATE_TYPE a");
	    sql.append(" LEFT JOIN C_EMAIL_TEMPLATE b ON a.ID = b.TYPE_ID")
	       .append(" WHERE a.CODE='"+code+"' ORDER BY b.CODE");
	    logger.info("获取邮件通知模板下拉列表获取sql="+sql.getSQL());
		return this.getDictByQuery(sql.getSQL(),sql.getParams());
	}
	
	/**
	 * 获取坐席信息
	 * @return
	 */
	@WebControl(name="getAgentInfo",type=Types.TEXT)
	public JSONObject getAgentInfo(){
		JSONObject obj = new JSONObject();
		Object type = getMethodParam(0);
		if("AGENT_NAME".equals(type)) {
			obj.put("data", UserUtil.getUser(this.request).getUserName());
		}else if("DEPT_CODE".equals(type)) {
			obj.put("data", UserUtil.getUser(this.request).getDeptCode());
		}else if("DEPT_NAME".equals(type)) {
			obj.put("data", UserUtil.getUser(this.request).getDeptName());
		}else if("EP_CODE".equals(type)) {
			obj.put("data", UserUtil.getUser(this.request).getEpCode());
		}else{
			obj.put("data", UserUtil.getUser(this.request).getUserAcc());
		}
		return obj;
	}
	
	
	/**
	 * 品类查询，单独查二级菜单
	 * @return
	 */
	@WebControl(name="productCodeByLevel",type=Types.DICT)
	public JSONObject productCodeByLevel(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_PRODUCT_CODE_BY_LEVEL);
		param.put("level",2);
		param.put("productCodeByLevel",StringUtil.formatNull(this.param.getString("productCodeByLevel")));
		param.put("orgCode",StringUtil.formatNull(this.param.getString("orgCode")));
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result);
	}
	@WebControl(name="sales",type=Types.LIST)
	public JSONObject sales(){
		EasySQL sql = this.getEasySQL(" select * FROM  C_NO_SALES_CENTER_PHONE ");
		sql.append(" where 1=1 ");
		sql.appendLike(param.getString("branchName"), "and branch_Name  like ? ");
		sql.append(param.getString("orgCode"), "and org_Code = ? ");
		sql.append(param.getString("groupPurchase"), "and GROUP_PURCHASE = ? ");
		sql.append(" order by id,PHONE desc ");
		System.out.println(sql.getSQL()+";"+sql.getParams());
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name="emailTitleChange",type=Types.RECORD)
	public JSONObject emailTitleChange(){
		String depCode = UserUtil.getUser(request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, "EMAIL_TITLE_CHANGE");
	}
	
	
	/**
	 * 条码查询接口
	 * @param insideBarcode 内机条码 
	 * @param orgCode 主体改造非必填
	 * @param wholeMachineFlag 整机标识 x:内机 y:外机 z:整
	 * @return
	 */
	@WebControl(name="searchfromsncc",type=Types.DICT)
	public JSONObject searchfromsncc(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_SEARCHFROMSNCC);
		param.put("orgCode",StringUtil.formatNull(this.param.getString("orgCode")));
		param.put("wholeMachineFlag",StringUtil.formatNull(this.param.getString("wholeMachineFlag")));
		param.put("insideBarcode",StringUtil.formatNull(this.param.getString("insideBarcode")));
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return result;
	}
	//获取全部的业务类型
	@WebControl(name="businessType",type=Types.DICT)
	public JSONObject businessType() {
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		JSONObject jsondata=new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_LSERVICE_TYPE);
		param.put("level",1);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		JSONObject json=CsUtil.getData(result);
		JSONArray arr=json.getJSONArray("data");
		JSONObject json1=new JSONObject();
		for (int i = 0; i < arr.size(); i++) {
			JSONObject js=arr.getJSONObject(i);
			json1.put(js.getString("serviceTypeCode"), js.getString("serviceTypeName"));
		}
		jsondata.put("data", json1);
		return jsondata;
		
	}
	//网点反馈项目
	@WebControl(name="feedbackitemByType",type=Types.DICT)
	public JSONObject feedbackitemByType() {
		JSONObject result = new JSONObject();
		JSONObject param1 =this.param;
		JSONObject obj = new JSONObject();
		JSONObject jsondata=new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_FEEDBACKITEM);
		if(getMethodParam(0)!=null&&!getMethodParam(0).equals("")&&!getMethodParam(0).equals("-1")){
			param.put("serviceResult",getMethodParam(0));
			param.put("parentFeedbackItemCode","0");
			param.put("level","1");
		}else if(getMethodParam(0)!=null&&!getMethodParam(0).equals("")&&getMethodParam(0).equals("-1")){
			return jsondata;
		}else{
			param.put("level",1);
			param.put("pageSize",9999);
			param.put("pageIndex",1);
		}
		
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		JSONObject json=CsUtil.getData(result);
		JSONArray arr=json.getJSONArray("list");
		JSONObject json1=new JSONObject(true);
		for (int i = 0; i < arr.size(); i++) {
			JSONObject js=arr.getJSONObject(i);
			json1.put(js.getString("feedbackItemCode"),js.getString("feedbackItemCode")+"_"+ js.getString("feedbackItemName"));
		}
		jsondata.put("data", json1);
		return jsondata;
	}
	//网点反馈项目
	@WebControl(name="feedbackitem",type=Types.DICT)
	public JSONObject feedbackitem() {
		JSONObject result = new JSONObject();
		JSONObject param1 =this.param;
		JSONObject obj = new JSONObject();
		JSONObject jsondata=new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_FEEDBACKITEM);
		if(getMethodParam(0)!=null&&!getMethodParam(0).equals("")&&!getMethodParam(0).equals("-1")){
			param.put("ParentFeedbackItemCode",getMethodParam(0));
			param.put("pageSize",9999);
			param.put("pageIndex",1);
			param.put("level",2);
		}else if(getMethodParam(0)!=null&&!getMethodParam(0).equals("")&&getMethodParam(0).equals("-1")){
			return jsondata;
		}else{
			param.put("level",1);
			param.put("pageSize",9999);
			param.put("pageIndex",1);
		}
		
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		JSONObject json=CsUtil.getData(result);
		JSONArray arr=json.getJSONArray("list");
		JSONObject json1=new JSONObject(true);
		for (int i = 0; i < arr.size(); i++) {
			JSONObject js=arr.getJSONObject(i);
			json1.put(js.getString("feedbackItemCode"),js.getString("feedbackItemCode")+"_"+ js.getString("feedbackItemName"));
		}
		jsondata.put("data", json1);
		return jsondata;
	}

	@WebControl(name = "getLastMin",type = Types.RECORD)
	public JSONObject getLastMin() {
		String time = ConfigUtil.getString(Constants.APP_NAME,"DISTRIBUTE_TIME","3");
		JSONObject obj = new JSONObject();
		obj.put("TIME", time);
		return EasyResult.ok(obj);
	}
	@WebControl(name="getDept",type=Types.RECORD)
	public JSONObject getDept() {
		Object levelO = getMethodParam(0);
		Object parentCodeO = getMethodParam(1);
		
		int level = 4;
		String parentCode = null;
		
		if (levelO!=null) {
			level = Integer.parseInt(levelO+"");
		}
		if (parentCodeO!=null) {
			parentCode = String.valueOf(parentCodeO).trim();
		}
		List<DeptModel> list=DeptMgr.getDeptByLevel(parentCode, level, false);
		Map<String, Object> ma = new HashMap<String, Object>();
		for (DeptModel deptModel : list) {//把值先存map然后在把值丢到json里面
			ma.put(deptModel.getDeptCode(),deptModel.getDeptName());
		}
		JSONObject json = new JSONObject();
		json.put("data",ma);
		return json;
	}
	
	
	@WebControl(name="getDeptAll",type=Types.RECORD)
	public JSONObject getDeptAll() {
		Object levelO = getMethodParam(0);
		Object parentCodeO = getMethodParam(1);
		int level = 4;
		String parentCode = null;
		
		if (levelO!=null) {
			level = Integer.parseInt(levelO+"");
		}
		String pareatCo="";
		if (parentCodeO!=null) {
			parentCode = String.valueOf(parentCodeO).trim();
			if(parentCode.contains(":")){
				String[] parent = parentCode.toString().split(":");
				for (int i = 0; i < parent.length; i++) {
					if(i ==0){
						pareatCo = "'"+parent[i]+"'";
					}else{
						pareatCo += ",'"+parent[i]+"' ";
					} 	
				}
			}else{
				pareatCo=parentCode;
			}
		}
		List<DeptModel> list=getDeptByLevel(pareatCo, level, false);
		Map<String, Object> ma = new HashMap<String, Object>();
		for (DeptModel deptModel : list) {//把值先存map然后在把值丢到json里面
			ma.put(deptModel.getDeptCode(),deptModel.getDeptName());
		}
		JSONObject json = new JSONObject();
		json.put("data",ma);
		return json;
	}
	/**
	 * 获取指定层级的部门
	 * @param parentCode 可以为空
	 * @param level      不能为空，要显示的部门层级
	 * @param loadUser
	 * @return
	 */
	public static List<DeptModel> getDeptByLevel(String parentCode,int level, boolean loadUser){
		List<DeptModel> deptList = new ArrayList<DeptModel>();
		EasyQuery query = ServerContext.getAdminQuery();
		EasySQL sql = new EasySQL();
		sql.append(" SELECT D1.DEPT_CODE,D1.DEPT_ID,D1.DEPT_NAME,D1.P_DEPT_CODE,D1.DEPT_PATH_NAME,D1.IDX_ORDER, ");
		sql.append(" (SELECT COUNT(1) FROM EASI_DEPT_USER DU WHERE DU.DEPT_ID = D1.DEPT_ID) DEPT_USERS, ");
		sql.append(" (SELECT COUNT(1) FROM EASI_DEPT ED WHERE ED.P_DEPT_CODE = D1.DEPT_CODE) SONS ");
		sql.append(" FROM EASI_DEPT D1 WHERE 1=1 ");
		sql.append(level*3, " AND LENGTH(D1.DEPT_CODE) = ? ");
		if(StringUtils.isNotBlank(parentCode)){
			sql.append(" AND D1.P_DEPT_CODE in ("+parentCode+")  ");
		}
		sql.append(" ORDER BY D1.IDX_ORDER ASC  ");
		try {
			
			List<EasyRow> list = query.queryForList(sql.getSQL(), sql.getParams());
			if(CommonUtil.listIsNotNull(list)){
				for(EasyRow row : list){
					DeptModel dept =  DeptModel.fromEasyRow(row);
					if(loadUser){
						dept.setUsers(DeptMgr.getUsersByDeptCode(dept.getDeptCode(),false));
					}
					deptList.add(dept);
				}
			}
			return deptList;
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return null;
	}
}
	
	
