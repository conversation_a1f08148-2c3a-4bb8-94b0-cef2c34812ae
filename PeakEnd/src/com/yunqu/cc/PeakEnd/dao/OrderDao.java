package com.yunqu.cc.PeakEnd.dao;

import java.sql.SQLException;

import com.yq.busi.common.util.PrivacyUtil;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.PeakEnd.base.AppDaoContext;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.sql.OrderDaoSql;

/**
 * 回访单
 */
@WebObject(name = "order")
public class OrderDao extends AppDaoContext {
	
	private EasyQuery getmarQuery(){
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.FRAME_DS);
	} 

	/**
	 * 发布列表
	 * @return
	 */
	@WebControl(name = "list",type = Types.LIST)
	public JSONObject list() {
		//查询列
		String col = "ID,CUSTOMER_NAME,ARCHIVES_TYPE,ARCHIVES_NO,ORG_CODE,BRAND_NAME,BRAND_CODE,AREA_NUM,AREA_NAME,CUSTOMER_ADDRESS,"
				+ "CUSTOMER_MOBILEPHONE1,CUSTOMER_MOBILEPHONE2,CUSTOMER_MOBILEPHONE3,UNIT_NAME,UNIT_CODE,CLIENT_NAME,CLIENT_CODE,"
				+ "SERVICE_ORDER_NO,ORDER_SERV_TYPE_NAME,ORDER_SERV_TYPE_CODE,ORDER_SER_ITEM1_NAME,ORDER_SER_ITEM1_CODE,"
				+ "ORDER_SER_ITEM2_NAME,ORDER_SER_ITEM2_CODE,SERVICE_STATUS,SYN_TIME,RESULT";
		
		EasySQL sql = new EasySQL("SELECT * FROM C_NO_PEAK_END_ORDER WHERE 1=1");
		//条件
		sql.append(param.getString("ARCHIVES_TYPE"),"AND ARCHIVES_TYPE = ?");
		sql.append(param.getString("CLIENT_CODE"),"AND CLIENT_CODE = ?");
		sql.append(param.getString("ORG_CODE"),"AND ORG_CODE = ?");
		sql.append(param.getString("BRAND_CODE"),"AND BRAND_CODE = ?");
		sql.append(param.getString("SYN_START_TIME"),"AND SYN_TIME >= ?",false);
		sql.append(param.getString("SYN_END_TIME"),"AND SYN_TIME <= ?",false);
		sql.append(param.getString("ORDER_SERV_TYPE_CODE"),"AND ORDER_SERV_TYPE_CODE = ?");
		sql.append(param.getString("REVISIT_STATE"),"AND REVISIT_STATE = ?");
		sql.append(param.getString("UNIT_CODE"),"AND UNIT_CODE = ?");
		sql.append(param.getString("RECEIVE_ACC"),"AND RECEIVE_ACC = ?");
		sql.append(param.getString("APPOINT_ACC"),"AND APPOINT_ACC = ?");
		sql.append(param.getString("DATA_SOURCES"),"AND DATA_SOURCES = ?");
		sql.append(param.getString("RECEIVE_START_TIME"),"AND RECEIVE_TIME >= ?");
		sql.append(param.getString("RECEIVE_END_TIME"),"AND RECEIVE_TIME <= ?");
		sql.append(param.getString("SERVICE_ORDER_NO"),"AND SERVICE_ORDER_NO = ?");
		sql.append("ORDER BY SYN_TIME DESC");
		JSONObject result = queryForPageList(sql.getSQL(), sql.getParams());
		JSONObject object = new JSONObject();
		object.put("CUSTOMER_NAME", PrivacyUtil.userName);
		object.put("CUSTOMER_ADDRESS", PrivacyUtil.addr);
		object.put("CUSTOMER_MOBILEPHONE1", PrivacyUtil.phone);
		object.put("CUSTOMER_MOBILEPHONE2", PrivacyUtil.phone);
		object.put("CUSTOMER_MOBILEPHONE3", PrivacyUtil.phone);

		return PrivacyUtil.desensitization(result,object);
	}
	
	/**
	 * 处理列表
	 * @return
	 */
	@WebControl(name = "handleList",type = Types.LIST)
	public JSONObject handleList() {
		
		UserModel user = UserUtil.getUser(request);
		EasySQL sql = new EasySQL("SELECT T1.*,T2.CARD_TYPE,T2.PUBLISH_TIME FROM C_NO_PEAK_END_ORDER T1");
		sql.append("LEFT JOIN C_NO_PEAK_END_PUBLISH T2 ON T1.C_NO_PUBLISH_ID = T2.C_NO_PUBLISH_ID");
		sql.append("WHERE 1=1");
		//条件
		sql.append(param.getString("SYN_START_TIME"),"AND T1.SYN_TIME >= ?");
		sql.append(param.getString("SYN_END_TIME"),"AND T1.SYN_TIME <= ?");
		sql.append(param.getString("ARCHIVES_TYPE"),"AND T1.ARCHIVES_TYPE = ?");
		sql.append(param.getString("CLIENT_CODE"),"AND T1.CLIENT_CODE = ?");
		sql.append(param.getString("ORG_CODE"),"AND T1.ORG_CODE = ?");
		sql.append(param.getString("BRAND_CODE"),"AND T1.BRAND_CODE = ?");
		sql.append(param.getString("ORDER_SERV_TYPE_CODE"),"AND T1.ORDER_SERV_TYPE_CODE = ?");
		sql.append(param.getString("REVISIT_STATE"),"AND T1.REVISIT_STATE = ?");
		sql.append(param.getString("CONVERSION_TYPE"),"AND T1.CONVERSION_TYPE = ?");
		sql.append(param.getString("SERVICE_ORDER_NO"),"AND T1.SERVICE_ORDER_NO = ?");
		sql.append(param.getString("CARD_TYPE"),"AND T2.CARD_TYPE = ?");
		sql.append(param.getString("PUBLISH_START_TIME"),"AND T2.PUBLISH_TIME >= ?");
		sql.append(param.getString("PUBLISH_END_TIME"),"AND T2.PUBLISH_TIME <= ?");
		sql.append(param.getString("CUSTOMER_MOBILEPHONE1"),"AND (T1.CUSTOMER_MOBILEPHONE1 = ? OR T1.CUSTOMER_MOBILEPHONE2 = ? OR T1.CUSTOMER_MOBILEPHONE3 = ?)");
		//判断权限
		boolean flag = checkRole(user.getUserAcc(), "peak_end_order_monitor");
		if(flag) {
			sql.append(param.getString("USER_ACC"),"AND T1.RECEIVE_ACC = ?");
		}else {
			sql.append(user.getUserAcc(),"AND T1.RECEIVE_ACC = ?");
		}
		sql.append("ORDER BY T1.SYN_TIME DESC");
		JSONObject resp = queryForPageList(sql.getSQL(), sql.getParams());
		//权限标记
		resp.put("flag", flag);
		return resp;
	}
	
	/**
	 * 发布列表
	 * @return
	 */
	@WebControl(name = "publishList",type = Types.LIST)
	public JSONObject publishList() {
		EasySQL sql = new EasySQL("SELECT T1.*,(SELECT COUNT(*) FROM C_NO_PEAK_END_APPLY T2 WHERE T2.C_NO_PUBLISH_ID=T1.C_NO_PUBLISH_ID) AS APPLY_NUM FROM C_NO_PEAK_END_PUBLISH T1 WHERE 1=1");
		sql.appendLike(param.getString("PUB_ACC"),"AND T1.PUBLISH_ACC LIKE ?");
		sql.append(param.getString("CONVERSION_TYPE"),"AND T1.CONVERSION_TYPE = ?");
		sql.append(param.getString("PUB_START_TIME"),"AND T1.PUBLISH_TIME >= ?");
		sql.append(param.getString("PUB_END_TIME"),"AND T1.PUBLISH_TIME <= ?");
		sql.append("ORDER BY T1.PUBLISH_TIME DESC");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 结果列表
	 * @return
	 */
	@WebControl(name = "resultList",type = Types.LIST)
	public JSONObject resultList() {
		EasySQL sql = OrderDaoSql.resultListSql(param);
		JSONObject result = queryForPageList(sql.getSQL(), sql.getParams());
		JSONObject object = new JSONObject();
		object.put("CUSTOMER_MOBILEPHONE1", PrivacyUtil.phone);
		object.put("CUSTOMER_NAME", PrivacyUtil.userName);
		object.put("CUSTOMER_ADDRESS", PrivacyUtil.addr);

		return PrivacyUtil.desensitization(result,object);
	}
	/**
	 * 申请列表
	 * @return
	 */
	@WebControl(name = "applyList",type = Types.LIST)
	public JSONObject applyList() {
		EasySQL sql = new EasySQL("SELECT * FROM C_NO_PEAK_END_APPLY WHERE 1=1");
		sql.append(param.getString("id1"),"AND C_NO_PUBLISH_ID = ? ORDER BY REVISIT_NUM");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 分配列表
	 * @return
	 */
	@WebControl(name = "distributeList",type = Types.LIST)
	public JSONObject distributeList() {
		EasySQL sql = new EasySQL("SELECT * FROM C_NO_PEAK_END_DISTRIBUTION WHERE 1=1");
		sql.append(param.getString("id2"),"AND C_NO_PUBLISH_ID = ? ORDER BY RECEIVE_NUM DESC");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	

	public boolean checkRole(String userAcc,String resId) {
		boolean flag = false;
		EasySQL checkSql = new EasySQL("SELECT COUNT(1) FROM EASI_ROLE_USER T1,EASI_ROLE_RES T2 WHERE 1=1");
		checkSql.append("AND T1.ROLE_ID = T2.ROLE_ID");
		checkSql.append(resId,"AND RES_ID = ?");//权限id
		checkSql.append(userAcc,"AND T1.USER_ID = (SELECT USER_ID FROM EASI_USER_LOGIN WHERE USER_ACCT = ?)");
		try {
			int k = getmarQuery().queryForInt(checkSql.getSQL(), checkSql.getParams());
			if(k!=0) {
				flag = true;
			}
		} catch (SQLException e) {
			CommLogger.logger.error(CommonUtil.getClassNameAndMethod(this) + "查询权限出错：" + e.getMessage(),e);
		}
		return flag;
	}
	
}
