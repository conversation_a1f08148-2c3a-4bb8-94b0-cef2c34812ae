package com.yunqu.cc.PeakEnd.inf;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.JsonUtil;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.service.OrgIntergalCacheManager;
import com.yunqu.cc.PeakEnd.service.OrgIntergalStatService;
import com.yunqu.cc.PeakEnd.utils.StringUtil;

public class IntergalStatJobService extends IService{
	private static IntergalStatJobService service = new IntergalStatJobService();
	private EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME,Constants.YW_DS);
	private Logger statLogger = CommLogger.getCommLogger("stat");
	private EasyCache cache = CacheManager.getMemcache();
	private static final String SEND_EMAIL_HOURS= Constants.SEND_EMAIL_HOURS;

	public static IntergalStatJobService getInstance(){
		return service;
	}
	
	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		if("PEAKEND-TIME-TO-WARN".equals(command)){
			return warnJob();
		} else {
			JSONObject result = JsonUtil.createInfRespJson(json);
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "error：不存在的command！");
			return result;
		}
	}
	
	public JSONObject warnJob() throws ServiceException {
		String cacheId = StringUtil.formatNull("JOB_PEAKEND_EMAIL_WARN");
		String value = cache.get(cacheId);
		if(value==null||"".equals(value)){//判断是否存在缓存
			cache.put(cacheId,"1",60*10);//缓存记录 10分钟后清空（防止卡死不执行）
			statLogger.info(Thread.currentThread().getName()+"开始....");
			stat();//统计并检测是否达到阈值
			statLogger.info(Thread.currentThread().getName()+"结束....");
			cache.delete(cacheId);//分配完毕删除缓存 
		}
		return null;
	}
	
	/**
	 * 统计开始
	 * @return
	 */
	public JSONObject stat() {
		OrgIntergalStatService orgIntergalStatService = new OrgIntergalStatService();
		List<JSONObject> emailReceiveList = new ArrayList<JSONObject>();
		String now = EasyDate.getCurrentDateString();
		EasySQL sql = new EasySQL("SELECT * FROM C_NO_ORG_INTEGRAL WHERE 1=1");
		sql.append(now," AND VALID_BEGIN_TIME <= ?");
		sql.append(now," AND VALID_END_TIME >= ?");
		
		List<EasyRow> list;
		try {
			list = query.queryForList(sql.getSQL(), sql.getParams());
			if(list!=null) {
				statLogger.info("统计事业部金额列表："+list.toString());
				for (int i = 0; i <list.size(); i++) {
					EasyRow row = list.get(i);
					String orgCode = row.getColumnValue("ORG_CODE");
					String intergalId = row.getColumnValue("ID");
					String openWarn = row.getColumnValue("OPEN_WARN");
					String warnEmail = row.getColumnValue("WARN_EMAIL");
					String warnContent = row.getColumnValue("WARN_CONTENT");
					JSONObject statModel = orgIntergalStatService.statOrgIntergalByModel(row);
					statLogger.info("统计对象："+statModel.toJSONString());

					//统计信息存进缓存 --五分钟内有效
					OrgIntergalCacheManager.setOrgIntergalModel(statModel, orgCode);
					//发送邮件规则：开启邮件提醒|首次提醒|邮件提醒人不空|邮件提醒内容不空
					if("Y".equals(openWarn)&&StringUtils.isNotBlank(warnEmail)&&StringUtils.isNotBlank(warnContent)) {
						int suprAmount = statModel.getIntValue("suprAmount");
						int thresholdAmount = statModel.getIntValue("thresholdAmount");
						JSONObject receiver = new JSONObject();
						//剩余金额小于等于阈值，邮件提醒
						if(suprAmount<=thresholdAmount) {
							String cacheId = StringUtil.formatNull("SEND_EMAIL_WARN_"+intergalId);
							String value = cache.get(cacheId);
							if(value==null||"".equals(value)){//缓存为空，说明间隔时间已过，可以发送邮件
								receiver.put("warnEmail", warnEmail);
								receiver.put("intergalId", intergalId);
								receiver.put("warnContent", warnContent);
								emailReceiveList.add(receiver);
							}
						}
					}
				}
				//发送预警邮件
				sendEmail(emailReceiveList);
			}else {
				statLogger.info("当前时间内无事业部金额配置项.");
			}
		} catch (Exception e) {
			statLogger.error(e.getMessage(),e);
		}	
		
		return null;
	}
	
	public void sendEmail(List<JSONObject> list) {
		statLogger.info("预警邮箱提醒数量："+list.size());
		if(list.size()>0) {
			for (int i = 0; i < list.size(); i++) {
				JSONObject json = list.get(i);
				String warnEmail = json.getString("warnEmail");
				String warnContent = json.getString("warnContent");
				String intergalId = json.getString("intergalId");
				JSONObject result = new JSONObject();
				JSONObject params = new JSONObject();
				params.put("sender", Constants.APP_NAME);
				params.put("command",ServiceCommand.REQ_SEND_EMAIL);
				params.put("serialId", IDGenerator.getDefaultNUMID());
				params.put("password", Constants.GW_PASSWORD);
				params.put("from", Constants.EMAIL_GW_ACCOUNT);
				params.put("title", "事业部金额使用预警");
				params.put("busiId", intergalId);
				params.put("content", warnContent);
				params.put("to", warnEmail);
				params.put("source", Constants.EMAIL_SOURCE );
				params.put("haveAttachment","N"); //没附件
				params.put("sendTime", EasyCalendar.newInstance().getDateTime("-") );
				params.put("userAcc", "admin@mars");//默认系统管理员发送
				try {
					statLogger.info("发送邮件 IService请求参数"+JSON.toJSONString(params));
					IService service = ServiceContext.getService(ServiceID.EMAIL_INTERFACE);
					result = service.invoke(params);
				} catch (ServiceException e) {
					statLogger.error("IService请求失败,请求参数"+JSON.toJSONString(params)+",原因"+e.getMessage());
				}
				String respCode = result.getString("respCode");
				params.put("errorCode", respCode);
				params.put("errorMsg", result.getString("respDesc"));
				params.put("userDept",  "admin@mars");
				JSONObject dbObj = getEmailObject(params);
				EasyRecord record = new EasyRecord("C_NO_EMAIL_RECORD", "ID").setColumns(dbObj);
				try {
					query.save(record);
					statLogger.info("发送邮件保存成功~~");
				} catch (SQLException e) {
					statLogger.info("发送邮件保存失败！"+e.getMessage());
				}
				if(GWConstants.RET_CODE_SUCCESS.equals(respCode)){
					statLogger.info("发送邮件成功~~");
					String cacheId = StringUtil.formatNull("SEND_EMAIL_WARN_"+intergalId);
					cache.put(cacheId,"1",60*60*Integer.valueOf(SEND_EMAIL_HOURS));//下次再发送间隔时间
					try {
						query.execute("update C_NO_ORG_INTEGRAL set warn_count = warn_count+1 where id = ? ", intergalId);
					} catch (SQLException e) {
						statLogger.error("更新金额配置项发送次数失败");
					}
				}else{
					statLogger.info("发送邮件失败~~");
				}
				
			}
		}
	}
	
	/**
	 * 获取邮件数据库对象
	 * @param email
	 * @return
	 */
	public JSONObject getEmailObject(JSONObject email) {
		JSONObject obj = new JSONObject();
		obj.put("ID",RandomKit.randomStr());
		obj.put("ERROR_CODE", email.getString("errorCode"));
		obj.put("ERROR_MSG", email.getString("errorMsg"));
		obj.put("MODULE_ID", email.getString("moduleId"));
		obj.put("MODULE_FLAG", email.getString("moduleName"));
		obj.put("MODULE_REMARK", email.getString("moduleRemark"));
		obj.put("RECEIVERS", email.getString("to"));
		obj.put("COPY_TO", email.getString("copyTo"));
		obj.put("CONTENT", email.getString("content"));
		obj.put("CREATE_ACC", email.getString("userAcc"));
		obj.put("CREATE_DEPT", email.getString("userDept"));
		obj.put("CREATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
		obj.put("SEND_TIME", email.getString("sendTime"));
		obj.put("HAVA_ATTACHMENT", email.getString("haveAttachment")); //是否有附件
		obj.put("TITLE", email.getString("subject"));
		statLogger.info("发送邮件保存数据="+JSON.toJSONString(obj));
		return obj;
	}

}
