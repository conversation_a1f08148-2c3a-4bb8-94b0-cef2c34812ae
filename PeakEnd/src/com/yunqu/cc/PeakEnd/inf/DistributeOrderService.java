package com.yunqu.cc.PeakEnd.inf;

import java.util.List;

import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.utils.StringUtil;

public class DistributeOrderService {
	private static DistributeOrderService service = new DistributeOrderService();
	private EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME,Constants.YW_DS);
	private Logger distrLogger = CommLogger.getCommLogger("disLog");
	private EasyCache cache = CacheManager.getMemcache();
	
	public static DistributeOrderService getInstance(){
		return service;
	}
	
	public JSONObject invoke() throws ServiceException {
		String cacheId = StringUtil.formatNull("JOB_PEAKEND_REVIST_DISTRIBUTE");
		String value = cache.get(cacheId);
		if(value==null||"".equals(value)){//判断是否存在缓存
			cache.put(cacheId,"1",60*10);//缓存记录 10分钟后清空（防止卡死不执行）
			distrLogger.info(Thread.currentThread().getName()+"开始....");
			distribute();//定时分配
			distrLogger.info(Thread.currentThread().getName()+"结束....");
			cache.delete(cacheId);//分配完毕删除缓存 
		}
		return EasyResult.ok();
	}
	
	/**
	 * 分配
	 * @return
	 */
	public synchronized JSONObject distribute() {
		EasyQuery query = this.query;
		String currTime = EasyCalendar.newInstance().getDateTime("-");//当前时间
		int day = ConfigUtil.getInt(Constants.APP_NAME, "RECEIVE_DATE",7);
		try {
			EasySQL selectSql = new EasySQL("SELECT * FROM C_NO_PEAK_END_PUBLISH WHERE 1=1");
			selectSql.append(currTime,"AND DEADLINE <= ?").append(Constants.HAS_PUBLISHED,"AND PUBLISH_STATUS = ?").append(Constants.PUBLISH_APPOINT,"AND PUBLISH_TYPE != ?");
			List<JSONObject> publishList = query.queryForList(selectSql.getSQL(), selectSql.getParams(),new JSONMapperImpl());
			if(publishList.size()==0) {
				distrLogger.info("无分配资料");
				return EasyResult.error(500,"无分配资料");
			}
			//任务总量
			int taskNum = 0;
			//分配总人数
			int disUserNum = 0;
			for(JSONObject obj:publishList) {
				//成功分配的任务数量
				int successNum = 0;
				try {
					EasySQL userNumSql = new EasySQL("SELECT APPLY_ACC,APPLY_NAME,(SELECT COUNT(*) FROM C_NO_PEAK_END_RESULT")
					.append("WHERE C_NO_PEAK_END_RESULT.VISIT_ACC = C_NO_PEAK_END_APPLY.APPLY_ACC")
					.append("AND VISIT_TIME>to_char(sysdate-" + day + ",'yyyy-mm-dd hh24:mi:ss')) AS REVISIT_NUM")
					.append(obj.getString("C_NO_PUBLISH_ID"),"FROM C_NO_PEAK_END_APPLY WHERE C_NO_PUBLISH_ID = ? ORDER BY REVISIT_NUM,APPLY_TIME");
					//申请人数量，根据回访数升序
					List<JSONObject> applyUserList = query.queryForList(userNumSql.getSQL(), userNumSql.getParams(),new JSONMapperImpl());
					taskNum = StringUtil.getInt(obj.getString("PUBLISH_NUM"));
					if(taskNum%50 == 0) {
						disUserNum = taskNum/50;
					}else {
						disUserNum = taskNum/50 + 1;
					}
					if(disUserNum!=0&&applyUserList.size()<disUserNum) {
						disUserNum = applyUserList.size();
					}
					for(int i=0;disUserNum!=0&&i<disUserNum;i++) {
						//验证当前用户的待回访数量，如果大于100，则当前用户不分配，分配给下一位，没有则回收资料
						if(query.queryForInt("SELECT COUNT(*) FROM C_NO_PEAK_END_ORDER WHERE RECEIVE_ACC = ? AND REVISIT_STATE = ?", new Object[] {applyUserList.get(i).getString("APPLY_ACC"),Constants.HAS_RECEIVED}) >= 100) {
							if(i==(disUserNum-1)) {
								break;
							}
							continue;
						}
						//更新数据
						EasySQL updateSql = new EasySQL("UPDATE C_NO_PEAK_END_ORDER SET");
						updateSql.append(applyUserList.get(i).getString("APPLY_ACC"),"RECEIVE_ACC = ?,")
						.append(currTime,"RECEIVE_TIME = ?,")
						.append(Constants.HAS_RECEIVED,"REVISIT_STATE = ?")
						.append(obj.getString("C_NO_PUBLISH_ID"),"WHERE C_NO_PUBLISH_ID = ?")
						.append(Constants.HAS_PUBLISHED,"AND REVISIT_STATE = ? AND ROWNUM <= 50");
						int disTaskNum = query.executeUpdate(updateSql.getSQL(), updateSql.getParams());
						successNum += disTaskNum;
						distrLogger.info("用户："+ applyUserList.get(i).getString("APPLY_ACC") +"资料分配完成，成功分配数量：" + disTaskNum);
						//写入领取表
						EasyRecord record = new EasyRecord("C_NO_PEAK_END_DISTRIBUTION");
						record.set("ID", RandomKit.uniqueStr());
						record.set("C_NO_PUBLISH_ID", obj.getString("C_NO_PUBLISH_ID"));
						record.set("USER_ACC", applyUserList.get(i).getString("APPLY_ACC"));
						record.set("USER_NAME", applyUserList.get(i).getString("APPLY_NAME"));
						record.set("CREATE_TIME",currTime);
						record.set("RECEIVE_NUM", disTaskNum);
						query.save(record);
						distrLogger.info("用户：" + applyUserList.get(i).getString("APPLY_ACC") + "写入领取记录表");
					}
					//周期回访数更新
					EasySQL updateSql = new EasySQL("UPDATE C_NO_PEAK_END_APPLY T1 SET REVISIT_NUM=(")
					.append("SELECT COUNT(1) FROM C_NO_PEAK_END_RESULT T2 WHERE T1.APPLY_ACC = T2.VISIT_ACC")
					.append("AND T2.VISIT_TIME>to_char(sysdate-"+day+",'yyyy-mm-dd hh24:mi:ss'))")
					.append(obj.getString("C_NO_PUBLISH_ID"),"WHERE T1.C_NO_PUBLISH_ID = ?");
					query.executeUpdate(updateSql.getSQL(), updateSql.getParams());
					//回收未分配的数据
					EasySQL recycleSql = new EasySQL("UPDATE C_NO_PEAK_END_ORDER SET");
					recycleSql.append("RECEIVE_ACC = '',RECEIVE_TIME = '',APPOINT_ACC = '',APPOINT_TIME = '',CONVERSION_TYPE = '',C_NO_PUBLISH_ID = '',")
					.append(Constants.NO_PUBLISH,"REVISIT_STATE = ? WHERE 1=1")
					.append(obj.getString("C_NO_PUBLISH_ID"),"AND C_NO_PUBLISH_ID = ?")
					.append(Constants.HAS_PUBLISHED,"AND REVISIT_STATE = ?");
					int recycleNum = query.executeUpdate(recycleSql.getSQL(), recycleSql.getParams());
					distrLogger.info("本次ID：" + obj.getString("C_NO_PUBLISH_ID") +"资料发布完成，开始回收未分配的资料，成功分配数量：" + successNum + ",回收数量：" + recycleNum);
					//将此发布记录状态改为已分配
					query.executeUpdate("UPDATE C_NO_PEAK_END_PUBLISH SET PUBLISH_STATUS = ? WHERE C_NO_PUBLISH_ID = ?", new Object[] {Constants.HAS_RECEIVED,obj.getString("C_NO_PUBLISH_ID")});
				} catch (Exception e) {
					//分配过程中遇到错误，直接回收数据
					EasySQL recycleSql = new EasySQL("UPDATE C_NO_PEAK_END_ORDER SET");
					recycleSql.append("RECEIVE_ACC = '',RECEIVE_TIME = '',APPOINT_ACC = '',APPOINT_TIME = '',CONVERSION_TYPE = '',C_NO_PUBLISH_ID = '',")
					.append(Constants.NO_PUBLISH,"REVISIT_STATE = ? WHERE 1=1")
					.append(obj.getString("C_NO_PUBLISH_ID"),"AND C_NO_PUBLISH_ID = ?");
					query.executeUpdate(recycleSql.getSQL(), recycleSql.getParams());
					//将此发布记录状态改为已分配
					query.executeUpdate("UPDATE C_NO_PEAK_END_PUBLISH SET PUBLISH_STATUS = ? WHERE C_NO_PUBLISH_ID = ?", new Object[] {Constants.HAS_RECEIVED,obj.getString("C_NO_PUBLISH_ID")});
					distrLogger.info("遇到错误回收本批数据，错误原因：" + e.getMessage()+",开始分配下一批数据",e);
					continue;
				}
			}
		} catch (Exception e) {
			distrLogger.error(CommonUtil.getClassNameAndMethod(this) + "分配出错：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}
}
