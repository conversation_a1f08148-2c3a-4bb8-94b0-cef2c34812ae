package com.yunqu.cc.PeakEnd.inf;

import java.sql.SQLException;

import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.model.base.Order;
import com.yunqu.cc.PeakEnd.model.base.Response;

public class SynPeakEndService extends IService{
	 protected static EasyQuery getQuery()
	  {
		 return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	  }
	 private static Logger logger = Logger.getLogger("syn");
	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		if("synOrder".equals(command)) {
			return synOrder(json);// 服务单完成时css数据同步		
		}else if("updateOrder".equals(command)) {
				return updateOrder(json);// 工单回访的同步新增保内/保外、服务完成状态的信息同步
		}else{
			JSONObject result = new JSONObject();
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "不存在的command,请检查！");
			return result;
		}
	}
	
	public  JSONObject synOrder(JSONObject json){
		 EasyQuery query = this.getQuery();
			Response  response=new Response();
			logger.info("--------->峰终同步接口接收参数"+json.toJSONString());
				try {
					query.begin();
					JSONArray jsarr=json.getJSONArray("data");
					for(int i=0;i<jsarr.size();i++){
						Order order=(Order)JSONObject.toJavaObject(jsarr.getJSONObject(i), Order.class);
						order.setSynTime(EasyDate.getCurrentDateString());//当前时间
						JSONObject parseObject= JSON.parseObject(JSONObject.toJSONString(order));
						parseObject.put("REVISIT_STATE", 0);
						EasyRecord recordinfo = new EasyRecord("C_NO_PEAK_END_ORDER", "ID").setColumns(parseObject);
						query.save(recordinfo);
					}
					query.commit();
					response.setRespCode(GWConstants.RET_CODE_SUCCESS);
					response.setRespDesc("操作成功");		
				} catch (SQLException e) {
					logger.error("--------->峰终同步接口失败"+e.getMessage(),e);
					try {
						query.roolback();
					} catch (SQLException e1) {
						e1.printStackTrace();
					}
					response.setRespCode(GWConstants.RET_CODE_OTHER_EXCEPTION);		
					response.setRespDesc(e.toString());		
					e.printStackTrace();
			}
			return (JSONObject) JSON.toJSON(response);
	}
	/**
	 * 工单回访的同步 保内/保外、服务完成状态的信息更新
	 * @param json
	 * @return
	 */
	public  JSONObject updateOrder(JSONObject json){
		EasyQuery query = this.getQuery();
		Response  response=new Response();
		logger.info("--------->峰终同步接口接收参数"+json.toJSONString());
		try {
			query.begin();
			JSONArray jsarr=json.getJSONArray("data");
			for(int i=0;i<jsarr.size();i++){
				JSONObject obj= jsarr.getJSONObject(i);
				StringBuffer SQL=new StringBuffer();
				SQL.append("UPDATE C_NO_PEAK_END_ORDER SET REVISIT_STATE=? ,BARCODE_TYPE=? ,UPDATE_TIME=? ");
				SQL.append("WHERE SERVICE_ORDER_NO =? ");
				SQL.append("AND REVISIT_STATE!=4  ");
				query.executeUpdate(SQL.toString(),
						new Object[]{obj.get("serviceStatus"),obj.get("barcodeType"),
								EasyDate.getCurrentDateString(),obj.get("serviceOrderNo")});
			}
			query.commit();
			response.setRespCode(GWConstants.RET_CODE_SUCCESS);
			response.setRespDesc("操作成功");		
		} catch (SQLException e) {
			logger.error("--------->峰终同步接口失败"+e.getMessage(),e);
			try {
				query.roolback();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			response.setRespCode(GWConstants.RET_CODE_OTHER_EXCEPTION);		
			response.setRespDesc(e.toString());		
			e.printStackTrace();
		}
		return (JSONObject) JSON.toJSON(response);
	}
	
}
