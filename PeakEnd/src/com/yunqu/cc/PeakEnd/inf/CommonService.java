package com.yunqu.cc.PeakEnd.inf;

import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.JsonUtil;

public class CommonService extends IService{

	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		if("PEAKEND-TIME-TO-DISTRIBUTE".equals(command)){
			return DistributeOrderService.getInstance().invoke();
		} else {
			JSONObject result = JsonUtil.createInfRespJson(json);
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "error：不存在的command！");
			return result;
		}
	}

}
