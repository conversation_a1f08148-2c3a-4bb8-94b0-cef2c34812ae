package com.yunqu.cc.PeakEnd.css;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.OrderCommand;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.utils.CsUtil;
import com.yunqu.cc.PeakEnd.utils.StringUtil;

public class CommMap{
	  protected static EasyQuery getQuery()
	  {
		 return EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);
	  }
	private Logger logger = CommLogger.logger;
			//Map mapNCProductCode = getMapNCProductCode();//品类
			//Map getMapNCSysCode = getMapNCSysCode("ORG_CODE","");//主体
			//Map getMapNCSysCode = getMapNCSysCode("WOM_ARCHIVE_TYPE","");//档案类型
			//Map getMapNCSysCode = getMapNCSysCode("WOM_CUSTOMER_LEVEL","CS001");//用户等级
			//品牌
			//Map mapNCProductCode = getMapNCBrachCode();分中心
			//Map mapNCProductCode = getMapNCWebsite();网点
	public Map getMapNCProductCode(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_PRODUCT_CODE_BY_LEVEL_INVERT);
		param.put("level",2);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService("CSSGW-COMM");
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		JSONObject jsonObject = result.getJSONObject("respData");
		return (Map)result.getJSONObject("respData").getJSONObject("data");
	}
	public Map getMapProductCode(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_PRODUCT_CODE_BY_LEVEL);
		param.put("level",2);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService("CSSGW-COMM");
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		JSONObject jsonObject = result.getJSONObject("respData");
		return (Map)result.getJSONObject("respData").getJSONObject("data");
	}
	public Map getMapBrachCode(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_BRANCH_CODE_ALL);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService("CSSGW-COMM");
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		JSONObject jsonObject = result.getJSONObject("respData");
		return (Map)result.getJSONObject("respData").getJSONObject("data");
	}
	public Map getMapNCBrachCode(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_BRANCH_CODE_ALL_INVERT);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService("CSSGW-COMM");
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		JSONObject jsonObject = result.getJSONObject("respData");
		return (Map)result.getJSONObject("respData").getJSONObject("data");
	}
	public Map getMapWebsite(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_WEBSITE_ALL);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService("CSSGW-COMM");
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		JSONObject jsonObject = result.getJSONObject("respData");
		Map a=(Map)result.getJSONObject("respData").getJSONObject("data");
		return (Map)result.getJSONObject("respData").getJSONObject("data");
	}
	
	public Map getMapNCWebsite(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_WEBSITE_ALL_INVERT);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService("CSSGW-COMM");
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		JSONObject jsonObject = result.getJSONObject("respData");
		if(jsonObject!=null&&jsonObject.isEmpty() ){
			return (Map)result.getJSONObject("respData").getJSONObject("data");
		}else{
			return null;
		}
	}
	public Map getMapSysCode(String codeType,String orgCode){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		param.put("codeType", codeType);
		param.put("orgCode", orgCode);
		obj.put("command",OrderCommand.COMM_SYSCODE);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService("CSSGW-COMM");
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return(Map)CsUtil.getData(result).getJSONObject("data");
	}
	public Map getMapNCSysCode(String codeType,String orgCode){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		param.put("codeType", codeType);
		param.put("orgCode", orgCode);
		obj.put("command",OrderCommand.COMM_SYSCODE_INVERT);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService("CSSGW-COMM");
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return(Map)CsUtil.getData(result).getJSONObject("data");
	}
	public Map<String ,Object> getConvert(){
		Map<String ,Object> map=new HashMap<String ,Object> ();
		  try {
			List<EasyRow> queryForList = getQuery().queryForList("select ID,NAME from C_NO_CONVERT  ", null);
			for(EasyRow row:queryForList){
				map.put(row.getColumnValue("ID"), row.getColumnValue("NAME"));
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return map;
	}
	public Map<String ,Object> getNCConvert(){
		Map<String ,Object> map=new HashMap<String ,Object> ();
		  try {
			List<EasyRow> queryForList = getQuery().queryForList("select ID,NAME from C_NO_CONVERT  ", null);
			for(EasyRow row:queryForList){
				map.put(row.getColumnValue("NAME"), row.getColumnValue("ID"));
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return map;
	}
	public JSONArray getServiceRequire(String queryLevel,String laseServiceItem){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_SERVICE_REQUIRE_TREE);
		param.put("queryLevel",StringUtil.formatNull(queryLevel,"1"));
		//param.put("laseServiceItem",StringUtil.formatNull(this.param.getString("laseServiceItem")));（原始）
		param.put("lastServiceRequireItem",StringUtil.formatNull(laseServiceItem));
		param.put("prodCode",StringUtil.formatNull(""));
		param.put("orgCode",StringUtil.formatNull(""));
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService(Constants.CSSGW_COMM);
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return result.getJSONObject("respData").getJSONArray("data");
	}
	public Map<String ,Object> getNCBrandlist(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command",OrderCommand.COMM_BRAND_LIST);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService("CSSGW-COMM");
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		Map<String, Object> newmap=new HashMap<String, Object>();
	      Map<String, Object> map =(Map)result.getJSONObject("respData").getJSONObject("data");  
	        for (Entry<String, Object> entry : map.entrySet()) {  
	            newmap.put((String) entry.getValue(), entry.getKey());
	        }   
		return newmap;
		//return(Map)CsUtil.getData(result).getJSONObject("data");
	}
	
}