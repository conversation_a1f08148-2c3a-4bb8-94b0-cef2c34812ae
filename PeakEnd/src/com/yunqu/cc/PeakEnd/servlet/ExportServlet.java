package com.yunqu.cc.PeakEnd.servlet;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.util.StrUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.PeakEnd.base.AppBaseServlet;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.css.CommMap;
import com.yunqu.cc.PeakEnd.sql.OrderDaoSql;
import com.yunqu.cc.PeakEnd.sql.PeakEndSql;
import com.yunqu.cc.PeakEnd.utils.StringUtil;

@WebServlet("/servlet/export")
public class ExportServlet extends AppBaseServlet{
	private static final long serialVersionUID = 1L;
	private Logger logger = CommLogger.logger;

	/**
	 * 导出结果
	 * @throws SQLException
	 */
	public void actionForExportResultList() throws SQLException {
		HttpServletRequest request = this.getRequest();
		JSONObject param = requestToJsonObject(request);
		EasySQL sql = OrderDaoSql.resultListSql(param);
		getQuery().setMaxRow(99999);
		String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
		Map<String, Object> dataSources = DictCache.getMapAllDictListByGroupCode(depCode, "DATA_SOURCES");// 回访状态
		List<Map<String, String>> data = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		// 组装表头
		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		List<String> headers = new ArrayList<String>();
		headers.add(" 序号 ");
		headers.add(" 产品主体");
		headers.add(" 产品品类");
		headers.add(" 用户姓名");
		headers.add(" 区号");
		headers.add(" 用户号码");
		headers.add(" 用户地址");
		headers.add(" 回访结果");
		headers.add(" 服务单号");
		headers.add(" 是否结算");
		headers.add(" 档案编码");
		headers.add(" 责任方");
		headers.add(" 回访坐席");
		headers.add(" 回访时间");
		headers.add(" 资料来源");
		List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
		for (String header : headers) {
			ExcelHeaderStyle style = new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			styles.add(style);
		}
		List<List<String>> excelData = new ArrayList<List<String>>();
		int i = 0;
		List<String> list = null;
			for (Map<String, String> map : data) {
				list = new ArrayList<String>();
				list.add(String.valueOf(++i));
				list.add(map.get("CUSTOMER_MOBILEPHONE1"));
				list.add(map.get("PROD_NAME"));
				list.add(map.get("CUSTOMER_NAME"));
				list.add(map.get("AREA_NUM"));
				list.add(map.get("CUSTOMER_MOBILEPHONE1"));
				list.add(map.get("CUSTOMER_ADDRESS"));
				list.add(map.get("VISIT_RESULT"));
				list.add(map.get("SERVICE_ORDER_NO"));
				list.add(map.get("SETTLEMENT"));
				list.add(map.get("ARCHIVES_NO"));
				list.add(map.get("RESPONSIBLE"));
				list.add(map.get("VISIT_ACC"));
				list.add(map.get("VISIT_TIME"));
				list.add(dataSources.get(map.get("ORDER_ORIGIN")) == null ? "" : dataSources.get(map.get("ORDER_ORIGIN")).toString());
				excelData.add(list);
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file, "回访结果列表.xlsx");
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
	}
	
	
	/**
	 * 导出坐席补偿明细
	 * @throws SQLException
	 */
	public void actionForExportPeakEndRecord() throws SQLException {
		HttpServletRequest request = this.getRequest();
		try {
			JSONObject param = requestToJsonObject(request);
			EasySQL sql = PeakEndSql.recordListSql(param);
			getQuery().setMaxRow(99999);
			CommMap commMap=new CommMap();
			Map ORG_CODE = commMap.getMapSysCode("ORG_CODE","");//主体
			String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
			Map<String, Object> compensateModeDict = DictCache.getMapAllDictListByGroupCode(depCode, "PEAKEND_COMPENSATE_MODE");// 补偿项目
			Map<String, Object> compensateTypeDict = DictCache.getMapAllDictListByGroupCode(depCode, "PEAKEND_COMPENSATE_TYPE");// 补偿类型
			Map<String, Object> visitResultDict = DictCache.getMapAllDictListByGroupCode(depCode, "PEAK_END_RESULT");// 回访结果
			Map<String, Object> respModeDict = DictCache.getMapAllDictListByGroupCode(depCode, "PEAK_END_RESPONSIBLE");// 责任人
			Map<String, Object> nonConformanceReasons = DictCache.getMapAllDictListByGroupCode(depCode, "NON_COMPLIANCE_REASONS");// 责任人

			List<Map<String, String>> data = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
			// 组装表头
			File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
			List<String> headers = new ArrayList<String>();
			headers.add(" 序号 ");
			headers.add(" 产品主体");
			headers.add(" 产品品类");
			headers.add(" 产品品牌");
			headers.add(" 服务单号");
			headers.add(" 接入单号");
			headers.add(" 区号");
			headers.add(" 用户姓名");
			headers.add(" 号码");
			headers.add(" 地址");
			headers.add(" 服务请求");
			headers.add(" 服务请求大类");
			headers.add(" 服务请求小类");
			headers.add(" 回访结果");
			headers.add(" 补偿类型");
			headers.add(" 补偿项目");
			headers.add(" 卡券码");
			headers.add(" 卡券名称");
			headers.add(" 卡券金额");
			headers.add(" 责任人");
			headers.add(" 委托方");
			headers.add(" 延保卡号");
			headers.add(" 延保类型");
			headers.add(" 赠送人");
			headers.add(" 运营区域");
			headers.add(" 坐席班组");
			headers.add(" 赠送时间");
			headers.add(" 备注");
			headers.add(" 是否补偿");
			headers.add(" 不符合补偿原因");
			List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
			for (String header : headers) {
				ExcelHeaderStyle style = new ExcelHeaderStyle();
				style.setData(header);
				style.setWidth(3600);
				styles.add(style);
			}
			List<List<String>> excelData = new ArrayList<List<String>>();
			int i = 0;
			List<String> list = null;
			for (Map<String, String> map : data) {
				list = new ArrayList<String>();
				list.add(String.valueOf(++i));
				String orgCode1 = map.get("ORG_CODE");
				if (!orgCode1.isEmpty()) {
					orgCode1 = ORG_CODE.get(orgCode1) != null ? String.valueOf(ORG_CODE.get(orgCode1)) : orgCode1;
				}
				list.add(orgCode1);
				list.add(map.get("PROD_NAME"));
				list.add(map.get("BRAND_NAME"));
				list.add(map.get("SERVICE_ORDER_NO"));
				list.add(StrUtil.isBlank(map.get("CONTACT_ORDER_CODE")) ? map.get("CONTACT_ORDER_ID") : map.get("CONTACT_ORDER_CODE"));
				list.add(map.get("AREA_NUM"));
				list.add(map.get("CUSTOMER_NAME"));
				list.add(map.get("PHONE"));
				list.add(map.get("ADDRESS"));
				list.add(map.get("ORDER_SERV_TYPE_NAME"));
				list.add(map.get("ORDER_SER_ITEM1_NAME"));
				list.add(map.get("ORDER_SER_ITEM2_NAME"));
				list.add(visitResultDict.get(map.get("VISIT_RESULT"))==null?"":visitResultDict.get(map.get("VISIT_RESULT")).toString());
				list.add(compensateTypeDict.get(map.get("COMPENSATE_TYPE"))==null?"":compensateTypeDict.get(map.get("COMPENSATE_TYPE")).toString());
				list.add(compensateModeDict.get(map.get("COMPENSATE_MODE"))==null?"":compensateModeDict.get(map.get("COMPENSATE_MODE")).toString());
				//清洗卷和商城券才有卡卷码
				if(Constants.COMPENSATE_MODE4.equals(map.get("COMPENSATE_MODE"))||Constants.COMPENSATE_MODE5.equals(map.get("COMPENSATE_MODE"))) {
					list.add(map.get("COMPENSATE_NO"));
				}else {
					list.add("");
				}
				list.add(map.get("COMPENSATE_NAME"));
				list.add(map.get("COMPENSATE_AMOUNT"));
				list.add(respModeDict.get(map.get("RESPONSIBLE"))==null?"":respModeDict.get(map.get("RESPONSIBLE")).toString());
				list.add(map.get("CKIENT_NAME"));
				list.add(encryption(map.get("CARD_CODE")));
				list.add(StringUtil.checkNull(map.get("CARD_CODE"))?"":"1年延保");
				list.add(map.get("CREATE_ACC"));
				list.add(map.get("AGENT_AREA_NAME"));
				list.add(map.get("AGENT_DEPT_NAME"));
				list.add(map.get("CREATE_TIME"));
				list.add(map.get("REMARKS"));
				String stateDescription;
				if ("0".equals(map.get("SUBMIT_STATES"))) {
					stateDescription = "已补未提";
				} else if ("1".equals(map.get("SUBMIT_STATES"))) {
					stateDescription = "已补偿";
				} else if ("2".equals(map.get("SUBMIT_STATES"))) {
					stateDescription = "不符合补偿";
				} else { // 假设为"3"
					stateDescription = "应补未补";
				}
				list.add(stateDescription);
				list.add(nonConformanceReasons.get(map.get("NON_CONFORMANCE_REASON"))==null?"":nonConformanceReasons.get(map.get("NON_CONFORMANCE_REASON")).toString());
				excelData.add(list);
			}
			try {
				ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
				renderFile(file, "坐席赠送记录明细.xlsx");
			} catch (Exception e) {
				logger.error(e.getMessage(),e);
			}
		}catch (Exception e){
			logger.error("导出坐席补偿明细失败"+e.getMessage(),e);
			renderJson(EasyResult.fail("导出坐席补偿明细失败"));
//			renderText("导出坐席赠送记录明细失败");
		}

	}
	public String encryption(String val){
			if(StringUtil.checkNull(val)){
				return "";
			}else if(!StringUtil.checkNull(val) && val.length()>9) {
				return desensitization(val, 9, val.length()-4);
			}else {
				return "***";
			}
	}
	public String desensitization(String str, int beginStr, int endStr){
		int len = str.length();
		String leftStr = str.substring(0,beginStr);
		String rightStr = str.substring(endStr,len);
		str = "";
		int i = 0;
		try {
			for (i = 0; i < endStr-beginStr;i++) {
				str = str + '*';
			}
		} catch (Exception e) {
		}
		str = leftStr + str + rightStr;
		return str;
	}
	public static JSONObject requestToJsonObject(HttpServletRequest request) {
		JSONObject requestJson = new JSONObject();
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String[] pv = request.getParameterValues(paramName);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < pv.length; i++) {
                if (pv[i].length() > 0) {
                    if (i > 0) {
                        sb.append(",");
                    }
                    sb.append(pv[i]);
                }
            }	
            requestJson.put(paramName, sb.toString());
        }
        return requestJson;
	}
	
}
