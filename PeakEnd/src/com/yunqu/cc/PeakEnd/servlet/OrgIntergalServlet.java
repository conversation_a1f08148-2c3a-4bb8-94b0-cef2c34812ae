package com.yunqu.cc.PeakEnd.servlet;

import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.PeakEnd.base.AppBaseServlet;
import com.yunqu.cc.PeakEnd.base.CommLogger;

@WebServlet("/servlet/orgIntergal")
public class OrgIntergalServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;
	
	private Logger logger = CommLogger.logger;
	
	public EasyResult actionForOrgIntergalSave() {
		JSONObject entInfo = this.getJSONObject("orgIntergal");
		try {
			String validBeginTime = entInfo.getString("VALID_BEGIN_TIME");
			String validEndTime = entInfo.getString("VALID_END_TIME");
			int intergalLimit = entInfo.getIntValue("INTEGRAL_LIMIT");
			int intergalThreshold = entInfo.getIntValue("INTEGRAL_THRESHOLD");
			String orgCode = entInfo.getString("ORG_CODE");
			if(StringUtils.isBlank(orgCode)) {
				return EasyResult.fail("事业部不为空！");
			}
			if(StringUtils.isBlank(validBeginTime)||StringUtils.isBlank(validEndTime)) {
				return EasyResult.fail("金额有效开始时间或者结束时间不为空！");
			}
			if(intergalThreshold>intergalLimit) {
				return EasyResult.fail("阈值不允许大于金额上限！");
			}
			EasySQL sql = new EasySQL();
			sql.append("select count(1) from C_NO_ORG_INTEGRAL WHERE 1=1 ");
			sql.append(validBeginTime," AND ((VALID_END_TIME >= ? ");
			sql.append(validBeginTime," AND VALID_BEGIN_TIME <=?  )");
			
			sql.append(validEndTime," OR (VALID_END_TIME >= ? ");
			sql.append(validEndTime," AND VALID_BEGIN_TIME <=?  )");
			
			sql.append(validBeginTime," OR (VALID_BEGIN_TIME >= ? ");
			sql.append(validEndTime," AND VALID_END_TIME <=? )");
			
			sql.append(validBeginTime," OR (VALID_BEGIN_TIME <= ? ");
			sql.append(validEndTime," AND VALID_END_TIME >=? ))");
			
			sql.append(orgCode," AND ORG_CODE =? ");
			sql.append(entInfo.getString("ID")," AND ID <> ? ");
			if(this.getQuery().queryForExist(sql.getSQL(), sql.getParams())) {
				return EasyResult.fail( "操作失败，该事业部存在金额有效时间区间重合！");
			}
			EasyRecord record = new EasyRecord("C_NO_ORG_INTEGRAL", "ID").setColumns(entInfo);
			if ("".equals(entInfo.getString("ID"))){
				record.set("ID", RandomKit.randomStr());
				record.set("CREATE_TIME", EasyDate.getCurrentDateString());
				record.set("CREATE_ACC",UserUtil.getUser(this.getRequest()).getUserAcc());
				this.getQuery().save(record);
			} else {
				record.set("ID", entInfo.getString("ID"));
				record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
				record.set("UPDATE_ACC",UserUtil.getUser(this.getRequest()).getUserAcc());
				this.getQuery().update(record);
			}
			return EasyResult.ok("", "操作成功！");
		} catch (Exception e) {
			this.error("操作失败，原因：" + e.getMessage(), e);
			return EasyResult.error(500, "操作失败，原因：" + e.getMessage());
		}
	}

	public EasyResult actionForOrgIntergalDelete() {
		JSONObject json = new JSONObject();
		JSONObject obj = this.getJSONObject();
		JSONArray ids = obj.getJSONArray("ids");
		try {
			EasyRecord record = null;
			for (int i = 0; i < ids.size(); i++) {
				json.put("id", ids.getString(i));
				record = new EasyRecord("C_NO_ORG_INTEGRAL", "ID").setColumns(json);
				this.getQuery().deleteById(record);
			}
			return EasyResult.ok("删除成功！");
		} catch (Exception e) {
			this.error("删除失败，原因：" + e.getMessage(), e);
			return EasyResult.error(500, "删除失败，原因：" + e.getMessage());
		}
	}
	
}
