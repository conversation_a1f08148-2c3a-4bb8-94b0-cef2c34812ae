package com.yunqu.cc.PeakEnd.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.PeakEnd.base.AppBaseServlet;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;

import static org.easitline.common.utils.calendar.EasyDate.getCurrentDateString;

/**
 * 角色现金补偿规则
 */
@WebServlet("/servlet/roleCashScript")
public class RoleCashScriptServlet extends AppBaseServlet {

    private static final Logger LOGGER = CommLogger.logger;

    /**
     * 新增脚本配置，一个角色只能配置一个脚本
     */
    public EasyResult actionForCashScriptSave() {
        JSONObject roleCashScriptParams = this.getJSONObject("cashScript");
        LOGGER.info("新增脚本配置，参数：{" + roleCashScriptParams + "}");

        String roleId = roleCashScriptParams.get("ROLE_ID").toString();
        if (roleId == null) {
            LOGGER.info("**[ERROR：新增角色补偿脚本失败，缺少角色ID参数！]**");
            return EasyResult.error(500, "新增角色补偿脚本失败，原因：参数缺失");
        }

        try {
            // 限制一个角色只能配置一个补偿脚本
            /*
            String sql = "select count(1) from C_NO_ROLE_CASH_SCRIPT";
            EasySQL getScriptByRoleIdSql = new EasySQL();
            getScriptByRoleIdSql.append(sql);
            getScriptByRoleIdSql.append(roleId, "where ROLE_ID = ?");
            if (this.getQuery().queryForExist(getScriptByRoleIdSql.getSQL(), getScriptByRoleIdSql.getParams())) {
                return EasyResult.error(500, "添加失败，当前角色已经存在补偿脚本，无法添加");
            }
            */

            // 新增补偿脚本
            roleCashScriptParams.put("ROLE_ID", roleId);
            EasyRecord ScriptConfigRecord = new EasyRecord("C_NO_ROLE_CASH_SCRIPT", "ID").setColumns(roleCashScriptParams);
            ScriptConfigRecord.set("ID", RandomKit.randomStr());
            ScriptConfigRecord.set("CREATE_TIME", getCurrentDateString());
            ScriptConfigRecord.set("CREATE_ACC", UserUtil.getUser(this.getRequest()).getUserAcc());
            LOGGER.info("保存现金补偿规则，脚本详情：{" + roleCashScriptParams + "}");

            this.getQuery().save(ScriptConfigRecord);
            return EasyResult.ok("添加成功");
        } catch (Exception e) {
            this.error("添加失败，原因：" + e.getMessage(), e);
            return EasyResult.error(500, "添加失败，原因：" + e.getMessage());
        }
    }

    /**
     * 删除脚本配置
     */
    public EasyResult actionForCashScriptDelete() {
        JSONObject params = this.getJSONObject();
        LOGGER.info("删除脚本配置，参数：{" + params + "}");

        if (params.getString("id") == null
                || StringUtils.isBlank(params.getString("id"))) {
            LOGGER.info("**[ERROR：删除角色补偿脚本失败，缺少id参数！]**");
            return EasyResult.error(500, "删除失败，原因：参数缺失");
        }

        try {
            EasyRecord record = new EasyRecord("C_NO_ROLE_CASH_SCRIPT", "ID");
            record.set("ID", params.getString("id"));
            this.getQuery().deleteById(record);
            return EasyResult.ok("删除成功！");
        } catch (Exception e) {
            this.error("删除失败，原因：" + e.getMessage(), e);
            return EasyResult.error(500, "删除失败，原因：" + e.getMessage());
        }
    }

    /**
     * 修改补偿脚本
     */
    public EasyResult actionForCashScriptUpdate() {
        JSONObject cashScript = this.getJSONObject("cashScript");
        LOGGER.info("修改补偿脚本，参数：{" + cashScript + "}");
        if (cashScript == null || StringUtils.isBlank(cashScript.getString("ID"))) {
            return EasyResult.error(500, "操作失败，参数缺失");
        }

        try {
            EasyRecord record = new EasyRecord("C_NO_ROLE_CASH_SCRIPT", "ID");
            record.setColumns(cashScript);
            this.getQuery().update(record);
            return EasyResult.ok("操作成功");
        } catch (Exception e) {
            this.error("操作失败，原因：" + e.getMessage(), e);
            return EasyResult.error(500, "操作失败，原因：" + e.getMessage());
        }
    }
}
