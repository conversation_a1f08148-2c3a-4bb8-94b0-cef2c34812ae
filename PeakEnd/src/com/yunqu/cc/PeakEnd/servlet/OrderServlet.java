package com.yunqu.cc.PeakEnd.servlet;


import java.io.File;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;

import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.PeakEnd.base.AppBaseServlet;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.css.CommMap;
import com.yunqu.cc.PeakEnd.inf.DistributeOrderService;
import com.yunqu.cc.PeakEnd.utils.CSSInterfaceUtil;
import com.yunqu.cc.PeakEnd.utils.OrderExcelUtil;
import com.yunqu.cc.PeakEnd.utils.StringUtil;

/**
 *回访单servlet
 */
@MultipartConfig
@WebServlet("/servlet/order")
public class OrderServlet extends AppBaseServlet{
	
	private static final long serialVersionUID = 1L;
	private Logger logger = CommLogger.getCommLogger("order");

	/**
	 * 发布
	 * @return
	 */
	public JSONObject actionForPublish() {
		JSONObject data = getJSONObject();
		EasyQuery query = getQuery();
		String synStartTime = data.getString("SYN_START_TIME");
		String synEndTime = data.getString("SYN_END_TIME");
		JSONArray areaCode = new JSONArray(); 
		if("COLMO".equals(data.getString("BRAND_CODE"))) {
			return EasyResult.error(500,"科慕品牌不进行服务补偿回访");
		}
		//验证清洗券
		if("2".equals(data.getString("CARD_TYPE"))) {
			JSONObject washFlag = checkWashCard(data);
			if(!washFlag.getBooleanValue("flag")) {
				return EasyResult.error(500,"未查询到该品类允许发放清洗券的地区，请在通用树中配置");
			}
			areaCode = washFlag.getJSONArray("code");
		}
		int taskNum = 0;
		try {
			int userNum = 0;
			String currentTime = EasyCalendar.newInstance().getDateTime("-");//统一时间
			String publishId = RandomKit.uniqueStr();//发布流水ID
			if(Constants.PUBLISH_SUB.equals(data.getString("PUBLISH_TYPE"))) {//部分发布
				String publishUserNum = data.getString("PUB_USER_NUM");
				if(StringUtil.getInt(publishUserNum)==0) {
					return EasyResult.error(500,"分配人数不正确");
				}
				userNum = StringUtil.getInt(publishUserNum);
			}
			//将表单状态改为已发布
			EasySQL updateSql = new EasySQL("UPDATE C_NO_PEAK_END_ORDER SET");
			updateSql.append(publishId,"C_NO_PUBLISH_ID = ?,");
			updateSql.append(data.getString("CONVERSION_TYPE"),"CONVERSION_TYPE = ?,");
			updateSql.append(Constants.HAS_PUBLISHED,"REVISIT_STATE = ?");
			updateSql.append("WHERE 1=1");
			updateSql.append(synStartTime,"AND SYN_TIME >= ?");//同步开始时间
			updateSql.append(synEndTime,"AND SYN_TIME <= ?");//同步结束时间
			updateSql.append(Constants.NO_PUBLISH,"AND REVISIT_STATE = ?");//回访状态：未发布
			updateSql.append(data.getString("ORG_CODE"),"AND ORG_CODE = ?");//主体
			updateSql.append(data.getString("DATA_SOURCES"),"AND DATA_SOURCES = ?");//数据来源
			updateSql.append(data.getString("ARCHIVE_TYPE"),"AND ARCHIVES_TYPE = ?");//档案类型
			updateSql.append(data.getString("PROD_CODE"),"AND PROD_CODE = ?");//品类
			updateSql.append(data.getString("BRAND_CODE"),"AND BRAND_CODE = ?");//品牌
			updateSql.append(data.getString("CLIENT_CODE"),"AND CLIENT_CODE = ?");//委托方
			updateSql.append(data.getString("CONTACT_ORDER_SERVICE_TYPECODE"),"AND ORDER_SERV_TYPE_CODE = ?");//服务请求
			updateSql.append(data.getString("CONTACT_ORDER_SERVICE_CODE2"),"AND ORDER_SER_ITEM1_CODE = ?");//服务请求2
			if("none".equals(data.getString("RETURN_REPLACE_TYPE"))) {
				updateSql.append("AND RETURN_REPLACE_TYPE = IS NULL");
			}else if("all".equals(data.getString("RETURN_REPLACE_TYPE"))){
				updateSql.append("AND RETURN_REPLACE_TYPE IS NOT NULL");
			}else {
				updateSql.append(data.getString("RETURN_REPLACE_TYPE"),"AND RETURN_REPLACE_TYPE = ?");
			}
			
			if(areaCode.size()>0) {
				updateSql.append("AND AREA_CODE IN ("+StringUtil.joinSqlStr(areaCode)+")");
			}
			
			//updateSql.append("AND ORDER_RESULT IS NOT NULL");//有结果才能发布
			//updateSql.append("AND SERVICE_STATUS != ?");//服务单不等于取消
			if(Constants.PUBLISH_SUB.equals(data.getString("PUBLISH_TYPE"))) {//部分发布
				updateSql.append(userNum*50,"AND ROWNUM <= ?");
			}
			//任务数量
			taskNum = query.executeUpdate(updateSql.getSQL(), updateSql.getParams());
			if(taskNum==0) {
				return EasyResult.error(500,"任务数为0，无资料发布");
			}
			int min = ConfigUtil.getInt(Constants.APP_NAME, "DISTRIBUTE_TIME",3);
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
			Date date = new Date();
	        Calendar calendar = Calendar.getInstance();
	        calendar.setTime(date); //设置为当前时间
	        calendar.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE) + min);
	        date = calendar.getTime();
	        String deadLine = dateFormat.format(date);
			//写入发布记录
			EasyRecord record = new EasyRecord("C_NO_PEAK_END_PUBLISH");
			record.set("C_NO_PUBLISH_ID", publishId);
			record.set("PUBLISH_ACC",UserUtil.getUser(getRequest()).getUserAcc());
			record.set("PUBLISH_TIME",currentTime);
			record.set("PUBLISH_TYPE",data.getString("PUBLISH_TYPE"));//发布类型
			record.set("PUBLISH_NUM",taskNum);
			record.set("DATA_SOURCES",data.getString("DATA_SOURCES"));//数据来源
			record.set("DEADLINE",deadLine);
			record.set("CONVERSION_TYPE",data.getString("CONVERSION_TYPE"));
			record.set("PUBLISH_STATUS",Constants.HAS_PUBLISHED);
			record.set("ORG_CODE",data.getString("ORG_CODE"));
			record.set("ARCHIVE_TYPE",data.getString("ARCHIVE_TYPE"));
			record.set("SORTNAME",data.getString("PROD_CODE"));//品类
			record.set("REGION",data.getString("REGION"));//区域sd、hf
			record.set("CARD_TYPE",data.getString("CARD_TYPE"));//区域sd、hf
			query.save(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "ERROR:" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok(null,"成功发布"+taskNum+"条数据");
	}
	/**
	 * 指派
	 * @return
	 */
	public JSONObject actionForAppoint() {
		JSONObject data = getJSONObject();
		EasyQuery query = getQuery();
		String synStartTime = data.getString("SYN_START_TIME");
		String synEndTime = data.getString("SYN_END_TIME");
		JSONArray areaCode = new JSONArray();
		if("COLMO".equals(data.getString("BRAND_CODE"))) {
			return EasyResult.error(500,"科慕品牌不进行服务补偿回访");
		}
		//验证清洗券
		if("2".equals(data.getString("CARD_TYPE"))) {
			JSONObject washFlag = checkWashCard(data);
			if(!washFlag.getBooleanValue("flag")) {
				return EasyResult.error(500,"未查询到该品类允许发放清洗券的地区，请在通用树中配置");
			}
			areaCode = washFlag.getJSONArray("code");
		}
		if(StringUtils.isBlank((data.getString("USER_ACC")))||StringUtils.isBlank(data.getString("USER_NAME"))) {
			return EasyResult.error(500,"坐席账号数量不正确");
		}
		int taskNum = 0;
		try {
			String[] userAccList = data.getString("USER_ACC").split(",");
			String[] userNameList = data.getString("USER_NAME").split(",");
			String publishId = RandomKit.uniqueStr();//发布流水ID
			String currentTime = EasyCalendar.newInstance().getDateTime("-");//统一时间
			int successNum = 0;
			for(int i = 0;i<userAccList.length;i++) {
				EasySQL updateSql = new EasySQL("UPDATE C_NO_PEAK_END_ORDER SET");
				updateSql.append(data.getString("CONVERSION_TYPE"),"CONVERSION_TYPE = ?,");
				updateSql.append(userAccList[i],"RECEIVE_ACC = ?,");
				updateSql.append(userAccList[i],"APPOINT_ACC = ?,");
				updateSql.append(currentTime,"APPOINT_TIME = ?,");
				updateSql.append(currentTime,"RECEIVE_TIME = ?,");
				updateSql.append(publishId,"C_NO_PUBLISH_ID = ?,");
				updateSql.append(Constants.HAS_RECEIVED,"REVISIT_STATE = ?");//已领取
				updateSql.append("WHERE 1=1");
				updateSql.append(synStartTime,"AND SYN_TIME >= ?").append(synEndTime,"AND SYN_TIME <= ?");
				updateSql.append(Constants.NO_PUBLISH,"AND REVISIT_STATE = ?");//未发布
				updateSql.append(data.getString("ORG_CODE"),"AND ORG_CODE = ?");//主体
				updateSql.append(data.getString("CLIENT_CODE"),"AND CLIENT_CODE = ?");//委托方
				updateSql.append(data.getString("ARCHIVE_TYPE"),"AND ARCHIVES_TYPE = ?");//档案类型
				updateSql.append(data.getString("PROD_CODE"),"AND PROD_CODE = ?");//品类
				updateSql.append(data.getString("BRAND_CODE"),"AND BRAND_CODE = ?");//品牌
				updateSql.append(data.getString("CONTACT_ORDER_SERVICE_TYPECODE"),"AND ORDER_SERV_TYPE_CODE = ?");//服务请求
				updateSql.append(data.getString("CONTACT_ORDER_SERVICE_CODE2"),"AND ORDER_SER_ITEM1_CODE = ?");//服务请求2
				if("none".equals(data.getString("RETURN_REPLACE_TYPE"))) {
					updateSql.append("AND RETURN_REPLACE_TYPE = IS NULL");
				}else if("all".equals(data.getString("RETURN_REPLACE_TYPE"))){
					updateSql.append("AND RETURN_REPLACE_TYPE IS NOT NULL");
				}else {
					updateSql.append(data.getString("RETURN_REPLACE_TYPE"),"AND RETURN_REPLACE_TYPE = ?");
				}
				if(areaCode.size()>0) {
					updateSql.append("AND AREA_CODE IN ("+StringUtil.joinSqlStr(areaCode)+")");
				}
				updateSql.append(data.getString("PUBLISH_NUM"),"AND ROWNUM <= ?");//发布数量
//				updateSql.append("AND ORDER_RESULT IS NOT NULL");//有结果才能发布
//				updateSql.append("AND SERVICE_STATUS != ?");//服务单不等于取消
				taskNum = query.executeUpdate(updateSql.getSQL(),updateSql.getParams());
				if(taskNum==0) {
					continue;
				}
				EasyRecord record = new EasyRecord("C_NO_PEAK_END_DISTRIBUTION");
				record.set("ID",RandomKit.uniqueStr());
				record.set("USER_ACC",userAccList[i]);
				record.set("USER_NAME",userNameList[i]);
				record.set("C_NO_PUBLISH_ID",publishId);
				record.set("CREATE_TIME",currentTime);
				record.set("RECEIVE_NUM",taskNum);
				query.save(record);
				successNum += taskNum;
			}
			if(successNum==0) {
				return EasyResult.error(500,"任务数为0，无资料发布");
			}
			//写入发布记录
			EasyRecord record = new EasyRecord("C_NO_PEAK_END_PUBLISH");
			record.set("C_NO_PUBLISH_ID", publishId);
			record.set("PUBLISH_ACC",UserUtil.getUser(getRequest()).getUserAcc());
			record.set("PUBLISH_TIME",currentTime);
			record.set("PUBLISH_TYPE",Constants.PUBLISH_APPOINT);
			record.set("PUBLISH_NUM",successNum);
			record.set("DATA_SOURCES",data.getString("DATA_SOURCES"));//数据来源
			record.set("CONVERSION_TYPE",data.getString("CONVERSION_TYPE"));
			record.set("PUBLISH_STATUS",Constants.HAS_RECEIVED);//发布状态
			record.set("ORG_CODE",data.getString("ORG_CODE"));//主体
			record.set("ARCHIVE_TYPE",data.getString("ARCHIVE_TYPE"));//当按类型
			record.set("SORTNAME",data.getString("PROD_CODE"));//品类
			record.set("REGION",data.getString("REGION"));//区域
			record.set("CARD_TYPE",data.getString("CARD_TYPE"));//卡券类型
			query.save(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "指派失败：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok(null,"成功指派"+taskNum+"条数据");
	}
	/**
	 * 回收
	 * @return
	 */
	public JSONObject actionForRecycle() {
		JSONObject data = getJSONObject();
		String synStartTime = data.getString("SYN_START_TIME");
		String synEndTime = data.getString("SYN_END_TIME");
		int taskNum = 0;
		EasySQL sql = new EasySQL("UPDATE C_NO_PEAK_END_ORDER SET");
		sql.append("RECEIVE_ACC = '',RECEIVE_TIME = '',APPOINT_ACC = '',APPOINT_TIME = '',CONVERSION_TYPE = '',C_NO_PUBLISH_ID = '',");
		sql.append(Constants.NO_PUBLISH,"REVISIT_STATE = ?");
		sql.append("WHERE 1=1");
		sql.append(data.getString("CONVERSION_TYPE"),"AND CONVERSION_TYPE = ?");//折算类型
		sql.append(synStartTime,"AND SYN_TIME >= ?").append(synEndTime,"AND SYN_TIME <= ?");
		sql.append(data.getString("REVISIT_RESULT"),"AND REVISIT_RESULT = ?");
		sql.append(data.getString("ORG_CODE"),"AND ORG_CODE = ?");//主体
		sql.append(data.getString("CLIENT_CODE"),"AND CLIENT_CODE = ?");//委托方
		sql.append(data.getString("ARCHIVE_TYPE"),"AND ARCHIVES_TYPE = ?");//档案类型
		sql.append(data.getString("PROD_CODE"),"AND PROD_CODE = ?");//品类
		sql.append(data.getString("BRAND_CODE"),"AND BRAND_CODE = ?");//品牌
		sql.append(data.getString("CONTACT_ORDER_SERVICE_TYPECODE"),"AND ORDER_SERV_TYPE_CODE = ?");//服务请求
		sql.append(data.getString("CONTACT_ORDER_SERVICE_CODE2"),"AND ORDER_SER_ITEM1_CODE = ?");//服务请求2
		if("none".equals(data.getString("RETURN_REPLACE_TYPE"))) {
			sql.append("AND RETURN_REPLACE_TYPE = IS NULL");
		}else if("all".equals(data.getString("RETURN_REPLACE_TYPE"))){
			sql.append("AND RETURN_REPLACE_TYPE IS NOT NULL");
		}else {
			sql.append(data.getString("RETURN_REPLACE_TYPE"),"AND RETURN_REPLACE_TYPE = ?");
		}
		
		sql.append(data.getString("PUBLISH_NUM"),"AND ROWNUM <= ?");//回收数量
		
		if("".equals(data.getString("REVISIT_STATE"))) {
			String status = "'2','3'";
			sql.append("AND REVISIT_STATE IN ( " + status + " )");//回访状态
		}else {
			sql.append(data.getString("REVISIT_STATE"),"AND REVISIT_STATE = ?");//回访状态
		}
		if(!"".equals(data.getString("USER_ACC"))) {
			String[] userAcc = data.getString("USER_ACC").split(",");
			sql.append("AND RECEIVE_ACC IN (" + StringUtil.joinSqlStr(userAcc) + ")");
		}
		sql.append("AND RESULT IS NULL");
		sql.append(data.getString("PUBLISH_NUM"),"AND ROWNUM <= ?");
		try {
			taskNum = getQuery().executeUpdate(sql.getSQL(), sql.getParams());
			if(taskNum==0) {
				return EasyResult.error(500,"任务数为0，无资料回收");
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "回收出错：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok(null,"成功回收"+taskNum+"条数据");
	}
	/**
	 * 申请资料
	 * @return
	 */
	public JSONObject actionForApply() {
		JSONObject data = getJSONObject();
		String synStartTime = data.getString("SYN_START_TIME");
		String synEndTime = data.getString("SYN_END_TIME");
		String userAcc = UserUtil.getUser(getRequest()).getUserAcc();//当前坐席
		String currentTime = EasyCalendar.newInstance().getDateTime("-");//统一时间
		EasyQuery query = getQuery();
		try {
			EasySQL checkSql = new EasySQL("SELECT COUNT(*) FROM C_NO_PEAK_END_ORDER WHERE RECEIVE_ACC = ? AND REVISIT_STATE = ?");
			int waitToRevisitNum = query.queryForInt(checkSql.getSQL(), new Object[] {userAcc,Constants.HAS_RECEIVED,});
			if(waitToRevisitNum>20) {
				return EasyResult.error(500,"待回访数量大于20，无法申请");
			}
			String userNo = UserUtil.getUser(this.getRequest()).getUserNo();
			String center = "SD";//区域
			if("admin@mars".equals(UserUtil.getUser(this.getRequest()).getUserAcc())||userNo==null||"".equals(userNo)){
				 center = "SD";
			}else{
				if(!StringUtil.isInteger(userNo.substring(0, 1))){//第一个字不是数字
					 center = "SD";
				}else{
					 center = Integer.parseInt(userNo.substring(0, 1)) < 5 ? "SD" : "HF";//如果开头小于5为顺德
				}
			}
			EasySQL sql = new EasySQL("SELECT DISTINCT T1.C_NO_PUBLISH_ID FROM C_NO_PEAK_END_ORDER T1");
			sql.append("JOIN C_NO_PEAK_END_PUBLISH T2 ON T1.C_NO_PUBLISH_ID = T2.C_NO_PUBLISH_ID WHERE 1=1");
			sql.append(synStartTime,"AND T1.SYN_TIME >= ?").append(synEndTime,"AND T1.SYN_TIME <= ?");
			sql.append(data.getString("CONVERSION_TYPE"),"AND T2.CONVERSION_TYPE = ?");
			sql.append(center,"AND T2.REGION = ?");
			sql.append(currentTime,"AND T2.DEADLINE >= ?").append(Constants.HAS_PUBLISHED,"AND T1.REVISIT_STATE = ?");
			
			List<JSONObject> dataList = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			if(dataList!=null&&dataList.size()>0) {
				EasyRecord record = new EasyRecord("C_NO_PEAK_END_APPLY");
				for(JSONObject obj:dataList) {
					if(query.queryForInt("SELECT COUNT(1) FROM C_NO_PEAK_END_APPLY WHERE APPLY_ACC = ? AND C_NO_PUBLISH_ID = ?",new Object[] {userAcc,obj.getString("C_NO_PUBLISH_ID")})>0) {
						continue;
					}
					record.set("ID",RandomKit.uniqueStr());
					record.set("C_NO_PUBLISH_ID",obj.getString("C_NO_PUBLISH_ID"));
					record.set("APPLY_ACC",userAcc);
					record.set("APPLY_TIME",currentTime);
					record.set("APPLY_NAME",UserUtil.getUser(getRequest()).getUserName());
					query.save(record);
				}
			}else {
				return EasyResult.error(500,"无资料发布，申请失败");
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "资料申请失败：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok(null,"申请成功");
	}
	
	 /**
	  * 删除未发布的导入数据
	  * @return
	  */
	public EasyResult actionForDel() {
		StringBuffer sb = new StringBuffer();
		JSONObject entInfo = this.getJSONObject();
		String startTime = entInfo.getString("startTime");
		String endTime = entInfo.getString("endTime");
		String ids = entInfo.getString("id");
		if (StringUtils.isBlank(ids)) {
			int executeUpdate = 0;
			String org_code = entInfo.getString("org_code");
			String archives_type = entInfo.getString("archives_type");
			String prod_code = entInfo.getString("prod_code");
			String client_code = entInfo.getString("client_code");
			String brand_code = entInfo.getString("brand_code");

			sb.append("delete from C_NO_PEAK_END_ORDER where DATA_SOURCES=0  and REVISIT_STATE=1 and "
					+ " SYN_TIME>=? and SYN_TIME<=?    ");
			List<String> updateParams = new ArrayList<String>();
			updateParams.add(startTime);
			updateParams.add(endTime);
			if (!"".equals(org_code)) {
				sb.append("  and org_code=? ");
				updateParams.add(org_code);
			}
			if (!"".equals(archives_type)) {
				sb.append("  and archives_type=? ");
				updateParams.add(archives_type);
			}
			if (!"".equals(prod_code)) {
				sb.append("  and prod_code=? ");
				updateParams.add(prod_code);
			}
			if (!"".equals(client_code)) {
				sb.append("  and client_code=? ");
				updateParams.add(client_code);
			}
			if (!"".equals(brand_code)) {
				sb.append("  and brand_code=? ");
				updateParams.add(brand_code);
			}
			Object[] arr = updateParams.toArray(new String[updateParams.size()]);
			try {
				executeUpdate = this.getQuery().executeUpdate(sb.toString(), arr);
			} catch (SQLException e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "删除失败1:" + e.getMessage(),e);
			}
			return EasyResult.ok("成功删除" + executeUpdate + "份数据");
		} else {
			String[] idsList = ids.split(";");
			int num = 0;
			try {
				sb.append(" delete from  C_NO_PEAK_END_ORDER where DATA_SOURCES=0 and REVISIT_STATE=1 and ID=?");
				for (String id : idsList) {
					int executeUpdate = this.getQuery().executeUpdate(sb.toString(),new Object[] {id});
					num += executeUpdate;
				}
			} catch (SQLException e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "删除失败2:" + e.getMessage(),e);
			}
			return EasyResult.ok("成功删除" + num + "份数据");
		}
	}
	

	/**
	 * 下载导入模板
	 */
	public void actionForDownload() {
		String type = getRequest().getParameter("type");
		String path = "";
		if("1".equals(type)) {
			path = "/pages/order/template/order-data.xlsx";
		}else if("2".equals(type)) {
			path = "/pages/order/template/order-result.xlsx";
		}else {
			return;
		}
		File file = new File(this.getRequest().getServletContext().getRealPath(path));
		renderFile(file, "导入模板.xlsx");
	}
	
	/**
	 * 数据导入
	 * @return
	 */
	public JSONObject actionForUpload() {
		String type = getRequest().getParameter("type");
		if("1".equals(type)) {
			return importData();
		}else if("2".equals(type)) {
			return importResult();
		}
		return EasyResult.error();
	}
	
	/**
	 * 结果导入
	 * @return
	 */
	private EasyResult importResult() {
		EasyQuery query = this.getQuery();
		String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
		try {
			query.begin();
			Part part = getFile("file");
			OrderExcelUtil util = new OrderExcelUtil();
			util.process(part.getInputStream());
			List<List<String>> list = new ArrayList<>();
			list.addAll(util.getList());
			boolean scuuess = true;
			String msg = "";
			for (int i = 1; i < list.size(); i++) {
				Map<Object, Object> m = new HashMap<Object, Object>();
				boolean lineScuuess = true;// 判断行内容
				String lineMsg = "";
				String val = "";
				for (int j = 0; j < list.get(i).size(); j++) {
					val = list.get(i).get(j);
					switch (j) {
					case 0:
						if(StringUtils.isBlank(val)) {
							scuuess = false;
							lineScuuess = false;
							lineMsg += " 服务单号不能为空";
							break;
						}
						m.put("SERVICE_ORDER_NO", val);//服务单号
						break;
					case 1:
						if(StringUtils.isBlank(val)) {
							scuuess = false;
							lineScuuess = false;
							lineMsg += " 工单结果不能为空";
							break;
						}
						m.put("ORDER_RESULT", val);//工单结果
						break;
					case 2:
						m.put("BARCODE_TYPE", val);//保内/保外
						break;
					default:break;
					}
				}
				if (scuuess) {
					String str = JSONObject.toJSON(m).toString();
					JSONObject jsonObject = JSONObject.parseObject(str);
					String serviceOrderNo = jsonObject.getString("SERVICE_ORDER_NO");
					jsonObject.remove("SERVICE_ORDER_NO");
					jsonObject.put("UPDATE_TIME", EasyDate.getCurrentDateString());
					jsonObject.put("UPDATE_ACC", UserUtil.getUser(this.getRequest()).getUserAcc());
					EasyRecord record = new EasyRecord("C_NO_PEAK_END_ORDER", "SERVICE_ORDER_NO").setPrimaryValues(serviceOrderNo);
					record.setColumns(jsonObject);
					query.update(record);
				} else {
					if (!lineScuuess) {
						msg += "第" + (i+1) + "行出错：" + lineMsg + "<br>";
					}
				}
			}
			if(list.size()==0|list.size()==1){
				scuuess=false;
				msg="无导入信息";
			}
			if (!scuuess) {
				query.roolback();
				return EasyResult.error(500, msg);
			} else {
				query.commit();
				return EasyResult.ok(null,"导入成功！");
			}
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				logger.error("回滚失败：" + e1.getMessage(), e1);
			}
			logger.error("添加失败，原因：" + e.getMessage(), e);
			return EasyResult.error(500, "导入失败！系统异常！");
		}
	}
	
	/**
	 * 数据导入
	 * @return
	 */
	private EasyResult importData() {
		EasyQuery query = this.getQuery();
		String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
		Map<String, Object> clientName = DictCache.getNCMapByGroupCode(depCode, "CLIENT_NAME");// 委托方
		Map<String, Object> returnReplaceType = DictCache.getNCMapByGroupCode(depCode, "PEAKEND_RETURN_REPLACE_TYPE");// 退换机类型
		CSSInterfaceUtil commMap = new CSSInterfaceUtil();
		JSONObject productCode = commMap.getNCProductCode();//品类
		JSONObject orgCode = commMap.getNCSysCode("ORG_CODE","");//主体
		JSONObject archiveType = commMap.getNCSysCode("WOM_ARCHIVE_TYPE","");//档案类型
		JSONObject mapNCBrandlist = commMap.getNCBrandlist();//品牌
		JSONObject unitList = commMap.getNCWebsite();//网点
		try {
			query.begin();
			Part part = getFile("file");
			OrderExcelUtil util = new OrderExcelUtil();
			util.process(part.getInputStream());
			List<List<String>> list = new ArrayList<>();
			list.addAll(util.getList());
			boolean scuuess = true;// 是否正确
			String msg = "";
			for (int i = 1; i < list.size(); i++) {
				Map<Object, Object> m = new HashMap<Object, Object>();
				boolean lineScuuess = true;// 判断行内容
				String lineMsg = "";
				String val = "";
				if(list.get(i).size()==0) {
					continue;
				}
				for (int j = 0; j < list.get(i).size(); j++) {
					val = list.get(i).get(j);
					switch (j) {
					case 0:
						if (StringUtils.isEmpty(val) || orgCode.get(val) == null) {
							scuuess = false;
							lineScuuess = false;
							lineMsg += " 主体为空或数据出错";
							break;
						}
						m.put("ORG_CODE", orgCode.get(val));
						break;
					case 1:
						if(StringUtils.isEmpty(val)){
							m.put("CLIENT_NAME", "");
							m.put("CLIENT_CODE", "");
						}else{
							if(clientName.get(val) == null){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + " 委托方出错";
								break;
							}
							m.put("CLIENT_NAME", val);
							m.put("CLIENT_CODE", clientName.get(val));
						}
						break;
					case 2:
						if(StringUtils.isEmpty(val)){
							m.put("UNIT_NAME", "");
							m.put("UNIT_CODE", "");
						}else{
							if (unitList.get(val) == null) {
								scuuess = false;
								lineScuuess = false;
								lineMsg += " 服务网点出错";
								break;
							}
							m.put("UNIT_NAME", val);
							m.put("UNIT_CODE", unitList.get(val));
							break;
						}
						break;
					case 3:
						if(StringUtils.isEmpty(val)){
							m.put("ARCHIVES_TYPE", "");
						}else{
							if (archiveType.get(val) == null) {
								scuuess = false;
								lineScuuess = false;
								lineMsg += " 档案类型出错";
								break;
							}
							m.put("ARCHIVES_TYPE", archiveType.get(val));
							break;
						}
						break;
					case 4:
						if (StringUtils.isEmpty(val) || productCode.get(val) == null) {
							scuuess = false;
							lineScuuess = false;
							lineMsg += " 品类出错";
							break;
						}
						m.put("PROD_NAME", val);
						m.put("PROD_CODE", productCode.get(val));
						break;
					case 5:
						if(StringUtils.isEmpty(val)){
							m.put("BRAND_NAME", val);
							m.put("BRAND_CODE", "");//品牌编码
						}else{
							if (mapNCBrandlist.get(val) == null) {
								scuuess = false;
								lineScuuess = false;
								lineMsg += " 品牌出错";
								break;
							}
							m.put("BRAND_NAME", val);
							m.put("BRAND_CODE", mapNCBrandlist.get(val));//品牌编码
						}
						break;
					case 6://型号
						m.put("PRODUCT_MODEL", val);//型号
						break;
					case 7:
						if(StringUtils.isEmpty(val)){
							m.put("RETURN_REPLACE_TYPE","");
						}else {
							if(returnReplaceType.get(val) == null) {
								scuuess = false;
								lineScuuess = false;
								lineMsg += " 退换机类型出错";
								break;
							}
							m.put("RETURN_REPLACE_TYPE", returnReplaceType.get(val));//退换机类型
						}
						break;
					case 8:
						m.put("ARCHIVES_NO", val);//档案编码
						break;
					case 9:
						m.put("BARCODE_TYPE", val);//保内/保外
						break;
					case 10:
//						if(StringUtils.isBlank(val)) {
//							scuuess = false;
//							lineScuuess = false;
//							lineMsg += " 工单结果不能为空";
//							break;
//						}
						m.put("ORDER_RESULT", val);//工单结果
						break;
					case 11:
						if(StringUtils.isEmpty((list.get(i).get(9)))&&StringUtils.isEmpty(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg += "档案编码和服务单号必须填写一个";
							break;
						}
						m.put("SERVICE_ORDER_NO", val);//服务单号
						break;
					case 12:
						if(StringUtils.isEmpty(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg += " 用户姓名不能为空";
							break;
						}
						m.put("CUSTOMER_NAME", val);//用户姓名
						break;
					case 13:
						m.put("AREA_NUM", val);//区号
						break;
					case 14:
						if(StringUtils.isEmpty(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg += " 电话号码1不能为空";
							break;
						}
						m.put("CUSTOMER_MOBILEPHONE1", val);//电话号码1
						break;
					case 15:
						m.put("CUSTOMER_MOBILEPHONE2", val);//电话号码2
						break;
					case 16:
						m.put("CUSTOMER_MOBILEPHONE3", val);//电话号码3
						break;
					case 17:
						m.put("AREA_NAME", val);//区域
						break;
					case 18:
						m.put("AREA_CODE", val);//区域编码
						break;
					case 19:
						if(StringUtils.isEmpty(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg += " 用户地址不能为空";
							break;
						}
						m.put("CUSTOMER_ADDRESS", val);//用户地址
						break;
					case 20:
						if(StringUtils.isEmpty(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg += " 服务请求不能为空";
							break;
						}
						m.put("ORDER_SERV_TYPE_NAME", val);//服务请求类型名称
						break;
					case 21:
						if(StringUtils.isEmpty(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg += " 服务请求编码不能为空";
							break;
						}
						m.put("ORDER_SERV_TYPE_CODE", val);//服务请求类型编码
						break;
					case 22:
						if(StringUtils.isEmpty(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg += " 服务请求1不能为空";
							break;
						}
						m.put("ORDER_SER_ITEM1_NAME", val);//服务请求大类名称
						break;
					case 23:
						if(StringUtils.isEmpty(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg += " 服务请求1编码不能为空";
							break;
						}
						m.put("ORDER_SER_ITEM1_CODE", val);//服务请求大类编码
						break;
					case 24:
						if(StringUtils.isEmpty(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg += " 服务请求2不能为空";
							break;
						}
						m.put("ORDER_SER_ITEM2_NAME", val);//服务请求小类名称
						break;
					case 25:
						if(StringUtils.isEmpty(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg += " 服务请求2编码不能为空";
							break;
						}
						m.put("ORDER_SER_ITEM2_CODE", val);//服务请求小类编码
						break;
					default:break;
					}
				}
				if (scuuess) {
					String str = JSONObject.toJSON(m).toString();
					JSONObject jsonObject = JSONObject.parseObject(str);
					jsonObject.put("ID", RandomKit.randomStr());
					jsonObject.put("CREATE_TIME", EasyDate.getCurrentDateString());
					jsonObject.put("CREATE_ACC", UserUtil.getUser(this.getRequest()).getUserAcc());
					jsonObject.put("SYN_TIME", EasyDate.getCurrentDateString());//同时写入一个同步时间
					jsonObject.put("DATA_SOURCES",Constants.DATA_SOURCE_0);//数据来源：导入
					EasyRecord record = new EasyRecord("C_NO_PEAK_END_ORDER", "ID").setColumns(jsonObject);
					query.save(record);
				} else {
					if (!lineScuuess) {
						msg += "第" + (i+1) + "行出错：" + lineMsg + "<br>";
					}
				}
			}
			if(list.size()==0|list.size()==1){
				scuuess=false;
				msg="无导入信息";
			}
			if (!scuuess) {
				query.roolback();
				return EasyResult.error(500, msg);
			} else {
				query.commit();
				return EasyResult.ok(null,"导入成功！");
			}
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				logger.error("回滚失败：" + e1.getMessage(), e1);
			}
			logger.error("添加失败，原因：" + e.getMessage(), e);
			return EasyResult.error(500, "导入失败！系统异常！");
		}
	}
	
	private JSONObject importData2() {
		EasyQuery query = this.getQuery();
		String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
		Map<String, Object> clientName = DictCache.getNCMapByGroupCode(depCode, "CLIENT_NAME");// 委托方
		Map<String, Object> returnReplaceType = DictCache.getNCMapByGroupCode(depCode, "PEAKEND_RETURN_REPLACE_TYPE");// 退换机类型
		CSSInterfaceUtil commMap = new CSSInterfaceUtil();
		JSONObject productCode = commMap.getNCProductCode();//品类
		JSONObject orgCode = commMap.getNCSysCode("ORG_CODE","");//主体
		JSONObject archiveType = commMap.getNCSysCode("WOM_ARCHIVE_TYPE","");//档案类型
		JSONObject mapNCBrandlist = commMap.getNCBrandlist();//品牌
		JSONObject unitList = commMap.getNCWebsite();//网点
		try {
			query.begin();
			Part part = getFile("file");
			Workbook workbook = WorkbookFactory.create(part.getInputStream());
			List<List<String>> list = new ArrayList<>();
			Sheet sheet = workbook.getSheetAt(0);
			int maxLine = sheet.getLastRowNum();
			int lastCellNum = 0;
			for (int ii = 0; ii <= maxLine; ii++) {
				List<String> rows = new ArrayList<>();
				Row row = sheet.getRow(ii);
				if (row != null && maxLine!=0) {
					if (ii == 0) {
						lastCellNum = row.getLastCellNum();
					}
					for (int j = 0; j < lastCellNum; j++) {
						Cell cell = row.getCell(j);
						if(cell!=null){
							String val = Utils.getCellValue(cell);
							if (StringUtils.isBlank(val)) {//isBlank用于判断单元格是否为空，如果为空，则返回TRUE；否则返回FALSE
								rows.add("");
							} else {
	            				rows.add(val);
							}
						}else{
							rows.add("");
						}
					}
					list.add(rows);
				}else {
					return EasyResult.error(500,"导入失败，导入的Excel为空！");
				}
			}
			boolean success = true;// 是否正确
			String msg = "";
			for (int i = 1; i < list.size(); i++) {
				Map<Object, Object> m = new HashMap<Object, Object>();
				boolean linesuccess = true;// 判断行内容
				String lineMsg = "";
				String val = "";
				for (int j = 0; j < list.get(0).size(); j++) {
					val = list.get(i).get(j);
					switch (j) {
						case 0:
							if (StringUtils.isEmpty(val) || orgCode.get(val) == null) {
								success = false;
								linesuccess = false;
								lineMsg += " 主体信息为空或错误";
								break;
							}
							m.put("ORG_CODE", orgCode.get(val));
							break;
						case 1:
							if(StringUtils.isEmpty(val)){
								m.put("CLIENT_NAME", "");
								m.put("CLIENT_CODE", "");
							}else{
								if(clientName.get(val) == null){
									success = false;
									linesuccess = false;
									lineMsg += " 委托方出错";
									break;
								}
								m.put("CLIENT_NAME", val);
								m.put("CLIENT_CODE", clientName.get(val));
							}
							break;
						case 2:
							if(StringUtils.isEmpty(val)){
								m.put("UNIT_NAME", "");
								m.put("UNIT_CODE", "");
							}else{
								if (unitList.get(val) == null) {
									success = false;
									linesuccess = false;
									lineMsg += " 服务网点出错";
									break;
								}
								m.put("UNIT_NAME", val);
								m.put("UNIT_CODE", unitList.get(val));
							}
							break;
						case 3:
							if(StringUtils.isEmpty(val)){
								m.put("ARCHIVES_TYPE", "");
							}else{
								if (archiveType.get(val) == null) {
									success = false;
									linesuccess = false;
									lineMsg += " 档案类型出错";
									break;
								}
								m.put("ARCHIVES_TYPE", archiveType.get(val));
								break;
							}
							break;
						case 4:
							if (StringUtils.isEmpty(val) || productCode.get(val) == null) {
								success = false;
								linesuccess = false;
								lineMsg += " 品类出错";
								break;
							}
							m.put("PROD_NAME", val);
							m.put("PROD_CODE", productCode.get(val));
							break;
						case 5:
							if(StringUtils.isEmpty(val)){
								m.put("BRAND_NAME", val);
								m.put("BRAND_CODE", "");//品牌编码
							}else{
								if (mapNCBrandlist.get(val) == null) {
									success = false;
									linesuccess = false;
									lineMsg += " 品牌出错";
									break;
								}
								m.put("BRAND_NAME", val);
								m.put("BRAND_CODE", mapNCBrandlist.get(val));//品牌编码
							}
							break;
						case 6://型号
							m.put("PRODUCT_MODEL", val);//型号
							break;
						case 7:
							if(StringUtils.isEmpty(val)){
								m.put("RETURN_REPLACE_TYPE","");
							}else {
								if(returnReplaceType.get(val) == null) {
									success = false;
									linesuccess = false;
									lineMsg += " 退换机类型出错";
									break;
								}
								m.put("RETURN_REPLACE_TYPE", returnReplaceType.get(val));//退换机类型
							}
							break;
						case 8:
							m.put("ARCHIVES_NO", val);//档案编码
							break;
						case 9:
							m.put("BARCODE_TYPE", val);//保内/保外
							break;
						case 10:
//							if(StringUtils.isBlank(val)) {
//								success = false;
//								linesuccess = false;
//								lineMsg += " 工单结果不能为空";
//								break;
//							}
							m.put("ORDER_RESULT", val);//工单结果
							break;
						case 11:
							if(StringUtils.isEmpty((list.get(i).get(9)))&&StringUtils.isEmpty(val)){
								success = false;
								linesuccess = false;
								lineMsg += "档案编码和服务单号必须填写一个";
								break;
							}
							m.put("SERVICE_ORDER_NO", val);//服务单号
							break;
						case 12:
							if(StringUtils.isEmpty(val)){
								success = false;
								linesuccess = false;
								lineMsg += " 用户姓名不能为空";
								break;
							}
							m.put("CUSTOMER_NAME", val);//用户姓名
							break;
						case 13:
							m.put("AREA_NUM", val);//区号
							break;
						case 14:
							if(StringUtils.isEmpty(val)){
								success = false;
								linesuccess = false;
								lineMsg += " 电话号码1不能为空";
								break;
							}
							m.put("CUSTOMER_MOBILEPHONE1", val);//电话号码1
							break;
						case 15:
							m.put("CUSTOMER_MOBILEPHONE2", val);//电话号码2
							break;
						case 16:
							m.put("CUSTOMER_MOBILEPHONE3", val);//电话号码3
							break;
						case 17:
							m.put("AREA_NAME", val);//区域
							break;
						case 18:
							m.put("AREA_CODE", val);//区域编码
							break;
						case 19:
							if(StringUtils.isEmpty(val)){
								success = false;
								linesuccess = false;
								lineMsg += " 用户地址不能为空";
								break;
							}
							m.put("CUSTOMER_ADDRESS", val);//用户地址
							break;
						case 20:
							if(StringUtils.isEmpty(val)){
								success = false;
								linesuccess = false;
								lineMsg += " 服务请求不能为空";
								break;
							}
							m.put("ORDER_SERV_TYPE_NAME", val);//服务请求类型名称
							break;
						case 21:
							if(StringUtils.isEmpty(val)){
								success = false;
								linesuccess = false;
								lineMsg += " 服务请求编码不能为空";
								break;
							}
							m.put("ORDER_SERV_TYPE_CODE", val);//服务请求类型编码
							break;
						case 22:
							if(StringUtils.isEmpty(val)){
								success = false;
								linesuccess = false;
								lineMsg += " 服务请求1不能为空";
								break;
							}
							m.put("ORDER_SER_ITEM1_NAME", val);//服务请求大类名称
							break;
						case 23:
							if(StringUtils.isEmpty(val)){
								success = false;
								linesuccess = false;
								lineMsg += " 服务请求1编码不能为空";
								break;
							}
							m.put("ORDER_SER_ITEM1_CODE", val);//服务请求大类编码
							break;
						case 24:
							if(StringUtils.isEmpty(val)){
								success = false;
								linesuccess = false;
								lineMsg += " 服务请求2不能为空";
								break;
							}
							m.put("ORDER_SER_ITEM2_NAME", val);//服务请求小类名称
							break;
						case 25:
							if(StringUtils.isEmpty(val)){
								success = false;
								linesuccess = false;
								lineMsg += " 服务请求2编码不能为空";
								break;
							}
							m.put("ORDER_SER_ITEM2_CODE", val);//服务请求小类编码
							break;
						default:break;
					}
				}
				if (success) {
					String str = JSONObject.toJSON(m).toString();
					JSONObject jsonObject = JSONObject.parseObject(str);
					jsonObject.put("ID", RandomKit.randomStr());
					jsonObject.put("CREATE_TIME", EasyDate.getCurrentDateString());
					jsonObject.put("CREATE_ACC", UserUtil.getUser(this.getRequest()).getUserAcc());
					jsonObject.put("SYN_TIME", EasyDate.getCurrentDateString());//同时写入一个同步时间
					jsonObject.put("DATA_SOURCES",Constants.DATA_SOURCE_0);//数据来源：导入
					EasyRecord record = new EasyRecord("C_NO_PEAK_END_ORDER", "ID").setColumns(jsonObject);
					query.save(record);
				} else {
					if (!linesuccess) {
						msg += "第" + (i+1) + "行出错：" + lineMsg + "<br>";
					}
				}
			}
			if(list.size()==0|list.size()==1){
				success=false;
				msg="无导入信息";
			}
			
			if (!success) {
				query.roolback();
				return EasyResult.error(500, msg);
			} else {
				query.commit();
				return EasyResult.ok(null,"导入成功！");
			}
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				logger.error("回滚失败");
			}
			logger.error(CommonUtil.getClassNameAndMethod(this)+"导入数据异常:"+e.getMessage(),e);
			return EasyResult.error();
		}
	}
	
	/**
	 * 结果导出
	 * @throws SQLException
	 */
	public void actionForExportResult() throws SQLException {
		HttpServletRequest request = this.getRequest();
		UserModel user = UserUtil.getUser(request);
		EasySQL sql = new EasySQL("SELECT T1.*,T2.CARD_TYPE,T2.PUBLISH_TIME FROM C_NO_PEAK_END_ORDER T1");
		sql.append("LEFT JOIN C_NO_PEAK_END_PUBLISH T2 ON T1.C_NO_PUBLISH_ID = T2.C_NO_PUBLISH_ID");
		sql.append("WHERE 1=1");
		//条件
		sql.append(request.getParameter("SYN_START_TIME"),"AND T1.SYN_TIME >= ?");
		sql.append(request.getParameter("SYN_END_TIME"),"AND T1.SYN_TIME <= ?");
		sql.append(request.getParameter("ARCHIVES_TYPE"),"AND T1.ARCHIVES_TYPE = ?");
		sql.append(request.getParameter("CLIENT_CODE"),"AND T1.CLIENT_CODE = ?");
		sql.append(request.getParameter("ORG_CODE"),"AND T1.ORG_CODE = ?");
		sql.append(request.getParameter("BRAND_CODE"),"AND T1.BRAND_CODE = ?");
		sql.append(request.getParameter("ORDER_SERV_TYPE_CODE"),"AND T1.ORDER_SERV_TYPE_CODE = ?");
		sql.append(request.getParameter("REVISIT_STATE"),"AND T1.REVISIT_STATE = ?");
		sql.append(request.getParameter("CONVERSION_TYPE"),"AND T1.CONVERSION_TYPE = ?");
		sql.append(request.getParameter("SERVICE_ORDER_NO"),"AND T1.SERVICE_ORDER_NO = ?");
		sql.append(request.getParameter("CARD_TYPE"),"AND T2.CARD_TYPE = ?");
		sql.append(request.getParameter("PUBLISH_START_TIME"),"AND T2.PUBLISH_TIME >= ?");
		sql.append(request.getParameter("PUBLISH_END_TIME"),"AND T2.PUBLISH_TIME <= ?");
		sql.append(request.getParameter("CUSTOMER_MOBILEPHONE1"),"AND (T1.CUSTOMER_MOBILEPHONE1 = ? OR T1.CUSTOMER_MOBILEPHONE2 = ? OR T1.CUSTOMER_MOBILEPHONE3 = ?)");
		sql.append(request.getParameter("USER_ACC"),"AND T1.RECEIVE_ACC = ?");
		sql.append("ORDER BY T1.SYN_TIME DESC");
		EasyQuery query = getQuery();
		query.setMaxRow(5000);
		List<JSONObject> data = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		// 组装表头
		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		List<String> headers = new ArrayList<String>();
		headers.add(" 序号 ");
		headers.add(" 申请人 ");
		headers.add(" 状态 ");
		headers.add(" 工单结果");
		headers.add(" 卡券类型");
		headers.add(" 发布时间");
		headers.add(" 用户姓名");
		headers.add(" 区号");
		headers.add(" 区域");
		headers.add(" 用户号码1");
		headers.add(" 用户号码2");
		headers.add(" 用户号码3");
		headers.add(" 用户地址");
		headers.add(" 折算类型");
		headers.add(" 档案类型");
		headers.add(" 产品主体");
		headers.add(" 产品品牌");
		headers.add(" 产品品类");
		headers.add(" 产品型号");
		headers.add(" 服务单号");
		headers.add(" 服务请求类型");
		headers.add(" 服务请求大类");
		headers.add(" 服务请求小类");
		headers.add(" 网点");
		List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
		for (String header : headers) {
			ExcelHeaderStyle style = new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			styles.add(style);
		}
		List<List<String>> excelData = new ArrayList<List<String>>();
		int i = 0;
		List<String> list = new ArrayList<String>();
		String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
		Map<String, Object> revisitState = DictCache.getMapAllDictListByGroupCode(depCode, "PEAKEND_REVISIT_STATE");// 回访状态
		Map<String, Object> cardType = DictCache.getMapAllDictListByGroupCode(depCode, "PEAKEND_CARD_TYPE");//卡券类型
		CommMap commMap=new CommMap();
		Map ORG_CODE = commMap.getMapSysCode("ORG_CODE","");//主体
		Map WOM_ARCHIVE_TYPE = commMap.getMapSysCode("WOM_ARCHIVE_TYPE","");//档案类型
		Map Convert = commMap.getConvert();//档案类型


		for (JSONObject obj : data) {
			list = new ArrayList<String>();
			list.add(String.valueOf(++i));
			list.add(obj.getString("RECEIVE_ACC"));
			list.add(revisitState.get(obj.getString("REVISIT_STATE")) == null ? "" : revisitState.get(obj.getString("REVISIT_STATE")).toString());
			list.add(obj.getString("ORDER_RESULT"));
			list.add(cardType.get(obj.getString("CARD_TYPE")) == null ? "" : cardType.get(obj.getString("CARD_TYPE")).toString());
			list.add(obj.getString("PUBLISH_TIME"));
			list.add(obj.getString("CUSTOMER_NAME"));
			list.add(obj.getString("AREA_NUM"));
			list.add(obj.getString("AREA_NAME"));
			list.add(obj.getString("CUSTOMER_MOBILEPHONE1"));
			list.add(obj.getString("CUSTOMER_MOBILEPHONE2"));
			list.add(obj.getString("CUSTOMER_MOBILEPHONE3"));
			list.add(obj.getString("CUSTOMER_ADDRESS"));
			list.add(obj.getString("CONVERSION_TYPE"));
			list.add(WOM_ARCHIVE_TYPE.get(obj.getString("ARCHIVES_TYPE")) == null ? "" :WOM_ARCHIVE_TYPE.get(obj.getString("ARCHIVES_TYPE")).toString());
			list.add(ORG_CODE.get(obj.getString("ORG_CODE")) == null ? "" :ORG_CODE.get(obj.getString("ORG_CODE")).toString());
			list.add(obj.getString("BRAND_NAME"));
			list.add(obj.getString("PROD_NAME"));
			list.add(obj.getString("PRODUCT_CODE"));
			list.add(obj.getString("SERVICE_ORDER_NO"));
			list.add(obj.getString("ORDER_SERV_TYPE_NAME"));
			list.add(obj.getString("ORDER_SER_ITEM1_NAME"));
			list.add(obj.getString("ORDER_SER_ITEM2_NAME"));
			list.add(obj.getString("UNIT_NAME"));
			excelData.add(list);
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file, "回访处理列表.xlsx");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error: " + e.getMessage(),e);
		}
	}
	
	
	/**
	 * 验证是否为事后补偿
	 * @return
	 */
	public JSONObject actionForCheckCompensation(){
		JSONObject data = getJSONObject();
		EasySQL sql = new EasySQL("SELECT COMPENSATE_TYPE FROM C_NO_SCRIPT_CONFIG WHERE 1=1");
		sql.append(data.getString("brandCode"),"AND BRAND_CODE = ?",false);
		sql.append(data.getString("prodCode"),"AND PROD_CODE = ?",false);
		sql.append(data.getString("serviceItem2Code"),"AND SERVICE_ITEM2_CODE = ?",false);
		try {
			String str = getQuery().queryForString(sql.getSQL(), sql.getParams());
			if(str!=null&&"2".equals(str)) {
				return EasyResult.ok();
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error:" + e.getMessage(),e);
		}
		return EasyResult.error();
	}
	/**
	 * 发布-指派-回收时查询回访单数量
	 * @return
	 */
	public JSONObject actionForGetOrderCount() {
		JSONObject data = getJSONObject();
		EasySQL sql = new EasySQL("SELECT COUNT(1) AS NUM FROM C_NO_PEAK_END_ORDER T1");
		
		//判断是否为回收
		if("recycle".equals(data.getString("HANDLE_TYPE"))) {
			
			//区域或卡券不为空，需要关联发布表
			if(StringUtils.isNotBlank(data.getString("REGION"))||StringUtils.isNotBlank(data.getString("CARD_TYPE"))) {
				sql.append("LEFT JOIN C_NO_PEAK_END_PUBLISH T2 ON T1.C_NO_PUBLISH_ID = T2.C_NO_PUBLISH_ID ");
			}
			sql.append("WHERE 1=1");
			if("".equals(data.getString("REVISIT_STATE"))) {
				String status = "'2','3'";
				sql.append("AND T1.REVISIT_STATE IN ( " + status + " )");
			}else {
				sql.append("WHERE 1=1");
				sql.append(data.getString("REVISIT_STATE"),"AND T1.REVISIT_STATE = ?");
			}
			if(!"".equals(data.getString("USER_ACC"))) {
				String[] userAcc = data.getString("USER_ACC").split(",");
				sql.append("AND T1.RECEIVE_ACC IN (" + StringUtil.joinSqlStr(userAcc) + ")");
			}
			sql.append(data.getString("CARD_TYPE"),"AND T2.CARD_TYPE = ?");//卡券类型
			sql.append(data.getString("REGION"),"AND T2.REGION = ?");//区域
			sql.append(data.getString("CONVERSION_TYPE"),"AND T1.CONVERSION_TYPE = ?");//折算类型
		}else {
			sql.append("WHERE 1=1");
			sql.append(Constants.NO_PUBLISH,"AND T1.REVISIT_STATE = ?");//未发布状态
		}
		
		sql.append(data.getString("ORG_CODE"),"AND T1.ORG_CODE = ?");//主体
		sql.append(data.getString("DATA_SOURCES"),"AND T1.DATA_SOURCES = ?");//数据来源
		sql.append(data.getString("ARCHIVE_TYPE"),"AND T1.ARCHIVES_TYPE = ?");//档案类型
		sql.append(data.getString("PROD_CODE"),"AND T1.PROD_CODE = ?");//品类
		sql.append(data.getString("BRAND_CODE"),"AND T1.BRAND_CODE = ?");//品牌
		sql.append(data.getString("CLIENT_CODE"),"AND T1.CLIENT_CODE = ?");//委托方
		sql.append(data.getString("CONTACT_ORDER_SERVICE_TYPECODE"),"AND T1.ORDER_SERV_TYPE_CODE = ?");//服务请求
		sql.append(data.getString("CONTACT_ORDER_SERVICE_CODE2"),"AND T1.ORDER_SER_ITEM1_CODE = ?");//服务请求2
		if("none".equals(data.getString("RETURN_REPLACE_TYPE"))) {
			sql.append("AND T1.RETURN_REPLACE_TYPE = IS NULL");
		}else if("all".equals(data.getString("RETURN_REPLACE_TYPE"))){
			sql.append("AND T1.RETURN_REPLACE_TYPE IS NOT NULL");
		}else {
			sql.append(data.getString("RETURN_REPLACE_TYPE"),"AND T1.RETURN_REPLACE_TYPE = ?");
		}
		sql.append(data.getString("SYN_START_TIME"),"AND T1.SYN_TIME >= ?",false);//同步开始时间
		sql.append(data.getString("SYN_END_TIME"),"AND T1.SYN_TIME <= ?",false);//同步结束时间
		
		
		System.out.println(sql.getSQL());
		JSONObject obj = new JSONObject();
		try {
			obj = getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (SQLException e) {
			logger.error("查询数量出错:" + e.getMessage(),e);
		}
		return EasyResult.ok(obj);
	}
	/**
	 * 判断是否满足清洗券发放条件
	 * @return
	 */
	private JSONObject checkWashCard(JSONObject json) {
		JSONObject result = new JSONObject();
		boolean flag = false;
		JSONArray areaCode = new JSONArray();
		EasySQL sql = new EasySQL("select NAME from C_CF_COMMON_TREE where 1=1");
		sql.append(ConfigUtil.getString("COMMON_TREE_WASHCODE_PID"),"and p_id = ?");
		sql.appendLike(json.getString("PROD_NAME"),"and name like ?");
		
		try {
			List<EasyRow> list = getQuery().queryForList(sql.getSQL(), sql.getParams());
			//查出数据表示该品类有区域可以发送清洗券
			if(list!=null&&list.size()>0) {
				flag = true;
				for(EasyRow r:list) {
					String[] code = r.getColumnValue("NAME").split("-");
					areaCode.add(code[1]);
				}
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error：" + e.getMessage(),e);
		}
		result.put("flag", flag);
		result.put("code", areaCode);
		return result;
	}
	
	public JSONObject actionForTest() {
		try {
			DistributeOrderService.getInstance().invoke();
		} catch (ServiceException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}
	
}
