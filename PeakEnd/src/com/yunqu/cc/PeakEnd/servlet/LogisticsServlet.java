package com.yunqu.cc.PeakEnd.servlet;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.PeakEnd.base.AppBaseServlet;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.utils.OrderExcelUtil;
@WebServlet("/servlet/logistics")
@MultipartConfig
public class LogisticsServlet extends AppBaseServlet{
	
	private static final long serialVersionUID = 1L;
	private Logger logger = CommLogger.logger;
	
	/**
	 * 物流导入
	 * @return
	 */
	public JSONObject actionForLogisticsDataImport(){
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			Part part = getFile("file");
			OrderExcelUtil util = new OrderExcelUtil();
			try {
				util.process(part.getInputStream());
			} catch (Exception e) {
				e.printStackTrace();
			}
			List<List<String>> list = new ArrayList<>();
			list.addAll(util.getList());
			boolean scuuess = true;// 是否正确
			String msg = "";
			for (int i = 1; i < list.size(); i++) {
				Map<Object, Object> m = new HashMap<Object, Object>();
				String val = "";
				for (int j = 0; j < list.get(i).size(); j++) {
					val = list.get(i).get(j);
					switch (j) {
					case 0:
						m.put("MIDEA_ORDER_NO", val);
						break;
					case 1:
						m.put("PRODUCT_NAME", val);
						break;
					case 2:
						m.put("PRODUCT_NUM", val);
						break;
					case 3:
						m.put("POST_NAME", val);
						break;
					case 4:
						m.put("POST_TEL", val);
						break;
					case 5:
						m.put("POST_ADDRESS", val);
						break;
					case 6:
						m.put("REMARK", val);
						break;
					case 7:
						m.put("EXPRESS_DELIVERY", val);
						break;
					case 8:
						m.put("DELIVERY_NO", val);
						break;
					default:
					break;
					}
				}
				String str = JSONObject.toJSON(m).toString();
				JSONObject jsonObject = JSONObject.parseObject(str);
				jsonObject.put("CREATE_ACC", UserUtil.getUser(this.getRequest()).getUserAcc());//导入人
				jsonObject.put("ID", RandomKit.randomStr());//id
				jsonObject.put("CREATE_TIME",EasyDate.getCurrentDateString());//同步时间
				EasyRecord record = new EasyRecord("C_NO_LOGISTICS_INFORMATION", "ID").setColumns(jsonObject);
				record.set("ID", RandomKit.randomStr());//id
				query.save(record);
				
			}
			if(list.size()==0|list.size()==1){
				scuuess=false;
				msg="无导入信息";
			}
			if (!scuuess) {// 出错了不记录
				query.roolback();
				return EasyResult.error(500, "" + msg);
			} else {
				query.commit();
				return EasyResult.ok("", "物流信息导入成功！");
			}
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				logger.error("[Exception] 导入失败，原因：",e);
			}
			logger.error("[Exception] 导入失败，原因：",e);
			return EasyResult.error(501, "导入失败，原因：" + e.getMessage());
		}
	}
	
	public void actionForDownload() {
		File file = new File(this.getRequest().getServletContext().getRealPath("/pages/template/logistics-info-template.xlsx"));
		renderFile(file, "物流信息导入模板.xlsx");
	}
	
	public EasyResult actionForLogisticsDelete() {
		JSONObject json = new JSONObject();
		JSONObject obj = this.getJSONObject();
		JSONArray ids = obj.getJSONArray("ids");
		try {
			EasyRecord record = null;
			for (int i = 0; i < ids.size(); i++) {
				json.put("id", ids.getString(i));
				record = new EasyRecord("C_NO_LOGISTICS_INFORMATION", "ID").setColumns(json);
				this.getQuery().deleteById(record);
			}
			return EasyResult.ok("删除成功！");
		} catch (Exception e) {
			this.error("删除失败，原因：" + e.getMessage(), e);
			return EasyResult.error(500, "删除失败，原因：" + e.getMessage());
		}
	}
}
