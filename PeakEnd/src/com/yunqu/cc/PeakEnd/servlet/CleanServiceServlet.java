package com.yunqu.cc.PeakEnd.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.PeakEnd.base.AppBaseServlet;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.utils.StringUtil;

@SuppressWarnings("serial")
@MultipartConfig
@WebServlet("/servlet/cleanService")
public class CleanServiceServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	private static Logger logger = CommLogger.logger;

	/**
	 * @Description: 营销单详情 - 发送优惠券 sendCopon - op
	 * @return String
	 */
	public String actionForSendCopon() {
		String userPhone = getRequest().getParameter("userPhone");
		String skuCode = getRequest().getParameter("skuCode");
		String totalPrice = getRequest().getParameter("totalPrice");
		String skuCnt = getRequest().getParameter("skuCnt");
		this.setAttr("skuCode", skuCode);
		this.setAttr("totalPrice", totalPrice);
		this.setAttr("skuCnt", skuCnt);
		this.setAttr("USER_PHONE", userPhone);
		this.setAttr("mid", getRequest().getParameter("mid"));
		return "/pages/marketing/send-coupon.jsp";
	}
	/**
	 * @Description: 营销资料查询 - 处理 - 发送优惠劵信息
	 * @return JSONObject
	 *///status
	
	public JSONObject actionForSendMessage() {
		JSONObject entInfo = this.getJSONObject();
		JSONObject obj = this.getJSONObject();
		JSONObject result = new JSONObject();
		JSONObject params = new JSONObject();
		String marketid = entInfo.getString("MARKET_ID");//营销单号
		Boolean save=false;//是否需要保存单据
		if("".equals(marketid)){//接入单进入
			marketid=RandomKit.randomStr();
			save=true;
		}
		JSONObject getCardInfo = GetCardInfo(entInfo,this.getRequest());
		if("000".equals(getCardInfo.get("respCode"))){
			String cardCode=getCardInfo.getJSONObject("respDate").getString("cardCode");//返回优惠劵编号
			String takeStartTime=getCardInfo.getJSONObject("respDate").getString("takeStartTime");//返回优惠劵编号
			String takeEndTime=getCardInfo.getJSONObject("respDate").getString("takeEndTime");//返回优惠劵编号
			String content=entInfo.getString("msg.content");
			content=content.replace ("[CARD_CODE]",cardCode);
			content=content.replace ("[START_TIME]",takeStartTime);
			content=content.replace ("[END_TIME]",takeEndTime);
			String id=IDGenerator.getDefaultNUMID();//短信编号
			params.put("command",ServiceCommand.SENDMESSAGE);
			params.put("sender", Constants.APP_NAME);
			params.put("serialId", id);
			params.put("password", "YQ_85521717");
			params.put("source", "05");
			params.put("busiId", IDGenerator.getIDByCurrentTime(20));
			params.put("sendTime", EasyCalendar.newInstance().getDateTime("-")); 
			params.put("model", "2"); //事业部标识
			params.put("category", "0"); ///品牌标识
			params.put("receivers", StringUtil.spiltByRegex( entInfo.getString("msg.receiver"),";",content));
			params.put("userAcc", UserUtil.getUser(this.getRequest()).getUserAcc());
				logger.info("发送短信"+obj.getString("msg.receiver")+";"+content+";");
			try {
				logger.info("--"+JSON.toJSONString(params));
				IService service = ServiceContext.getService(ServiceID.SMSGW_INTEFACE);
				result = service.invoke(params);
			} catch (ServiceException e) {
				this.error("actionForSendMessage请求失败,",e);
			}
			//result.put("respCode", "000");
			String respCode = result.getString("respCode");
			if(GWConstants.RET_CODE_SUCCESS.equals(respCode)){
				
			}else{
				return EasyResult.error(500,"短信发送失败");
			}
		}else{
			return EasyResult.error(500,"获取优惠劵失败");
		}
		return result;
	}
	//获取优惠劵
		public static  JSONObject GetCardInfo(JSONObject json,HttpServletRequest request){
			JSONObject result = new JSONObject();
			JSONObject obj = new JSONObject();
			obj.put("command",ServiceCommand.MIXGW_WEIXINCS_GETCARD);
			obj.put("cardId", json.get("msg.card_Id"));
			//obj.put("expireDay", json.get("msg.expireDay"));
			obj.put("agentCode",  UserUtil.getUser(request).getUserAcc());
			try {
				IService service = ServiceContext.getService(ServiceID.MIXGW_WEIXINCS_INTEFACE);
				result = service.invoke(obj);
			} catch (ServiceException e) {
				logger.error("IService - 洗悦家 - 下单接口,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
			}
			return result;
		}
}
