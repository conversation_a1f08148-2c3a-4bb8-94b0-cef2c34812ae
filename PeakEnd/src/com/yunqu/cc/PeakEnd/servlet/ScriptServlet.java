package com.yunqu.cc.PeakEnd.servlet;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.PeakEnd.base.AppBaseServlet;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.css.CommMap;
import com.yunqu.cc.PeakEnd.utils.OrderExcelUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;
import java.io.File;
import java.sql.SQLException;
import java.util.*;

@WebServlet("/servlet/script")
@MultipartConfig
public class ScriptServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;

    private Logger logger = CommLogger.logger;

    public EasyResult actionForScriptSave() {
        JSONObject entInfo = this.getJSONObject("Script");
        try {
            long startTime = System.currentTimeMillis();
            EasyRecord record = new EasyRecord("C_NO_SCRIPT_CONFIG", "ID").setColumns(entInfo);
            if ("".equals(entInfo.getString("ID"))) {
                record.set("ID", RandomKit.randomStr());
                record.set("CREATE_TIME", EasyDate.getCurrentDateString());
                record.set("CREATE_ACC", UserUtil.getUser(this.getRequest()).getUserAcc());
                this.getQuery().save(record);
            } else {
                record.set("ID", entInfo.getString("ID"));
                record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
                record.set("UPDATE_ACC", UserUtil.getUser(this.getRequest()).getUserAcc());
                this.getQuery().update(record);
            }
            logger.info("操作数据库耗时：" + (System.currentTimeMillis() - startTime) + "ms");
            return EasyResult.ok("", "操作成功！");
        } catch (Exception e) {
            this.error("操作失败，原因：" + e.getMessage(), e);
            return EasyResult.error(500, "操作失败，原因：" + e.getMessage());
        }
    }

    /**
     * 脚本导入
     * 
     * @return
     */
    public JSONObject actionForScriptDataImport() {
        EasyQuery query = this.getQuery();
        try {
            query.begin();
            Part part = getFile("file");
            OrderExcelUtil util = new OrderExcelUtil();
            try {
                util.process(part.getInputStream());
            } catch (Exception e) {
                e.printStackTrace();
            }
            List<List<String>> list = new ArrayList<>();
            list.addAll(util.getList());
            boolean scuuess = true;// 是否正确
            String msg = "";

            // List<EasyRow> data = query.queryForList("select ID,PROD_NAME FROM
            // C_AS_SCRIPT", null);
            // HashMap<String, String> map = new HashMap<String,String>();
            // for (int i = 0; i < data.size(); i++) {
            // EasyRow row = data.get(i);
            // map.put(row.getColumnValue("PROD_NAME"), row.getColumnValue("ID"));
            // }
            String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
            Map<String, Object> compensateTypeDict = DictCache.getNCMapByGroupCode(depCode, "PEAKEND_COMPENSATE_TYPE");// 回访状态
            Map<String, Object> peakEndResponsible = DictCache.getNCMapByGroupCode(depCode, "PEAK_END_RESPONSIBLE");//
            CommMap commMap = new CommMap();
            Map prodDict = commMap.getMapProductCode();// 品类
            Map brandDict = commMap.getNCBrandlist();// 品牌
            JSONArray serviceRequire1LevelDict = commMap.getServiceRequire("1", "");// 服务请求类型
            Map<String, JSONArray> serviceRequire1Leve2Dict = new HashMap<String, JSONArray>();// 一级服务请求
            Map<String, JSONArray> serviceRequire1Leve3Dict = new HashMap<String, JSONArray>();// 二级服务请求
            Map orgCode = commMap.getMapNCSysCode("ORG_CODE", "");// 主体

            for (int i = 1; i < list.size(); i++) {
                JSONObject js = new JSONObject();
                Map<Object, Object> m = new HashMap<Object, Object>();
                boolean lineScuuess = true;// 判断行内容
                StringBuffer lineMsg = new StringBuffer();
                String val = "";
                String serviceTypeCode1 = null;
                String serviceTypeCode2 = null;
                String serviceTypeCode3 = null;
                for (int j = 0; j < list.get(i).size(); j++) {
                    List<String> list2 = list.get(i);
                    val = list.get(i).get(j);
                    switch (j) {
                        case 0:
                            if ("".equals(val)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("主体名称不为空");
                                break;
                            }
                            if (StringUtils.isBlank(orgCode.get(val).toString())) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("系统不存在主体名称：").append(val);
                                break;
                            }
                            m.put("ORG_CODE", orgCode.get(val));
                            break;
                        case 1:
                            if ("".equals(val)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("品类编码不为空");
                                break;
                            }
                            m.put("PROD_CODE", val);
                            break;
                        case 2:
                            if ("".equals(val)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("品类名称不为空");
                                break;
                            }
                            m.put("PROD_NAME", val);
                            break;
                        case 3:
                            if ("".equals(val)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("品牌编码不为空");
                                break;
                            }
                            m.put("BRAND_CODE", val);
                            break;
                        case 4:
                            if ("".equals(val)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("品牌名称不为空");
                                break;
                            }
                            m.put("BRAND_NAME", val);
                            break;
                        case 5:
                            if ("".equals(val)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("服务请求类型不为空");
                                break;
                            }
                            for (int k = 0; k < serviceRequire1LevelDict.size(); k++) {
                                JSONObject jsonObject = serviceRequire1LevelDict.getJSONObject(k);
                                String name = jsonObject.getString("name");
                                if (name.equals(val)) {
                                    serviceTypeCode1 = jsonObject.getString("id");
                                    break;
                                }
                            }
                            if (StringUtils.isBlank(serviceTypeCode1)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("系统不存在服务请求名称：").append(val);
                                break;
                            }
                            m.put("SERVICE_TYPE_NAME", val);
                            m.put("SERVICE_TYPE_CODE", serviceTypeCode1);
                            break;
                        case 6:
                            if ("".equals(val)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("一级服务请求名称不为空");
                                break;
                            }
                            JSONArray serviceRequire1Leve2Array = serviceRequire1Leve2Dict.get(serviceTypeCode1);
                            if (serviceRequire1Leve2Array == null) {
                                serviceRequire1Leve2Array = commMap.getServiceRequire("2", serviceTypeCode1);// 品类
                                serviceRequire1Leve2Dict.put(serviceTypeCode1, serviceRequire1Leve2Array);
                            }

                            if (serviceRequire1Leve2Array != null) {
                                for (int k = 0; k < serviceRequire1Leve2Array.size(); k++) {
                                    JSONObject jsonObject = serviceRequire1Leve2Array.getJSONObject(k);
                                    String name = jsonObject.getString("name");
                                    if (name.equals(val)) {
                                        serviceTypeCode2 = jsonObject.getString("id");
                                        break;
                                    }
                                }
                            }

                            if (StringUtils.isBlank(serviceTypeCode2)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("系统不存在一级服务请求名称：").append(val);
                                break;
                            }
                            m.put("SERVICE_ITEM1_NAME", val);
                            m.put("SERVICE_ITEM1_CODE", serviceTypeCode2);
                            break;
                        case 7:
                            if ("".equals(val)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("二级服务请求名称不为空");
                                break;
                            }

                            JSONArray serviceRequire1Leve3Array = serviceRequire1Leve3Dict
                                    .get(serviceTypeCode1 + serviceTypeCode2);
                            if (serviceRequire1Leve3Array == null) {
                                serviceRequire1Leve3Array = commMap.getServiceRequire("3", serviceTypeCode2);// 品类
                                serviceRequire1Leve3Dict.put(serviceTypeCode1 + serviceTypeCode2,
                                        serviceRequire1Leve3Array);
                            }

                            if (serviceRequire1Leve3Array != null) {
                                for (int k = 0; k < serviceRequire1Leve3Array.size(); k++) {
                                    JSONObject jsonObject = serviceRequire1Leve3Array.getJSONObject(k);
                                    String name = jsonObject.getString("name");
                                    if (name.equals(val)) {
                                        serviceTypeCode3 = jsonObject.getString("id");
                                        break;
                                    }
                                }
                            }

                            if (StringUtils.isBlank(serviceTypeCode3)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("系统不存在二级服务请求名称：").append(val);
                                break;
                            }
                            m.put("SERVICE_ITEM2_NAME", val);
                            m.put("SERVICE_ITEM2_CODE", serviceTypeCode3);
                            break;
                        case 8:
                            if ("".equals(val)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("补偿类型不为空");
                                break;
                            }
                            if ("null".equals(String.valueOf(compensateTypeDict.get(val)))) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("补偿类型不正确");
                                break;
                            }

                            m.put("COMPENSATE_TYPE", compensateTypeDict.get(val));
                            break;
                        case 9:
                            m.put("MIN_BY_COMPENSATE", val);
                            break;
                        case 10:
                            m.put("MAX_BY_COMPENSATE", val);
                            break;
                        case 11:
                            m.put("MIN_TS_COMPENSATE", val);
                            break;
                        case 12:
                            m.put("MAX_TS_COMPENSATE", val);
                            break;
                        case 13:
                            m.put("MIN_YYBG_COMPENSATE", val);
                            break;
                        case 14:
                            m.put("MAX_YYBG_COMPENSATE", val);
                            break;
                        case 15:
                            if ("".equals(val)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("脚本内容不为空");
                                break;
                            }
                            m.put("GUIDED_SPEECH", val);
                            break;
                        case 16:
                            if ("".equals(val)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("重复提醒内容不为空");
                                break;
                            }
                            m.put("REPEAT_CONTENT", val);
                            break;
                        case 17:
                            m.put("REMARKS", val);
                            break;
                        case 18:
                            if ("null".equals(String.valueOf(peakEndResponsible.get(val)))) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("系统不存在责任方名称：").append(val);
                                break;
                            }
                            m.put("RESPONSIBLE", peakEndResponsible.get(val));//
                            break;
                        case 19:
                            if ("".equals(val) || !"N".equals(val)) {
                                val = "Y";
                            }
                            m.put("ALLOW_CASH", val);
                            break;
                        case 20:
                            if ("".equals(val) || !"Y".equals(val)) {
                                val = "N";
                            }
                            m.put("ALLOW_ALL_CASH", val);
                            break;
                        case 21:// 是否允许黄金会员
                            if ("".equals(val) || !"Y".equals(val)) {
                                val = "N";
                            }
                            m.put("IS_GOLD_MEMBER", val);
                            break;
                        case 22:// 是否允许实物补偿
                            if ("".equals(val) || !"Y".equals(val)) {
                                val = "N";
                            }
                            m.put("IS_PHY_COMP", val);
                            break;
                        case 23:// 是否允许企业微信
                            if ("".equals(val) || !"Y".equals(val)) {
                                val = "N";
                            }
                            m.put("IS_WX_ENTER", val);
                            break;
                        case 24:// 是否允许延保卡
                            if ("".equals(val) || !"Y".equals(val)) {
                                val = "N";
                            }
                            m.put("IS_EXT_WARRANTY", val);
                            break;
                        case 25:
                            m.put("SMS_CONTENT", val);
                            break;
                        case 26: // 场景类型
                            if ("".equals(val)) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("场景类型不为空");
                                break;
                            }
                            // 获取场景类型字典
                            Map<String, Object> sceneTypeDict = DictCache.getNCMapByGroupCode(depCode,
                                    "SCENE_TYPE_VALUE");
                            if ("null".equals(String.valueOf(sceneTypeDict.get(val)))) {
                                scuuess = false;
                                lineScuuess = false;
                                lineMsg.append("场景类型不正确");
                                break;
                            }
                            m.put("SCENE_TYPE", sceneTypeDict.get(val));
                            break;
                        case 27: // 现金补偿脚本
                            m.put("CASH_GUIDED_SPEECH", val);
                            break;
                        case 28: // 实物补偿脚本
                            m.put("GIFT_GUIDED_SPEECH", val);
                            break;
                        default:
                            break;
                    }
                }
                logger.info(">>>" + m);
                if (scuuess) {
                    String str = JSONObject.toJSON(m).toString();
                    JSONObject jsonObject = JSONObject.parseObject(str);
                    jsonObject.put("CREATE_ACC", UserUtil.getUser(this.getRequest()).getUserAcc());// 导入人
                    jsonObject.put("ID", RandomKit.randomStr());// id
                    jsonObject.put("STATUS", "Y");// 启用
                    jsonObject.put("CREATE_TIME", EasyDate.getCurrentDateString());// 同步时间
                    EasyRecord record = new EasyRecord("C_NO_SCRIPT_CONFIG", "ID").setColumns(jsonObject);
                    // if(map.containsKey(jsonObject.getString("PROD_NAME"))) {
                    // record.set("ID", map.get(jsonObject.getString("PROD_NAME")));//id
                    // query.update(record);
                    // }else {
                    // record.set("ID", RandomKit.randomStr());//id
                    // query.save(record);
                    // }
                    query.save(record);
                } else {
                    if (!lineScuuess) {
                        msg = msg + "第" + i + "行出错：" + lineMsg + "<br>";
                    }
                }
            }
            if (list.size() == 0 | list.size() == 1) {
                scuuess = false;
                msg = "无导入信息";
            }
            if (!scuuess) {// 出错了不记录
                query.roolback();
                return EasyResult.error(500, "" + msg);
            } else {
                query.commit();
                return EasyResult.ok("", "脚本导入成功！");
            }
        } catch (Exception e) {
            try {
                query.roolback();
            } catch (SQLException e1) {
                logger.error("[Exception] 导入失败，原因：", e);
            }
            logger.error("[Exception] 导入失败，原因：", e);
            return EasyResult.error(501, "导入失败，原因：" + e.getMessage());
        }
    }

    /**
     * 批量删除
     * 
     * @return
     */
    public EasyResult actionForScritpDelete() {
        JSONObject json = new JSONObject();
        JSONObject obj = this.getJSONObject();
        JSONArray ids = obj.getJSONArray("ids");
        try {
            EasyRecord record = null;
            for (int i = 0; i < ids.size(); i++) {
                json.put("id", ids.getString(i));
                record = new EasyRecord("C_NO_SCRIPT_CONFIG", "ID").setColumns(json);
                this.getQuery().deleteById(record);
            }
            return EasyResult.ok("删除成功！");
        } catch (Exception e) {
            this.error("删除失败，原因：" + e.getMessage(), e);
            return EasyResult.error(500, "删除失败，原因：" + e.getMessage());
        }
    }

    public void actionForDownload() {
        File file = new File(
                this.getRequest().getServletContext().getRealPath("/pages/template/script-config-template.xlsx"));
        renderFile(file, "话术脚本导入模板.xlsx");
    }

    /**
     * 导出结果
     * 
     * @throws SQLException
     */
    public void actionForExportScriptConfig() throws SQLException {
        HttpServletRequest request = this.getRequest();
        JSONObject param = requestToJsonObject(request);
        EasySQL sql = new EasySQL();
        sql.append("select * from C_NO_SCRIPT_CONFIG WHERE 1=1 ");
        sql.append(param.getString("brandCode"), " and BRAND_CODE=? ");
        sql.append(param.getString("prodCode"), " and PROD_CODE=? ");
        sql.append(param.getString("compensateType"), " and COMPENSATE_TYPE=? ");
        sql.append(param.getString("status"), " and STATUS=? ");
        sql.append(param.getString("createTimeStar"), " and CREATE_TIME>= ? ");
        sql.append(param.getString("createTimeEnd"), " and CREATE_TIME<=? ");
        sql.append(param.getString("createAcc"), " and CREATE_ACC=? ");
        sql.append(param.getString("serviceTypeCode"), " and SERVICE_TYPE_CODE=? ");
        sql.append(param.getString("serviceItem1Code"), " and SERVICE_ITEM1_CODE=? ");
        sql.append(param.getString("serviceItem2Code"), " and SERVICE_ITEM2_CODE=? ");
        sql.append(param.getString("ORG_CODE"), " and ORG_CODE=? ");
        sql.append(param.getString("sceneType"), " and SCENE_TYPE=? ");
        sql.append(" ORDER BY CREATE_TIME DESC");
        getQuery().setMaxRow(99999);
        List<Map<String, String>> data = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
        CommMap commMap = new CommMap();
        Map orgCode = commMap.getMapSysCode("ORG_CODE", "");// 主体
        String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
        Map<String, Object> compensateTypeDict = DictCache.getMapEnableDictListByGroupCode(depCode,
                "PEAKEND_COMPENSATE_TYPE");//
        Map<String, Object> peakEndResponsible = DictCache.getMapEnableDictListByGroupCode(depCode,
                "PEAK_END_RESPONSIBLE");//
        Map<String, Object> sceneTypeDict = DictCache.getMapEnableDictListByGroupCode(depCode, "SCENE_TYPE_VALUE");// 场景类型字典

        // 组装表头
        File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
        List<String> headers = new ArrayList<String>();
        headers.add(" 主体 ");
        headers.add(" 品类编码");
        headers.add(" 品类名称");
        headers.add(" 品牌编码");
        headers.add(" 品牌名称");
        headers.add(" 一级服务请求");
        headers.add(" 二级服务请求");
        headers.add(" 三级服务请求");
        headers.add(" 场景类型");
        headers.add(" 补偿类型");
        headers.add(" 抱怨最小补偿金额");
        headers.add(" 抱怨最大补偿金额");
        headers.add(" 投诉最小补偿金额");
        headers.add(" 投诉最大补偿金额");
        headers.add(" 扬言曝光最小补偿金额");
        headers.add(" 扬言曝光最大补偿金额");
        headers.add(" 脚本内容");
        headers.add(" 现金补偿脚本");
        headers.add(" 实物补偿脚本");
        headers.add(" 重复提醒内容");
        headers.add(" 备注");
        headers.add(" 责任方");
        headers.add(" 是否允许现金补偿（Y/N)");
        headers.add(" 是否全员现金补偿（Y/N)");
        headers.add(" 是否允许黄金会员（Y/N)");
        headers.add(" 是否允许实物补偿（Y/N)");
        headers.add(" 是否允许企业微信（Y/N)");
        headers.add(" 是否允许延保卡（Y/N)");
        headers.add(" 短信内容");
        List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
        for (String header : headers) {
            ExcelHeaderStyle style = new ExcelHeaderStyle();
            style.setData(header);
            style.setWidth(3600);
            styles.add(style);
        }
        List<List<String>> excelData = new ArrayList<List<String>>();
        int i = 0;
        List<String> list = null;
        for (Map<String, String> map : data) {
            list = new ArrayList<String>();
            list.add(orgCode.get(map.get("ORG_CODE")) == null ? "" : orgCode.get(map.get("ORG_CODE")).toString());
            list.add(map.get("PROD_CODE"));
            list.add(map.get("PROD_NAME"));
            list.add(map.get("BRAND_CODE"));
            list.add(map.get("BRAND_NAME"));
            list.add(map.get("SERVICE_TYPE_NAME"));
            list.add(map.get("SERVICE_ITEM1_NAME"));
            list.add(map.get("SERVICE_ITEM2_NAME"));
            list.add(sceneTypeDict.get(map.get("SCENE_TYPE")) == null ? ""
                    : sceneTypeDict.get(map.get("SCENE_TYPE")).toString());
            list.add(compensateTypeDict.get(map.get("COMPENSATE_TYPE")) == null ? ""
                    : compensateTypeDict.get(map.get("COMPENSATE_TYPE")).toString());
            list.add(map.get("MIN_BY_COMPENSATE"));
            list.add(map.get("MAX_BY_COMPENSATE"));
            list.add(map.get("MIN_TS_COMPENSATE"));
            list.add(map.get("MAX_TS_COMPENSATE"));
            list.add(map.get("MIN_YYBG_COMPENSATE"));
            list.add(map.get("MAX_YYBG_COMPENSATE"));
            list.add(map.get("GUIDED_SPEECH"));
            list.add(map.get("CASH_GUIDED_SPEECH"));
            list.add(map.get("GIFT_GUIDED_SPEECH"));
            list.add(map.get("REPEAT_CONTENT"));
            list.add(map.get("REMARKS"));
            list.add(peakEndResponsible.get(map.get("RESPONSIBLE")) == null ? ""
                    : peakEndResponsible.get(map.get("RESPONSIBLE")).toString());
            list.add(map.get("ALLOW_CASH"));
            list.add(map.get("ALLOW_ALL_CASH"));
            list.add(map.get("IS_GOLD_MEMBER"));
            list.add(map.get("IS_PHY_COMP"));
            list.add(map.get("IS_WX_ENTER"));
            list.add(map.get("IS_EXT_WARRANTY"));
            list.add(map.get("SMS_CONTENT"));
            excelData.add(list);
        }
        try {
            ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
            renderFile(file, "补偿脚本配置.xlsx");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public static JSONObject requestToJsonObject(HttpServletRequest request) {
        JSONObject requestJson = new JSONObject();
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String[] pv = request.getParameterValues(paramName);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < pv.length; i++) {
                if (pv[i].length() > 0) {
                    if (i > 0) {
                        sb.append(",");
                    }
                    sb.append(pv[i]);
                }
            }
            requestJson.put(paramName, sb.toString());
        }
        return requestJson;
    }
}
