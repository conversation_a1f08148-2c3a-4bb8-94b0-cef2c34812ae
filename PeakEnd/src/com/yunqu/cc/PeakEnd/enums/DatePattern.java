package com.yunqu.cc.PeakEnd.enums;

public enum DatePattern {
	PAT_DATE1("yyyy-MM-dd"),
	PAT_DATE2("yyyy/MM/dd"),
	PAT_DATE3("yyyyMMdd"),
	PAT_DATETIME1("yyyy-MM-dd HH:mm:ss"),
	PAT_DATETIME2("yyyy/MM/dd HH:mm:ss"),
	PAT_DATETIME3("yyyyMMddHHmmss"),
	PAT_DATETIMES1("yyyy-MM-dd HH:mm:ss,SSS"),
	PAT_DATETIMES2("yyyy/MM/dd HH:mm:ss,SSS"),
	PAT_DATETIMES3("yyyyMMddHHmmss,SSS"),
	PAT_TIME1("HH:mm:ss"),
	PAT_TIME2("HH:mm:ss,SSS");
	
	private DatePattern(String pattern){
		this.pattern = pattern;
	}
	
	private String pattern;

	public String getPattern() {
		return pattern;
	}

	public void setPattern(String pattern) {
		this.pattern = pattern;
	}

	
}
