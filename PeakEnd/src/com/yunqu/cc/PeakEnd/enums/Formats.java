package com.yunqu.cc.PeakEnd.enums;

public enum Formats {
	INT("int","^[-\\+]?[\\d]{1,}$"),
	TEXT("text","^(*+)$"),
	DOUBLE("double","^(-?\\d+)(\\.\\d+)?$"),
	PERCENT("percent","^(-?\\d+)\\.[\\d]{1}%$"),
	PERCENT1("percent1","^(-?\\d+)\\.[\\d]{2}%$"),
	PERCENT2("percent2","^(-?\\d+)%$");
	
	private Formats(String key,String pattern) {
		this.setKey(key);
		this.pattern = pattern;
	}
	
	public String getPattern() {
		return pattern;
	}

	public void setPattern(String pattern) {
		this.pattern = pattern;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	private String pattern;
	private String key;
	
	
	
	
}
