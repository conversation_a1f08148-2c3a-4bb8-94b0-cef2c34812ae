package com.yunqu.cc.PeakEnd.service;

import java.sql.SQLException;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;

public class OrgIntergalStatService{
	
	private Logger logger = CommLogger.logger;
	
	protected static EasyQuery getQuery()
	  {
		 return EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);
	  }
	/**
	 * 统计所有事业部有效时间内容的金额使用情况
	 */
	public void statAllOrg() {
		String now = EasyDate.getCurrentDateString();
		EasyQuery query = getQuery();
		EasySQL sql = new EasySQL("SELECT * FROM C_NO_ORG_INTEGRAL WHERE status = '1' ");
		sql.append(now," and VALID_BEGIN_TIME >= ?");
		sql.append(now," and VALID_END_TIME <= ?");
		try {
			List<EasyRow> list = query.queryForList(sql.getSQL(), sql.getParams());
			for (int i = 0; i < list.size(); i++) {
				EasyRow row = list.get(i);
				row.getColumnValue("");
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
	}

	/**
	 *根据金额配置项id统计已使用金额情况
	 * @param 金额配置编号 intergalId
	 * @return
	 */
	public JSONObject statOrgIntergalById(String intergalId) {
		EasyQuery query = getQuery();
		JSONObject data = new JSONObject();
		try {
			String now = EasyDate.getCurrentDateString();
			EasySQL sql = new EasySQL("SELECT * FROM C_NO_ORG_INTEGRAL WHERE 1=1");
			sql.append(intergalId," AND ID = ?");
			EasyRow row = query.queryForRow(sql.getSQL(), sql.getParams());
			int useAmount = 0;
			if(row!=null) {
				String orgCode = row.getColumnValue("ORG_CODE");
				String validEndTime = row.getColumnValue("VALID_END_TIME");
				String validBeginTime = row.getColumnValue("VALID_BEGIN_TIME");
				int limitAmount = Integer.valueOf(row.getColumnValue("INTEGRAL_LIMIT"));
				int thresholdAmount = Integer.valueOf(row.getColumnValue("INTEGRAL_THRESHOLD"));
				
				EasySQL statSql = new EasySQL("SELECT SUM(COMPENSATE_AMOUNT) TOTAL_AMOUNT FROM C_NO_PEAK_END_RECORD ");
				statSql.append(intergalId," WHERE ORG_INTEGRAL_ID = ?");
				String useAmountValue = query.queryForString(statSql.getSQL(), statSql.getParams());
				
				if(StringUtils.isNotBlank(useAmountValue)) {
					useAmount = Integer.valueOf(useAmountValue);
				}
				data.put("orgCode", orgCode);
				data.put("limimtAount", limitAmount);
				data.put("thresholdAmount", thresholdAmount);
				data.put("suprAmount", limitAmount-useAmount);
				data.put("useAmount", useAmount);
				data.put("validEndTime", validEndTime);
				data.put("validBeginTime", validBeginTime);
				data.put("updateTime", now);
				data.put("status", "1");//正常
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return data;
	}
	
	/**
	 *根据金额配置项统计已使用金额情况
	 * @param row 金额配置对象
	 * @return
	 * @throws Exception 
	 */
	public JSONObject statOrgIntergalByModel(EasyRow row) throws Exception {
		EasyQuery query = getQuery();
		JSONObject data = new JSONObject();
		String now = EasyDate.getCurrentDateString();

		if(row!=null) {
			String intergalId = row.getColumnValue("ID");
			String orgCode = row.getColumnValue("ORG_CODE");
			String validEndTime = row.getColumnValue("VALID_BEGIN_TIME");
			String validBeginTime = row.getColumnValue("VALID_END_TIME");
			int limitAmount = Integer.valueOf(row.getColumnValue("INTEGRAL_LIMIT"));
			int thresholdAmount = Integer.valueOf(row.getColumnValue("INTEGRAL_THRESHOLD"));
			
			EasySQL statSql = new EasySQL("SELECT SUM(COMPENSATE_AMOUNT) TOTAL_AMOUNT FROM C_NO_PEAK_END_RECORD WHERE COMPENSATE_MODE = '1' ");//只统计延保补偿记录
			statSql.append(intergalId,"AND ORG_INTEGRAL_ID = ?");
			String useAmountValue = query.queryForString(statSql.getSQL(), statSql.getParams());
			int useAmount = 0;
			if(StringUtils.isNotBlank(useAmountValue)) {
				useAmount = Integer.valueOf(useAmountValue);
			}
			data.put("orgCode", orgCode);
			data.put("limitAmount", limitAmount);
			data.put("thresholdAmount", thresholdAmount);
			data.put("suprAmount", limitAmount-useAmount);
			data.put("useAmount", useAmount);
			data.put("validEndTime", validEndTime);
			data.put("validBeginTime", validBeginTime);
			data.put("updateTime", now);
			data.put("status", "1");//正常
		}
		return data;
	}
	
	/**
	 * 根据事业部统计当前有效时间内使用的金额情况
	 * @param orgCode
	 * @return
	 */
	public JSONObject statOrgIntergalByOrg(String orgCode) {
		JSONObject data = new JSONObject();
		EasyQuery query = getQuery();
		try {
			String now = EasyDate.getCurrentDateString();
			EasySQL sql = new EasySQL("SELECT * FROM C_NO_ORG_INTEGRAL WHERE 1=1");
			sql.append(orgCode," AND ORG_CODE = ?");
			sql.append(now," AND VALID_BEGIN_TIME >= ?");
			sql.append(now," AND VALID_END_TIME <= ?");
			EasyRow row = query.queryForRow(sql.getSQL(), sql.getParams());
			if(row!=null) {
				String intergalId = row.getColumnValue("ID");
				int limitAmount = Integer.valueOf(row.getColumnValue("INTEGRAL_LIMIT"));
				int thresholdAmount = Integer.valueOf(row.getColumnValue("INTEGRAL_THRESHOLD"));
				EasySQL statSql = new EasySQL("SELECT SUM(COMPENSATE_AMOUNT) TOTAL_AMOUNT FROM C_NO_PEAK_END_RECORD ");
				statSql.append(intergalId,"  ORG_INTEGRAL_ID = ?");
				int useAmount = 0;
				String useAmountValue = query.queryForString(statSql.getSQL(), statSql.getParams());
				if(StringUtils.isNotBlank(useAmountValue)) {
					useAmount = Integer.valueOf(useAmountValue);
				}
				data.put("orgCode", orgCode);
				data.put("limitAmount", limitAmount);
				data.put("thresholdAmount", thresholdAmount);
				data.put("suprAmount", limitAmount-useAmount);
				data.put("useAmount", useAmount);
				data.put("updateTime", now);
				data.put("status", "1");//正常
			}else {
				data.put("orgCode", orgCode);
				data.put("limimtAount", 0);
				data.put("thresholdAmount", 0);
				data.put("suprAmount", 0);
				data.put("useAmount", 0);
				data.put("updateTime", now);
				data.put("status", "0");//未找到金额配置项
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		
		return data;
	}
}
