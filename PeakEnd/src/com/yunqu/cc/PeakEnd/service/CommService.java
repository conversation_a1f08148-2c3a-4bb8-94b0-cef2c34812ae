package com.yunqu.cc.PeakEnd.service;

import java.sql.SQLException;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.utils.StringUtil;

public class CommService{
	
	private Logger logger = CommLogger.logger;
	private static Logger  smsLogger = CommLogger.getCommLogger("sms");
	
	protected static EasyQuery getQuery()
	  {
		 return EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);
	  }
	/**
	 * 获取短信模板
	 * @param code 模板编码
	 * @param itemCode 模板分类编码
	 * @param getEpCode
	 * @return
	 */
	public static String getSmsContent(String code,String itemCode,String getEpCode) {
		String sql="  select * from  C_SMS_TEMPLATE where MSG_CODE=? and TYPE_ID =(select id  from  C_SMS_TEMPLATE_TYPE  where  "
				+ "code=?  and EP_CODE=? and  STATUS='1') and EP_CODE=? ";
		String content="";
		try {
			JSONObject queryForRecord = getQuery().queryForRow(sql, new Object[]{code,itemCode,getEpCode,getEpCode}, new JSONMapperImpl());
			JSONObject resultJson = new JSONObject();
			content = queryForRecord.get("CONTENT").toString();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		 return content;
	}
	/**
	 * 发送短信内容
	 * @param content
	 * @param phone
	 */
	public  static boolean sendMessage(String content,String phone,String userAcc,String source){
		JSONObject result=new JSONObject();
		String id=IDGenerator.getDefaultNUMID();//短信编号
		JSONObject params = new JSONObject();
		params.put("command",ServiceCommand.SENDMESSAGE);
		params.put("sender", Constants.APP_NAME);
		params.put("serialId", id);
		params.put("password", "YQ_85521717");
		params.put("source", source);//峰终来源
		params.put("busiId", IDGenerator.getIDByCurrentTime(20));
		params.put("sendTime", EasyCalendar.newInstance().getDateTime("-")); 
		params.put("model", Constants.getSmsModel()); //事业部标识
		params.put("category", Constants.getSmsCategory()); ///品牌标识
		params.put("receivers", StringUtil.spiltByRegex( phone,";",content));
		params.put("userAcc", userAcc);
		smsLogger.info("发送短信"+phone+";"+content+";");
		try {
			smsLogger.info(JSON.toJSONString(params));
			IService service = ServiceContext.getService(ServiceID.SMSGW_INTEFACE);
			result = service.invoke(params);
			if(result!=null){
				String respCode = result.getString("respCode");
				if (Constants.GATEWAY_SUCCESS_CODE.equals(respCode)) {// "000"
					return true;
				}
			}else{
				return false;
			}
		} catch (ServiceException e) {
			smsLogger.error("sendMessage请求失败,",e);
		}
		return false;
	}

	
}
