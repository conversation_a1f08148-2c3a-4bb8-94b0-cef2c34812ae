package com.yunqu.cc.PeakEnd.service;

import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.db.EasyQuery;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;

/**
 *缓存 金额使用情况
 * <AUTHOR>
 *
 */
public class OrgIntergalCacheManager {
	
	private static EasyCache cache = CacheManager.getMemcache();
	private static Logger cacheLogger = CommLogger.getCommLogger("cache");
	protected static EasyQuery getQuery()
	  {
		 return EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);
	  }
	
	private static String key = Constants.CACHE_KEY_ORG_INTERGAL;
	
	public static JSONObject getOrgIntergalModel(String orgCode) {
		Object object = cache.get(key+orgCode);
		if(object==null) {
			OrgIntergalStatService statService = new OrgIntergalStatService();
			JSONObject model = statService.statOrgIntergalByOrg(orgCode);
			setOrgIntergalModel(model, orgCode);
			object = cache.get(key+orgCode);
		}
		return (JSONObject)object;
	}
	
	public static void setOrgIntergalModel(JSONObject model,String orgCode) {
		cache.put(key+orgCode, model,300);//五分钟内有效
	}
}
