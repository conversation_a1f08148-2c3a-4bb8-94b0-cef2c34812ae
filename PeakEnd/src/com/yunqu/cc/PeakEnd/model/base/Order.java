package com.yunqu.cc.PeakEnd.model.base;

import com.alibaba.fastjson.annotation.JSONField;

public class Order  {
	@JSONField(name="ID")
	private String id;//编号
	@JSONField(name="CONTACT_ORDER")
	private String contactOrder;//接入单号
	@JSONField(name="ORDER_RESULT")
	private String orderResult;//工单结果
	@JSONField(name="SETTLEMENT")
	private String settlement;//是否结算
	@JSONField(name="SERVICE_ORDER_NO")
	private String serviceOrderNo;//服务单号
	@JSONField(name="CUSTOMER_CODE")
	private String customerCode;//用户编码
	@JSONField(name="CUSTOMER_NAME")
	private String customerName;//用户姓名
	@JSONField(name="AREA_NUM")
	private String areaNum;//用户区号
	@JSONField(name="AREA_CODE")
	private String areaCode;//区域编码
	@JSONField(name="AREA_NAME")
	private String areaName;//区域名称
	@JSONField(name="CUSTOMER_MOBILEPHONE1")
	private String customerMobilephone1;//用户号码1
	@JSONField(name="CUSTOMER_MOBILEPHONE2")
	private String customerMobilephone2;//用户号码2
	@JSONField(name="CUSTOMER_MOBILEPHONE3")
	private String customerMobilephone3;//用户号码3
	@JSONField(name="CUSTOMER_ADDRESS")
	private String customerAddress;//用户详细地址
	@JSONField(name="ARCHIVES_TYPE")
	private String archivesType;//档案类型
	@JSONField(name="ARCHIVES_NO")
	private String archivesNo;//档案编码
	@JSONField(name="ORG_CODE")
	private String orgCode;//主体
	@JSONField(name="BRAND_CODE")
	private String brandCode;//品牌
	@JSONField(name="BRAND_NAME")
	private String brandName;//品牌名称
	@JSONField(name="PROD_CODE")
	private String prodCode;//产品品类编码
	@JSONField(name="PROD_NAME")
	private String prodName;//产品品类
	@JSONField(name="PRODUCT_MODEL")
	private String productModel;//型号
	@JSONField(name="PRODUCT_CODE")
	private String productCode;//型号编码
	@JSONField(name="UNIT_CODE")
	private String unitCode;//服务网点编码
	@JSONField(name="UNIT_NAME")
	private String unitName;//服务网点名称
	@JSONField(name="CLIENT_CODE")
	private String clientCode;//委托方编码
	@JSONField(name="CLIENT_NAME")
	private String clientName;//委托方名称
	@JSONField(name="ORDER_SERV_TYPE_NAME")
	private String orderServTypeName;//服务请求类型名称
	@JSONField(name="ORDER_SERV_TYPE_CODE")
	private String orderServTypeCode;//服务请求类型编码
	@JSONField(name="ORDER_SER_ITEM1_NAME")
	private String orderSerItem1Name;//服务请求大类名称
	@JSONField(name="ORDER_SER_ITEM1_CODE")
	private String orderSerItem1Code;//服务请求大类编码
	@JSONField(name="ORDER_SER_ITEM2_NAME")
	private String orderSerItem2Name;//服务请求小类名称
	@JSONField(name="ORDER_SER_ITEM2_CODE")
	private String orderSerItem2Code;//服务请求小类编码
	@JSONField(name="INSTALL_UNIT_CODE")
	private String installUnitCode;//安装单位编码
	@JSONField(name="INSTALL_UNIT_NAME")
	private String installUnitName;//安装单位名称
	@JSONField(name="ON_SITE_FLAG")
	private String onSiteFlag;//是否上门
	@JSONField(name="REMARK")
	private String remark;//备注
	@JSONField(name="SERVICE_STATUS")
	private String serviceStatus;//服务单状态
	@JSONField(name="PURCHASE_BAR_CODE")
	private String purchaseBarCode;//购机条码
	@JSONField(name="PURCHASE_DATE")
	private String purchaseDate;//购机日期
	@JSONField(name="BARCODE_TYPE")
	private String barcodeType;//保内/保外
	@JSONField(name="PURCHASE_DATE1")
	private String purchaseDate1;//购买日期
	@JSONField(name="SERVICE_DATE")
	private String serviceDate;//服务日期
	@JSONField(name="STATEMENT_DATE")
	private String statementDate;//结单日期
	@JSONField(name="FILTER_ELEMENT_FLAG")
	private String filterElementFlag;//是否换滤芯
	@JSONField(name="DISTRIBUTION_FLAG")
	private String distributionFlag;//是否经销
	@JSONField(name="MAINTENANCE_TYPE")
	private String maintenanceType;//包修类型
	@JSONField(name="RETURN_REPLACE_TYPE")
	private String returnReplaceType;//退换机类型
	@JSONField(name="INTERNAL_MACHINE")
	private String internalMachine;//内机条码
	@JSONField(name="EXTENDED_WARRANTY")
	private String extendedWarranty;//有无延保档案
	@JSONField(name="COMPENSATE_tYPE")
	private String compensateType;//补偿类型
	@JSONField(name="SYN_TIME")
	private String synTime;//同步时间
	@JSONField(name="SYN_BATCH_NO")
	private String synBatchNo;//同步批次号
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getContactOrder() {
		return contactOrder;
	}
	public void setContactOrder(String contactOrder) {
		this.contactOrder = contactOrder;
	}
	public String getOrderResult() {
		return orderResult;
	}
	public void setOrderResult(String orderResult) {
		this.orderResult = orderResult;
	}
	public String getSettlement() {
		return settlement;
	}
	public void setSettlement(String settlement) {
		this.settlement = settlement;
	}
	public String getServiceOrderNo() {
		return serviceOrderNo;
	}
	public void setServiceOrderNo(String serviceOrderNo) {
		this.serviceOrderNo = serviceOrderNo;
	}
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	public String getAreaNum() {
		return areaNum;
	}
	public void setAreaNum(String areaNum) {
		this.areaNum = areaNum;
	}
	public String getAreaCode() {
		return areaCode;
	}
	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}
	public String getAreaName() {
		return areaName;
	}
	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}
	public String getCustomerMobilephone1() {
		return customerMobilephone1;
	}
	public void setCustomerMobilephone1(String customerMobilephone1) {
		this.customerMobilephone1 = customerMobilephone1;
	}
	public String getCustomerMobilephone2() {
		return customerMobilephone2;
	}
	public void setCustomerMobilephone2(String customerMobilephone2) {
		this.customerMobilephone2 = customerMobilephone2;
	}
	public String getCustomerMobilephone3() {
		return customerMobilephone3;
	}
	public void setCustomerMobilephone3(String customerMobilephone3) {
		this.customerMobilephone3 = customerMobilephone3;
	}
	public String getCustomerAddress() {
		return customerAddress;
	}
	public void setCustomerAddress(String customerAddress) {
		this.customerAddress = customerAddress;
	}
	public String getArchivesType() {
		return archivesType;
	}
	public void setArchivesType(String archivesType) {
		this.archivesType = archivesType;
	}
	public String getArchivesNo() {
		return archivesNo;
	}
	public void setArchivesNo(String archivesNo) {
		this.archivesNo = archivesNo;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getBrandCode() {
		return brandCode;
	}
	public void setBrandCode(String brandCode) {
		this.brandCode = brandCode;
	}
	public String getBrandName() {
		return brandName;
	}
	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}
	public String getProdCode() {
		return prodCode;
	}
	public void setProdCode(String prodCode) {
		this.prodCode = prodCode;
	}
	public String getProdName() {
		return prodName;
	}
	public void setProdName(String prodName) {
		this.prodName = prodName;
	}
	public String getProductModel() {
		return productModel;
	}
	public void setProductModel(String productModel) {
		this.productModel = productModel;
	}
	public String getProductCode() {
		return productCode;
	}
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	public String getUnitCode() {
		return unitCode;
	}
	public void setUnitCode(String unitCode) {
		this.unitCode = unitCode;
	}
	public String getUnitName() {
		return unitName;
	}
	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}
	public String getClientCode() {
		return clientCode;
	}
	public void setClientCode(String clientCode) {
		this.clientCode = clientCode;
	}
	public String getClientName() {
		return clientName;
	}
	public void setClientName(String clientName) {
		this.clientName = clientName;
	}
	public String getOrderServTypeName() {
		return orderServTypeName;
	}
	public void setOrderServTypeName(String orderServTypeName) {
		this.orderServTypeName = orderServTypeName;
	}
	public String getOrderServTypeCode() {
		return orderServTypeCode;
	}
	public void setOrderServTypeCode(String orderServTypeCode) {
		this.orderServTypeCode = orderServTypeCode;
	}
	public String getOrderSerItem1Name() {
		return orderSerItem1Name;
	}
	public void setOrderSerItem1Name(String orderSerItem1Name) {
		this.orderSerItem1Name = orderSerItem1Name;
	}
	public String getOrderSerItem1Code() {
		return orderSerItem1Code;
	}
	public void setOrderSerItem1Code(String orderSerItem1Code) {
		this.orderSerItem1Code = orderSerItem1Code;
	}
	public String getOrderSerItem2Name() {
		return orderSerItem2Name;
	}
	public void setOrderSerItem2Name(String orderSerItem2Name) {
		this.orderSerItem2Name = orderSerItem2Name;
	}
	public String getOrderSerItem2Code() {
		return orderSerItem2Code;
	}
	public void setOrderSerItem2Code(String orderSerItem2Code) {
		this.orderSerItem2Code = orderSerItem2Code;
	}
	public String getInstallUnitCode() {
		return installUnitCode;
	}
	public void setInstallUnitCode(String installUnitCode) {
		this.installUnitCode = installUnitCode;
	}
	public String getInstallUnitName() {
		return installUnitName;
	}
	public void setInstallUnitName(String installUnitName) {
		this.installUnitName = installUnitName;
	}
	public String getOnSiteFlag() {
		return onSiteFlag;
	}
	public void setOnSiteFlag(String onSiteFlag) {
		this.onSiteFlag = onSiteFlag;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getServiceStatus() {
		return serviceStatus;
	}
	public void setServiceStatus(String serviceStatus) {
		this.serviceStatus = serviceStatus;
	}
	public String getPurchaseBarCode() {
		return purchaseBarCode;
	}
	public void setPurchaseBarCode(String purchaseBarCode) {
		this.purchaseBarCode = purchaseBarCode;
	}
	public String getPurchaseDate() {
		return purchaseDate;
	}
	public void setPurchaseDate(String purchaseDate) {
		this.purchaseDate = purchaseDate;
	}
	public String getBarcodeType() {
		return barcodeType;
	}
	public void setBarcodeType(String barcodeType) {
		this.barcodeType = barcodeType;
	}
	public String getPurchaseDate1() {
		return purchaseDate1;
	}
	public void setPurchaseDate1(String purchaseDate1) {
		this.purchaseDate1 = purchaseDate1;
	}
	public String getServiceDate() {
		return serviceDate;
	}
	public void setServiceDate(String serviceDate) {
		this.serviceDate = serviceDate;
	}
	public String getStatementDate() {
		return statementDate;
	}
	public void setStatementDate(String statementDate) {
		this.statementDate = statementDate;
	}
	public String getFilterElementFlag() {
		return filterElementFlag;
	}
	public void setFilterElementFlag(String filterElementFlag) {
		this.filterElementFlag = filterElementFlag;
	}
	public String getDistributionFlag() {
		return distributionFlag;
	}
	public void setDistributionFlag(String distributionFlag) {
		this.distributionFlag = distributionFlag;
	}
	public String getMaintenanceType() {
		return maintenanceType;
	}
	public void setMaintenanceType(String maintenanceType) {
		this.maintenanceType = maintenanceType;
	}
	public String getReturnReplaceType() {
		return returnReplaceType;
	}
	public void setReturnReplaceType(String returnReplaceType) {
		this.returnReplaceType = returnReplaceType;
	}
	public String getInternalMachine() {
		return internalMachine;
	}
	public void setInternalMachine(String internalMachine) {
		this.internalMachine = internalMachine;
	}
	public String getExtendedWarranty() {
		return extendedWarranty;
	}
	public void setExtendedWarranty(String extendedWarranty) {
		this.extendedWarranty = extendedWarranty;
	}
	public String getCompensateType() {
		return compensateType;
	}
	public void setCompensateType(String compensateType) {
		this.compensateType = compensateType;
	}
	public String getSynTime() {
		return synTime;
	}
	public void setSynTime(String synTime) {
		this.synTime = synTime;
	}
	public String getSynBatchNo() {
		return synBatchNo;
	}
	public void setSynBatchNo(String synBatchNo) {
		this.synBatchNo = synBatchNo;
	}
	
	
	



}
