package com.yunqu.cc.PeakEnd.sql;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;

public class PeakEndSql {
	
	public static EasySQL recordListSql(JSONObject param) {
		String areaCode = param.getString("areaCode");
		String agentDept =param.getString("agentDept");
		EasySQL sql = new EasySQL("SELECT t1.*,T4.AREA_NAME AGENT_AREA_NAME,T4.DEPT_NAME AGENT_DEPT_NAME FROM C_NO_PEAK_END_RECORD t1 ");
		sql.append("LEFT JOIN C_NO_PEAK_END_ORDER t2 ON t1.ORDER_ID = t2.ID ");
//		if (StringUtils.isNotBlank(areaCode)||StringUtils.isNotBlank(agentDept)) {
			sql.append("LEFT JOIN C_YG_EMPLOYEE t4 ON t1.CREATE_ACC=T4.USER_ACC ");
//		}
		sql.append("WHERE 1=1 ");
		sql.append(param.getString("ORG_CODE")," AND t1.ORG_CODE = ? ");
		sql.append(param.getString("PHONE")," AND t1.PHONE = ? ");
		sql.append(param.getString("COMPENSATE_MODE")," AND t1.COMPENSATE_MODE = ? ");
		sql.append(param.getString("COMPENSATE_TYPE")," AND t1.COMPENSATE_TYPE = ? ");
		sql.append(param.getString("CREATE_ACC")," AND t1.CREATE_ACC = ? ");
		sql.append(param.getString("START_CREATE_TIME")," AND t1.CREATE_TIME >= ? ");
		sql.append(param.getString("END_CREATE_TIME")," AND t1.CREATE_TIME <= ? ");
		sql.append(param.getString("SUBMIT_STATES")," AND t1.SUBMIT_STATES = ? ");
		if (StringUtils.isNotBlank(areaCode)) {
			areaCode = areaCode.replace("[", "").replace("]", "").replace("\"", "'");
			sql.append(" and T4.AREACODE in (" + areaCode + ")");
		}
		if (StringUtils.isNotBlank(param.getString("agentDept"))) {
			agentDept = agentDept.replace("[", "").replace("]", "").replace("\"", "'");
			sql.append(" and T4.DEPT_CODE in (" + agentDept + ")");
		}
		
		sql.append(" ORDER BY t1.CREATE_TIME DESC");
		return sql;
	}
}
