package com.yunqu.cc.PeakEnd.sql;

import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;

/**
 * 回访单sqlO
 */
public class OrderDaoSql {
	
	public static EasySQL resultListSql(JSONObject param) {
		
		EasySQL sql = new EasySQL("SELECT t2.*,t1.VISIT_RESULT,t1.VISIT_TIME,t1.VISIT_ACC,t1.ID RESULT_ID FROM C_NO_PEAK_END_RESULT t1 LEFT JOIN C_NO_PEAK_END_ORDER t2 ON t1.ORDER_ID = t2.ID WHERE 1=1 ");
		sql.append(param.getString("ORG_CODE")," AND t2.ORG_CODE = ? ");
		sql.append(param.getString("ARCHIVES_TYPE")," AND t2.ARCHIVES_TYPE = ? ");
		sql.append(param.getString("CUSTOMER_MOBILEPHONE1")," AND t2.CUSTOMER_MOBILEPHONE1 = ? ");
		sql.append(param.getString("SERVICE_ORDER_NO")," AND t2.SERVICE_ORDER_NO = ? ");
		sql.append(param.getString("RESULT")," AND t1.VISIT_RESULT = ? ");
		sql.append(param.getString("RESULT_ACC")," AND t1.VISIT_ACC = ? ");
		sql.append(param.getString("ARCHIVES_NO")," AND t2.ARCHIVES_NO = ? ");
		sql.append(param.getString("BRAND_CODE")," AND t2.BRAND_CODE = ? ");
		sql.append(param.getString("ORDER_ORIGIN")," AND t2.ORDER_ORIGIN = ? ");
		sql.append(param.getString("START_VISIT_TIME")," AND t1.VISIT_TIME >= ? ");
		sql.append(param.getString("END_VISIT_TIME")," AND t1.VISIT_TIME <= ? ");
		sql.append(param.getString("DATA_SOURCES")," AND t2.DATA_SOURCES = ? ");
		sql.append(param.getString("CALL_ID")," AND t1.CALL_ID = ? ");
		sql.append(" ORDER BY VISIT_TIME DESC");
		return sql;
	}
}
