package com.yunqu.cc.PeakEnd.base;

import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.ParamUtil;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {
	public final static String APP_NAME = "PeakEnd";     //应用名称

	public final static String DS_NAME = "default-ds";     //默认数据源名称
	public final static String YW_DS = "yw-ds";     //工单数据源名称
	public final static String FRAME_DS = "mars-ds";     //平台数据源名称
	public final static String YCBUSI_DS = "ycbusi-ds";     //平台数据源名称

	/**
	 * 售后接口 IService 服务名
	 */
	public final static String CSSGW_COMM = "CSSGW-COMM";     //常用售后接口

	public final static String CACHE_KEY_ORG_INTERGAL = "ORG_INTERGA_";
	
	public final static String GW_PASSWORD =  ConfigUtil.getString(APP_NAME,"GW_PASSWORD","YQ_85521717") ; //工单模块调用网关调用密码
	
	public final static String EMAIL_GW_ACCOUNT =  ConfigUtil.getString(APP_NAME,"EMAIL_GW_ACCOUNT","") ; //邮件网关发送账户
	
	public final static String SEND_EMAIL_HOURS =  ConfigUtil.getString(APP_NAME,"SEND_EMAIL_HOURS","12") ; //发送邮件的间隔时间
	
	public final static String EMAIL_SOURCE = "02" ; //邮件来源
	
	public final static String GATEWAY_SUCCESS_CODE ="000";

	/**
	 * 全部发布
	 */
	public final static String PUBLISH_ALL = "0";
	/**
	 * 部分发布
	 */
	public final static String PUBLISH_SUB = "1";
	/**
	 * 指派
	 */
	public final static String PUBLISH_APPOINT = "2";
	
	/**
	 * 回访状态1-未发布，2-已发布，3-已分配，4-已回访
	 */
	public final static String NO_PUBLISH = "1";
	public final static String HAS_PUBLISHED = "2";
	public final static String HAS_RECEIVED = "3";
	public final static String HAS_REVISITED = "4";

	public final static String PEAKEND_TYPE1 = "1" ; //事中
	public final static String PEAKEND_TYPE2 = "2" ; //事后
	
	public final static String STATUS_Y = "Y" ; //事后

	/**
	 * 数据来源 0系统档案 1命令单
	 */
	public final static Integer DATA_SOURCE_0 = 0;
	public final static Integer DATA_SOURCE_1 = 1;
	
	/**
	 * 优惠券类型
	 * 0-代金券 1-折扣券 2-余额券 3-体验券
	 */
	public final static Integer CARD_TYPE_0 = 0;
	public final static Integer CARD_TYPE_1 = 1;
	public final static Integer CARD_TYPE_2 = 2;
	public final static Integer CARD_TYPE_3 = 3;
	

	/**
	 * 是否办结 Y-已办结，N-未办结
	 */
	public final static String IS_DONE = "Y";
	public final static String NOT_DONE = "N";

	public final static String COMPENSATE_MODE1 = "1" ; //实物礼品
	public final static String COMPENSATE_MODE2= "2" ; //会员
	public final static String COMPENSATE_MODE3 = "3" ; //企微
	public final static String COMPENSATE_MODE4 = "4" ; //清洗
	public final static String COMPENSATE_MODE5 = "5" ; //商城券
	public final static String COMPENSATE_MODE6 = "6" ; //延保卡
	public final static String COMPENSATE_MODE7 = "7" ; //现金
	public final static String COMPENSATE_MODE8 = "8" ; //实物


	public final static String PEAK_END_SMS = "PEAK_END";//峰终模板
	public final static String PEAK_END_CLEAN_SMS = "PEAK_END_CLEAN";//清洗券模板
	public final static String PEAK_END_WX_SMS = "PEAK_END_WX";//企微管家模板
	public final static String PEAK_END_COMMODITY_SMS = "PEAK_END_COMMODITY";//实物赠送模板
	public final static String PEAK_END_LEVEL_SMS = "PEAK_END_LEVEL";//会员赠送模板
	public final static String PEAK_END_COMMODITY_YB = "PEAK_END_YB";//延保卡赠送模板

	//下单接口的appid
	public final static String DOEXCHANGESCENE_APPID =  ConfigUtil.getString(APP_NAME,"DOEXCHANGESCENE_APPID","35ad8329a10b8eb68ff62241f679efcb");
	//下单接口的key
	public final static String DOEXCHANGESCENE_KEY =  ConfigUtil.getString(APP_NAME,"DOEXCHANGESCENE_KEY","12b0ec2aea755a4a36ffb5764211c8fa"); //
	//会员赠送的taskId
	public final static String TCHECK_GROWTH_RULE_TASKID =  ConfigUtil.getString(APP_NAME,"TCHECK_GROWTH_RULE_TASKID","101380"); //
	//实物补偿重复赠送有效期
	public final static int PEAK_END_VALIDITY_MONTH =  ConfigUtil.getInt(APP_NAME,"PEAK_END_VALIDITY_MONTH",1); //
	public final static String PEAK_END_VALIDITY_MONTH1 =  ConfigUtil.getString(APP_NAME,"PEAK_END_VALIDITY_MONTH","3"); //
	
	public final static String COMPENSATE_SERVICE_ID = "CSSGW-COMPENSATE";
	//现金补偿重复补偿提示有效期
	public final static int CASH_COMPENSATE_CHECK_DAYS =  ConfigUtil.getInt(APP_NAME,"CASH_COMPENSATE_CHECK_DAYS",30);

	/**
	 * 服务补偿发送短信所使用渠道对应的事业部和品牌 35 9
	 * @return
	 */
	public static String getSmsModel() {
		return ParamUtil.getParam(APP_NAME,"PEAK_END_SMS_MODEL", "2");
		//return ConfigUtil.getString(APP_NAME,"PEAK_END_SMS_MODEL","2");
	}
	public static String getSmsCategory() {
		return ParamUtil.getParam(APP_NAME,"PEAK_END_SMS_CATEGORY", "0");
		//return ConfigUtil.getString(APP_NAME,"PEAK_END_SMS_CATEGORY","0");
	}
}
