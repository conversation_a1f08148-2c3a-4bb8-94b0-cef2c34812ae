package com.yunqu.cc.PeakEnd.base;

import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.web.EasyBaseServlet;
import org.easitline.common.db.EasyQuery;

import java.sql.SQLException;

public abstract class AppBaseServlet extends EasyBaseServlet {

	private static final long serialVersionUID = 1L;

	@Override
	protected String getAppDatasourceName() {
		return Constants.YW_DS;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}
	
	@Override
	protected String getResId() {
		return null;
	}
	
	protected EasyCache getCache() {
		return CacheManager.getMemcache();
	}

	protected void roolback(EasyQuery query) {
		try {
			query.roolback();
		} catch (SQLException e1) {
			this.getLogger().error(e1.getMessage(),e1);
		}
	}
}
