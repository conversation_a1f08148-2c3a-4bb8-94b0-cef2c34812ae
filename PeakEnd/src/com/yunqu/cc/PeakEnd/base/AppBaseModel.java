package com.yunqu.cc.PeakEnd.base;

import org.easitline.common.db.EasyModel;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

@SuppressWarnings("serial")
public abstract class AppBaseModel extends EasyModel{

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}
	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_NAME;
	}
	public void addCreateTime(){
		this.put("CREATE_TIME",EasyDate.getCurrentDateString(null));
	}
	public void setCreator(String userId){
		this.put("CREATOR",userId );
	}
	public void addPkRandomValue(){
		this.setPrimaryValues(RandomKit.randomStr());
	}

	
}
