package com.yunqu.cc.PeakEnd.base;

import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.dao.DaoContext;
/**
 * dao base class
 * <AUTHOR>
 *
 */
public class AppDaoContext extends DaoContext {
	

	@Override
	protected String getAppDatasourceName() {
		return Constants.YW_DS;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	@Override
	protected boolean loginCheck() {
		return true;
	}

	protected EasyCache getCache() {
		return CacheManager.getMemcache();
	}
}
