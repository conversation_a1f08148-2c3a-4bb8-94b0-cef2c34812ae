<?xml version="1.0" encoding="UTF-8"?>
<project name="makewar" default="deploy" basedir=".">
    <property name="MARS_HOME"  value="D:\work\mars-server-2.1.2" />
    <property name="model"  value="ICCSCallRecordGW" />
    
	<!--生成WAR-->
		<target name="buildWAR"  >
			<jar jarfile="${model}.war" compress="yes">
				<fileset dir="WebContent" >
				</fileset>
			</jar>
		</target>
	<target name="deploy" depends="buildWAR">
		<!--部署war包-->
		<copy todir="${MARS_HOME}\webapps" overwrite="true" >
			<fileset dir="./">
				<include name="${model}.war" />
			</fileset>
		</copy>
		
	</target>
</project>

