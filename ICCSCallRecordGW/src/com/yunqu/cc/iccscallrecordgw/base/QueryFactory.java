package com.yunqu.cc.iccscallrecordgw.base;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyQuery;


/**
 * 
 * <AUTHOR>
 *
 */
public class QueryFactory {

	
	private static EasyQuery writeQuery1 = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	private static EasyQuery writeQuery2 = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	private static EasyQuery readQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);

	static{
//		writeQuery1.setLogger(MediagwLogger.getLogger());
	}
	
	public static EasyQuery getReadQuery(){
		return readQuery;
	}
	public static EasyQuery getQuery(){
		return writeQuery1;
	}
	
	public static EasyQuery getQuery(String entId){
		if(StringUtils.isBlank(entId)){
			return writeQuery1;
		}
		int _entId = Integer.parseInt(entId);
		if(_entId%2==0){  
			return writeQuery1;
		}
		return writeQuery2;
	}
}
