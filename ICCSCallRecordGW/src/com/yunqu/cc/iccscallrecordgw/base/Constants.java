package com.yunqu.cc.iccscallrecordgw.base;

import com.yq.busi.common.util.ConfigUtil;

public class Constants {
	public static final String APP_NAME = "ICCSCallRecordGW";
	
	public static final String DS_NAME = "yw-ds";
	
	public static final String INT_PASSWORD = ConfigUtil.getString(APP_NAME, "INT_PASSWORD", "YQ_85521717");
	
	/**
	 * 用于识别客户话单地区,如SD-顺德 ,HF-合肥
	 */
	public static final String REGION_CODE = ConfigUtil.getString(APP_NAME, "REGION_CODE", "SD");
	
	//振铃事件
	public static final String EVT_ALTERING = "evtAltering";
	//接通事件
	public static final String EVT_CONNECTED = "evtConnected";
	//挂断事件
	public static final String EVT_DISCONNECTED = "evtDisConnected";
	//保持事件
	public static final String EVT_HELD = "evtHeld";
	
	/**
	 * 用于识别客户话单地区,如SD-顺德 ,HF-合肥
	 */
	public static final String SMS_CONTENT = ConfigUtil.getString(APP_NAME, "SMS_CONTENT", "请对本次通话服务评价：1-非常满意,2-满意,3-一般,4-对本次话务服务不满意,5-对结果不满意,您的意见是我们提升的动力！");
	
	public static final String ITEM = ConfigUtil.getString(APP_NAME, "ITEM", "安装");
	public static final String CSSGW_URL = ConfigUtil.getString(APP_NAME, "CSSGW_URL", "http://10.16.70.188:8080/uinterface/Receive.do");

	/**
	 * 统一网关 URL
	 */
	public static String getUnifiedGatewayUrl(){
		return ConfigUtil.getString(APP_NAME, "UNIFIED_GATEWAY_URL", "https://ccuat.midea.com/uinterface/Receive.do");
	}
}
