package com.yunqu.cc.iccscallrecordgw.base;

import org.apache.log4j.Logger;
import org.easitline.common.core.dao.DaoContext;
/**
 * dao base class
 * <AUTHOR>
 *
 */
public class AppDaoContext extends DaoContext {
	public Logger logger = CommLogger.getCommLogger();

	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_NAME;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	@Override
	protected boolean loginCheck() {
		return false;
	}

}
