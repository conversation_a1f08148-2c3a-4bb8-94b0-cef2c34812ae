package com.yunqu.cc.iccscallrecordgw.dao;

import java.sql.SQLException;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;

import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.iccscallrecordgw.base.AppDaoContext;
import com.yunqu.cc.iccscallrecordgw.base.Constants;
import com.yunqu.cc.iccscallrecordgw.bean.CallRecordBean;
import org.easitline.common.utils.string.StringUtils;

public class CallRecordDao extends AppDaoContext {
	
	public boolean addCallRecord(CallRecordBean callRecord) {
		try {
			return callRecord.save();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			logger.error(CommonUtil.getClassNameAndMethod(this)+ "保存语音通话记录异常:"+ e.getMessage(), e);
		}
		return false;
	}
	
	public boolean editCallRecord(CallRecordBean callRecord) {
		try {
//			String sessionId = callRecord.getString(CallRecordRefEnum.connId.getCallRecordField());
//			String callId = callRecord.getString(CallRecordRefEnum.callId.getCallRecordField());
//			callRecord.setPrimaryKeys("SESSION_ID", "CALL_ID","AGENT_NO");
			callRecord.setPrimaryKeys("ID");
//			callRecord.setPrimaryValues(sessionId, callId);
			return callRecord.update();
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+ "更新语音通话记录异常:"+ e.getMessage(), e);
		}
		return false;
	}
	
	public boolean isAdd(String sessionId, String callId) {
		boolean result = false;

		try {
			EasySQL sql = getEasySQL("select count(1) from C_PF_CALL_RECORD where ");
			sql.append(sessionId, "SESSION_ID = ? ");
			sql.append(callId, "and CALL_ID = ? ");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "通过sessionId和callId获取语音记录Sql:"+ sql.getSQL()+",params:"+sessionId+"、"+ callId);
			
			result = !this.getQuery().queryForExist(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+ "通过sessionId和callId获取语音记录异常:"+ e.getMessage(), e);
		}
		
		return result;
	}

	/**
	 * 发送短信满意度
	 * @param caller   客户号码
	 * @param connid   呼叫id
	 * @param modelId  事业部
	 * @param category 品牌
	 * @param conent   内容
	 */
	public void sendSatisfySms(String caller, String connid,String modelId, String category,String conent) {
		try {
			EasyRecord record = new EasyRecord("C_GENESYS_SMS","ID");
			record.set("ID", IDGenerator.getDefaultNUMID());
			record.set("PHONENUM", caller);
			record.set("TYPE", "01"); //01-挂机满意度  02-挂机短信
			record.set("CREATE_TIME", DateUtil.getCurrentDateStr());
			record.set("CONNECT_ID", connid);
			record.set("STATUS", "01"); //01-待发送 02-已发送(转入短信记录表)
			record.set("MODEL_ID", modelId);
			record.set("CATEGORY",category);
			record.set("CONTENT",Constants.SMS_CONTENT);
			this.getQuery().save(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+ "保持客户挂机短信异常:"+ e.getMessage(), e);
		}
		
	}

	public void saveCallRecordExp(String sessionId,String outCallType){
		try {
			if (StringUtils.isBlank(sessionId)){
				logger.info(CommonUtil.getClassNameAndMethod(this)+ "保存外呼记录失败,sessionId为空");
				return;
			}
			EasySQL sql = new EasySQL();
			sql.append("SELECT COUNT(1) from C_PF_CALL_RECORD_EXP WHERE ");
			sql.append(sessionId," SESSION_ID = ?");
			int count = this.getQuery().queryForInt(sql.getSQL(), sql.getParams());
			if (count > 0){
				logger.info(sessionId+"转接为同一sessionId,不再继续记录外呼类型");
				return;
			}
			EasyRecord record = new EasyRecord("C_PF_CALL_RECORD_EXP","SESSION_ID");
			record.set("SESSION_ID", sessionId);
			record.set("OUT_CALL_TYPE", outCallType);
			record.set("CREATE_TIME", DateUtil.getCurrentDateStr());
			this.getQuery().save(record);
			logger.info(CommonUtil.getClassNameAndMethod(this)+ sessionId +"保存外呼记录成功");
		}catch(Exception e){
			logger.error(CommonUtil.getClassNameAndMethod(this)+ sessionId +"保存外呼记录失败:"+ e.getMessage(), e);
		}
	}
}
