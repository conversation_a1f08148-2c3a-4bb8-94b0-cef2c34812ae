package com.yunqu.cc.iccscallrecordgw.dao;

import java.sql.SQLException;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.iccscallrecordgw.base.AppDaoContext;

public class AgentInfoDao extends AppDaoContext {
	
	public JSONObject getAgentInfoByNo(String agentNo) {
		JSONObject result = null;
		if (StringUtils.isNotBlank(agentNo)) {
			EasySQL sql = this.getEasySQL("select t1.USER_ACC, t1.WORK_NO, t1.USER_NAME, t1.USER_NICKNAME, t1.DEPT_CODE, t1.AREACODE, t1.EP_CODE ");
			sql.append("from C_YG_EMPLOYEE t1 where t1.STATUS != '04' ");
			sql.append(agentNo, " AND  t1.WORK_NO = ? ");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "获取坐席信息sql:"+ sql.getSQL()+", agentNo:" +agentNo);
			try {
				EasyRow row = getQuery().queryForRow(sql.getSQL(), sql.getParams());
				if (row != null) {
					result = row.toJSONObject();
				}
			} catch (SQLException e) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+ "获取坐席信息异常:"+ e.getMessage(), e);
			}
		}
		return result;
	}
	/**
	 * 坐席是否有权限	 
	 * @param agentNo
	 * @return
	 */
	public boolean getAgentSMS(String agentNo) {
		JSONObject result = null;
		if (StringUtils.isNotBlank(agentNo)) {
			EasySQL sql = this.getEasySQL("SELECT count(1)as count FROM V_SEND_STATISFY_TYPE WHERE  VOICE='Y' ");
			sql.append(agentNo, " and WORK_NO=?  " ,false);
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "获取坐席信息sql:"+ sql.getSQL()+", agentNo:" +agentNo);
			try {
				int count = getQuery().queryForInt(sql.getSQL(), sql.getParams());
				if (count != 0) {
					return true;
				}
			} catch (SQLException e) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+ "获取坐席信息异常:"+ e.getMessage(), e);
			}
		}
		return false;
	}
}
