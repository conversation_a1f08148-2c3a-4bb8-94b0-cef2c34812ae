package com.yunqu.cc.iccscallrecordgw.listener;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

import org.apache.log4j.Logger;

import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.iccscallrecordgw.base.CommLogger;
import com.yunqu.cc.iccscallrecordgw.service.IVRMgr;


/**
 * 应用初始化 InitListener.java
 */
@WebListener
public class InitListener implements ServletContextListener{

	private Logger logger = CommLogger.getCommLogger();


	/**
	 * 模块启动
	 */
	@Override
	public void contextInitialized(ServletContextEvent arg0) {
		
		//初始化定时任务时，要从其他模块的服务中获取数据，所以整体延迟执行
		logger.info(CommonUtil.getClassNameAndMethod(this)+"contextInitialized启动!");
		new Thread(){
			public void run() {
					//启动任务
				IVRMgr.getInstance().startJob();
					logger.info(CommonUtil.getClassNameAndMethod(this)+"线程池启动成功!");
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 线程池启动完成...");
				
			};
		}.start(); 	
		}
	


	/**
	 * 模块停止
	 */
	@Override
	public void contextDestroyed(ServletContextEvent arg0) {
		IVRMgr.getInstance().stopJob();
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 停止定时任务..");
		
	}
}