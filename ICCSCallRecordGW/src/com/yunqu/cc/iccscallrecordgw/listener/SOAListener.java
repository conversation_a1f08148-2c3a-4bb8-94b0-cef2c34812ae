package com.yunqu.cc.iccscallrecordgw.listener;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebListener;

import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import com.yunqu.cc.iccscallrecordgw.base.Constants;

@WebListener
public class SOAListener extends ServiceContextListener {

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		List<ServiceResource> list = new ArrayList<ServiceResource>();
		ServiceResource resource = new ServiceResource();
		resource.appName = Constants.APP_NAME;
		resource.description = "genesys话单同步接口";
		resource.serviceId = "ICCS_CALLEVENT_RECORDGW_INTEFACE";
		resource.serviceName = "genesys话单同步接口";
		resource.className = "com.yunqu.cc.iccscallrecordgw.service.CallRecordSyncService";
		list.add(resource);
		return list;
	}

}
