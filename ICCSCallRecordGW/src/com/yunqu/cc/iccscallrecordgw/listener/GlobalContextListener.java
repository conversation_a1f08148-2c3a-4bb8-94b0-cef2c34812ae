package com.yunqu.cc.iccscallrecordgw.listener;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebListener;

import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import com.yunqu.cc.iccscallrecordgw.base.Constants;

@WebListener
public class GlobalContextListener extends  ServiceContextListener {

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		List<ServiceResource> list = new ArrayList<ServiceResource>();
		ServiceResource  resource = new ServiceResource();
		resource.appName     =  Constants.APP_NAME; //服务所在的WAR应用名   
		resource.className   =  "com.yunqu.cc.iccscallrecordgw.service.CallUrlService";//服务实现类，类必须实现IService接口
		resource.description =  "iccportal5来电弹屏服务";//服务描述
		resource.serviceId   =  "CALLIN_URL_ICCPORTAL5";//服务ID，必须唯一，服务是通过serviceId继续查找并调用
		resource.serviceName =  "iccportal5来电弹屏服务";//服务名称
		list.add(resource);
		return list;
	}
	
}
