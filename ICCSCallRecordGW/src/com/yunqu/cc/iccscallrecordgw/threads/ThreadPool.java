package com.yunqu.cc.iccscallrecordgw.threads;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadPool {
	public static ThreadPool instance = null;
	public ThreadPoolExecutor  executor = null;
	
	public ThreadPool(){
		executor = new ThreadPoolExecutor(16, 24, 5, TimeUnit.SECONDS,new ArrayBlockingQueue<Runnable>(400));
	}
	
	public static ThreadPool getInstance(){
		if (instance==null){
			instance = new ThreadPool();
			return instance;
		}else{
			return instance;
		}
	}
	
	public void execute(Runnable thread){
		executor.execute(thread);
	}
	
	public void shutDown(){
		executor.shutdown();
	}
}
