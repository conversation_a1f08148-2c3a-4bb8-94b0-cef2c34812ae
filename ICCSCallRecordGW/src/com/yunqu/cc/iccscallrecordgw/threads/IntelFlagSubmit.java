package com.yunqu.cc.iccscallrecordgw.threads;

import java.sql.SQLException;
import java.util.List;

import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yq.busi.common.util.security.SecurityUtil;
import com.yunqu.cc.iccscallrecordgw.base.CommLogger;
import com.yunqu.cc.iccscallrecordgw.base.Constants;
/**
 * 通过电话号码查询是否寻找智能产品
 * <AUTHOR>
 *
 */
public class IntelFlagSubmit implements Runnable{
	 protected static EasyQuery getQuery()
	  {
		 return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	  }
	public static EasyCache cache = CacheManager.getMemcache();
	private Logger logger = CommLogger.getCommLogger();
	private JSONObject data;
	private String phone;
	
	public IntelFlagSubmit(String phone ,JSONObject data ){
		this.phone = phone;
		this.data = data;
	}
	@Override
	public void run() {
		if(data.get("userData")!=null){
			String ITEM=String.valueOf(data.getJSONObject("userData").get("ITEM"));
			String[] AZ_ITEM=Constants.ITEM.split(",");
			if(included(AZ_ITEM,ITEM)){
				HttpResp queryIntelFlagByMobile = queryIntelFlagByMobile(phone);
				try {
					JSONObject resp = JSON.parseObject(queryIntelFlagByMobile.getResult()); 
					logger.info("通过电话号码查询是否寻找智能产品"+resp.toJSONString());
					String cacheId = String.valueOf("ICCSCallRecordGW-IntelFlagByMobile-"+data.get("caller"));
					if(resp.getString("respCode").equals("000")){
						if(resp.getJSONObject("respData").get("intelFlag")!=null){
							logger.info(data.get("caller")+"是智能产品");
							cache.put(cacheId,""+resp.getJSONObject("respData").get("intelFlag"),60*60);//缓存
						}
					}
				} catch (Exception e) {
					logger.error("IService请求失败,请求参数,原因"+e.getMessage());
					
				}
			}
		}
		
	}
	public boolean included(String[] AZ_ITEM,String ITEM){
		String items="";
		JSONArray jsonArray=new JSONArray();
		for(String s: AZ_ITEM){
			items=items.equals("")?" name  like '%"+s+"%'":items+" or  name like '%"+s+"%'";
		}
		String sql="select code from C_GENESYS_SKILL t where  "+items;
		try {
			String cacheId = String.valueOf("ICCSCallRecordGW-IntelFlagSubmit-item");
			String value = cache.get(cacheId);
			if((value==null||"".equals(value))){//判断是否存在缓存
					 List<JSONObject> queryForList = getQuery().queryForList(sql, null,new JSONMapperImpl());
					 logger.info("C_GENESYS_SKILL查询sql"+sql);
					 for(JSONObject e : queryForList){
						 jsonArray.add(e);
					 }
					// cache.put(cacheId, jsonArray.toJSONString(),6000);//缓存记录 xx分钟后清空（防止卡死不执行）

			 }else{//存在缓存返回
				 logger.error("存在缓存返回"+value);
				 jsonArray = JSONArray.parseArray(value);
			 }
			 for(Object e : jsonArray){
				if(ITEM.equals(JSON.parseObject(e.toString()).getString("CODE"))) {
					 logger.info("符合查询寻找智能产品条件"+ITEM);
					return true;
				}
			 }
		} catch (SQLException e) {
			logger.error("C_GENESYS_SKILL查询sql出错"+sql+";"+e.getMessage());
			e.printStackTrace();
		}
		 logger.info("不符合查询寻找智能产品条件"+ITEM);

		return false;
	}
	public  static HttpResp queryIntelFlagByMobile(String phone) {
		JSONObject json = new JSONObject();
		json.put("sender", "MIDEA_AGENT");
		json.put("password", "YQ_85521717");
		json.put("serialId", IDGenerator.getDefaultNUMID());
		json.put("command", "queryIntelFlagByMobile"); 
		json.put("serviceId", "CSSGW-OTHER");
		json.put("timestamp", DateUtil.getCurrentDateStr());
		JSONObject params=new JSONObject();
		params.put("mobile", phone);
		json.put("params", params);
		String sign = SecurityUtil.encryptMsgByMD5(json.getString("sender")+json.getString("password")+json.getString("timestamp")+json.getString("serialId"));
		json.put("signature", sign);
		HttpResp resp = HttpUtil.post(Constants.CSSGW_URL,json.toJSONString(),GWConstants.ENCODE_UTF8);
		return resp;
		
	}
}
