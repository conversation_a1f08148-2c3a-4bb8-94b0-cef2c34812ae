package com.yunqu.cc.iccscallrecordgw.service;

import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yq.busi.common.util.security.SecurityUtil;
import com.yunqu.cc.iccscallrecordgw.base.QueryFactory;
import com.yunqu.cc.iccscallrecordgw.threads.ThreadPool;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.iccscallrecordgw.base.CommLogger;
import com.yunqu.cc.iccscallrecordgw.base.Constants;
import com.yunqu.cc.iccscallrecordgw.bean.CallRecordBean;
import com.yunqu.cc.iccscallrecordgw.bean.CallRecordRefEnum;
import com.yunqu.cc.iccscallrecordgw.bean.response.CallRecordResultEnum;
import com.yunqu.cc.iccscallrecordgw.dao.AgentInfoDao;
import com.yunqu.cc.iccscallrecordgw.dao.CallRecordDao;

import static com.yq.busi.common.util.IDGenerator.getDefaultNUMID;

public class CallRecordSyncService extends IService {
	private Logger logger = CommLogger.getCommLogger();

	private CallRecordDao callRecordDao = new CallRecordDao();


	private AgentInfoDao agentInfoDao = new AgentInfoDao();

	private  EasyCache cache = CacheManager.getMemcache();

	@Override
	public JSONObject invoke(JSONObject params) throws ServiceException {

		CallRecordResultEnum result = CallRecordResultEnum.SUCCESS;

		try {
			String messageId = params.getString("messageId");

			if (!Constants.EVT_CONNECTED.equals(messageId) && !Constants.EVT_DISCONNECTED.equals(messageId)) {
				return result.parseJSON();
			}

			logger.info(CommonUtil.getClassNameAndMethod(this)+ "写话单接口-获取接口访问参数:"+ params.toJSONString());

			CallRecordBean callRecord = this.mappingCallRecord(params);
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "对象转换后的数据:"+ callRecord.toJSONString());

			//获取分机号,ccbar目前应该没有传入
			String agentNo = callRecord.getString(CallRecordRefEnum.agentNo.getCallRecordField());

			//分机号为空时，获取agentId，格式为  1013@1000R
			if (StringUtils.isBlank(agentNo)) {
				agentNo = params.getString("agentId");
			}
			if (StringUtils.isBlank(agentNo)) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+ "agentNo参数为空");
				return CallRecordResultEnum.FAIL.parseJSON();
			}

			//从agentId中拆分出分机号
			agentNo = filterAgentNo(agentNo);
			callRecord.put(CallRecordRefEnum.agentNo.getCallRecordField(), agentNo);

			String answerTime = callRecord.getString(CallRecordRefEnum.agentAnswerTime.getCallRecordField());
			String endTime = callRecord.getString(CallRecordRefEnum.endTime.getCallRecordField());

//			String sessionId = callRecord.getString(CallRecordRefEnum.connId.getCallRecordField());
//			String callId = callRecord.getString(CallRecordRefEnum.callId.getCallRecordField());

//			if (Constants.EVT_CONNECTED.equals(messageId) && callRecordDao.isAdd(sessionId, callId)) {//接通事件，需要新增语音记录

			//从ccbar获取通话唯一id
			JSONObject userData = params.getJSONObject("userData");
			String uuid = IDGenerator.getDefaultNUMID();
			String thisQueue = ""; //接入队列
			String outCallType = ""; //外呼类型
			String sessionId = "";
			String called = "";
			if(userData!=null){
				uuid = userData.getString("callSerialId");
				outCallType = userData.getString("outCallType");
				//用于溢出通话导致的易迅传入connid和我们保存的不一致
				sessionId = userData.getString("UD_ConnID");
				//用于溢出通话导致的易迅传入被叫号码有问题兼容
				called = userData.getString("UD_DNIS");
				if(StringUtils.isNotBlank(sessionId)){
					callRecord.put("SESSION_ID",sessionId);
				}
				if(StringUtils.isNotBlank(called)){
					callRecord.put("CALLED",called);
				}
				if(StringUtils.isBlank(uuid)){
					logger.error(CommonUtil.getClassNameAndMethod(this)+" ccbar未传入 userdata 里的唯一的通话id,取唯一id作为话单id...");
				}
				thisQueue = userData.getString("thisQueue");
			}else{
				logger.error(CommonUtil.getClassNameAndMethod(this)+" ccbar未传入userdata,取唯一id作为话单id...");
			}

			callRecord.put("ID", uuid);
			//呼叫接入时，写入初始话单
			if (Constants.EVT_CONNECTED.equals(messageId)) {

				//根据工单查询坐席信息，设置到话单里
				JSONObject agent = getAgent(agentNo);
				if (agent!=null) {
					callRecord.put("AGENT_ACC", agent.getString("USER_ACC"));
					callRecord.put("AGENT_NAME", agent.getString("USER_NAME"));
					callRecord.put("AGENT_DEPT", agent.getString("DEPT_CODE"));
					callRecord.put("EP_CODE", agent.getString("EP_CODE"));
					callRecord.put("OP_AREA_CODE", agent.getString("AREACODE"));
				}
				callRecord.put("CHANNEL_ID", "99");//固定渠道，可能需要修改
				callRecord.put("REGION_CODE", Constants.REGION_CODE);//用于区别话单生成的地区

				//ccbar接入类型：2 外线呼入 6 呼出  3 转移呼入;  对应话单 IN_TYPE: 01-正常接入 02-外呼回拨接入 03-坐席转移后接入
				//ccbar
				String createCause = callRecord.getString(CallRecordRefEnum.createCause.getCallRecordField());
				setCreateCause(createCause,callRecord,thisQueue);

				//对于呼出需要拿随路数据入库
				if("6".equals(createCause) && userData!=null){
					//业务类型：ICC_BUSI_TYPE
					//业务id：ICC_BUSI_ID
					//外呼事业部标识：ICC_DIVISION_ID
					callRecord.put("ICC_BUSI_TYPE", userData.getString("ICC_BUSI_TYPE"));
					callRecord.put("ICC_BUSI_ID", userData.getString("ICC_BUSI_ID"));
					callRecord.put("ICC_DIVISION_ID", userData.getString("ICC_DIVISION_ID"));
					String iccCoefficient = userData.getString("ICC_COEFFICIENT");
					if(StringUtils.isNotBlank(iccCoefficient)){
						callRecord.put("ICC_COEFFICIENT", userData.getString("ICC_COEFFICIENT"));
					}
				}

				callRecord.put("STATUS", "06");
				try {
					if("2".equals(createCause) ){
						String cacheId = "ICCS_GW_TAG_REPEAT_CALL_"+callRecord.get("CALLER");
						String value = cache.get(cacheId);
						if((value==null||"".equals(value))){//判断是否存在缓存
							cache.put(cacheId,cacheId,60*60*5);
							callRecord.put("REPEAT_CALL", "N");
						}else{//存在则是重复来电
							callRecord.put("REPEAT_CALL", "Y");
						}
					}
				} catch (Exception e) {
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 存在则是重复来电:"+e.getMessage());
				}


				callRecordDao.addCallRecord(callRecord);
				callRecordDao.saveCallRecordExp(callRecord.getString("SESSION_ID"),outCallType);
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 接入时新增话单:"+callRecord.toJSONString());

			}
			//挂断事件，需要更新语音记录
			else if (Constants.EVT_DISCONNECTED.equals(messageId)) {
	//			double lens = DateUtil.calDurationHours(answerTime, endTime);
				if (StringUtils.isBlank(endTime)) {
					endTime = DateUtil.getCurrentDateStr();
					logger.error(CommonUtil.getClassNameAndMethod(this)+" ccbar未传入endTime,取当前时间作为话单结束时间,"+endTime);
					callRecord.put(CallRecordRefEnum.endTime.getCallRecordField(), endTime);
				}
				String firstAnswerTime = QueryFactory.getQuery().queryForString("select answer_time from c_pf_call_record where id = ? ",new Object[]{callRecord.getString("ID")});

				if(StringUtils.isNotBlank(firstAnswerTime)){
					answerTime = firstAnswerTime;
				}
				//计算时长
				double lens = DateUtil.calDurationMills(answerTime, endTime)/1000;
				if(lens<0){
					lens  =  0;
				}
				callRecord.put("LENS", lens);

				//移除不需要更新的字段
				callRecord.remove("CALL_ID");
				callRecord.remove("RINGING_TIME");
				callRecord.remove("DIRECTION");
				callRecord.remove("CALLER");
				callRecord.remove("ANSWER_TIME");
				callRecord.remove("AGENT_NO");
				callRecord.remove("CALLED");
				callRecord.remove("IN_TYPE");
				callRecord.remove("SESSION_ID");
				callRecord.remove("CALL_UUID");
				//坐席挂断  由亿迅实现相关满意度流程
				if(params.get("hangupType")!=null&&"agent".equals(params.getString("hangupType"))){
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 坐席挂断");
					callRecord.put(CallRecordRefEnum.hangupType.getCallRecordField(), "02");//agent，说明是坐席挂断
					callRecord.put("STATUS", "06");
					callRecordDao.editCallRecord(callRecord);
				}else{
					//客户挂断 进行满意度评价流程
					String finalAgentNo = agentNo;
					//开启线程池进行异步调用
					callRecord.put("STATUS", "06");
					callRecordDao.editCallRecord(callRecord);
					try {
						 String finalCalled = called;
						 String finalSessionId = sessionId;
						ThreadPool.getInstance().execute(() -> {
							callRecord.put(CallRecordRefEnum.hangupType.getCallRecordField(), "01");//没有值，说明是客户挂断
							//用户挂机推送满意度 调用online模块接口
							JSONObject json = new JSONObject();
							json.put("sender", "ICCSCallRecordGW");
							json.put("password", "YQ_85521717");
							json.put("serialId", getDefaultNUMID());
							json.put("command","sendSatisfyByVoice");
							json.put("serviceId", "ONLINE_JOB");
							json.put("timestamp", DateUtil.getCurrentDateStr());
							String sign = SecurityUtil.encryptMsgByMD5(json.getString("sender")+json.getString("password")+json.getString("timestamp")+json.getString("serialId"));
							json.put("signature", sign);
							JSONObject data = new JSONObject();
							data.put("caller",params.getString("caller"));
							data.put("called", finalCalled);
							data.put("sessionId", finalSessionId);
							data.put("agentNo", finalAgentNo);
							json.put("data",data);
							JSONObject respJson = null;
							try {
								long start = System.currentTimeMillis();
								HttpResp resp = HttpUtil.post(Constants.getUnifiedGatewayUrl(), json.toJSONString(), GWConstants.ENCODE_UTF8);
								long end = System.currentTimeMillis();
								CommLogger.getLogger("online").info(params.getString("connId")+":"+"耗时:"+(end-start));
								respJson = JSON.parseObject(resp.getResult());
							} catch (Exception e) {
								logger.info(CommonUtil.getClassNameAndMethod(this)+" 客户"+ params.getString("caller") + "挂机，推送满意度评价:"+respJson.toJSONString());
							}
							logger.info(CommonUtil.getClassNameAndMethod(this)+" 客户:"+ params.getString("caller") +"挂机，推送满意度评价:"+respJson.toJSONString());
						});
					}catch (Exception e) {
						CommLogger.getLogger("online").error(CommonUtil.getClassNameAndMethod(this)+e.getMessage(),e);
					}

				}
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 结束时修改话单:"+callRecord.toJSONString());
			}


		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+ "保存ICCS话单异常:"+ e.getMessage(), e);
			result = CallRecordResultEnum.FAIL;
		}
		return result.parseJSON();
	}



	/**
	 * 根据坐席工号获取坐席信息，优先从缓存中读取
	 * @param agentNo
	 * @return
	 */
	private JSONObject getAgent(String agentNo) {
		//根据工单查询坐席信息，设置到话单里
		JSONObject agent = cache.get("ICCSCallRecorGW-Agent-"+agentNo);
		if(agent==null){
			agent =  agentInfoDao.getAgentInfoByNo(agentNo);
			if(agent!=null){
				cache.put("ICCSCallRecorGW-Agent-"+agentNo,agent,3600);
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 查询到工号"+agentNo+"的信息并写入缓存:"+agent.toJSONString());
			}else{
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法查询到工号"+agentNo+"的信息...");
			}
		}
		return agent;
	}

	/**
	 * 根据ccbar的话单创建类型，写入到话单里的呼叫方向和接入类型
	 * @param createCause
	 * @param callRecord
	 * @param thisQueue  转接队列
	 */
	private void setCreateCause(String createCause, CallRecordBean callRecord,String thisQueue) {

		//ccbar接入类型：2 外线呼入 6 呼出  3 转移呼入;
		//对应话单 IN_TYPE: 01-正常接入 02-外呼回拨接入 03-坐席转移后接入   DIRECTION：01-呼入 02-呼出
		if ("2".equals(createCause)) {//呼入
			callRecord.put("DIRECTION", "01");
			callRecord.put("IN_TYPE", "01");
		}else if("3".equals(createCause)){
			callRecord.put("DIRECTION", "01");
			//转接时，要区分转大电还是转小电
			if("666668".equals(thisQueue) || "777778".equals(thisQueue)){ //666668顺德大电   777778合肥大电
				callRecord.put("IN_TYPE", "04"); //转大电
			}else if("666669".equals(thisQueue) || "777779".equals(thisQueue)){//666669顺德小电   777779合肥小电
				callRecord.put("IN_TYPE", "05"); //转小电
			}else{
				callRecord.put("IN_TYPE", "03");
			}
		} else if("6".equals(createCause)) {//呼出
			callRecord.put("DIRECTION", "02");
			callRecord.put("IN_TYPE", "02");
		}

		//由于genesys和ccbar对转移话单没对接上；只能在写入话单时判断处理；
		//转移的话单的sessionId都是一样，第一次接入时，会将sessionId写入缓存，转接后，缓存里存在该sessionId，则将该通通话设置为转接记录
		String sessionId = callRecord.getString("SESSION_ID");
		if(StringUtils.isNotBlank(sessionId)){
			String counter = cache.get("ICCSCallRecorGW-connid-counter-"+sessionId);
			if(StringUtils.isNotBlank(counter)){
				if("666668".equals(thisQueue) || "777778".equals(thisQueue)){ //666668顺德大电   777778合肥大电
					callRecord.put("IN_TYPE", "04"); //转大电
				}else if("666669".equals(thisQueue) || "777779".equals(thisQueue)){//666669顺德小电   777779合肥小电
					callRecord.put("IN_TYPE", "05"); //转小电
				}else{
					callRecord.put("IN_TYPE", "03");
				}
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 设置为转移话单:"+callRecord.toJSONString());
			}
			cache.put("ICCSCallRecorGW-connid-counter-"+sessionId,"1",3600);
		}
	}

	/**
	 * 从ccbar的坐席工号格式中，拿出真正的工号
	 * @param agentNo  格式为 工号@企业号，如 8001@1000
	 * @return
	 */
	private String filterAgentNo(String agentNo) {
		String result = agentNo;
		if (StringUtils.isNotBlank(agentNo) && agentNo.indexOf("@")!=-1) {
			String[] temps = result.split("@");
			result = temps[0];
		}
		return result;
	}

	/**
	 * 将传过来的callInfo字段转为数据库对应的字段
	 * @param callInfo
	 * @return
	 */
	private CallRecordBean mappingCallRecord(JSONObject callInfo) {
		CallRecordBean callRecord = new CallRecordBean();
		for (Map.Entry<String, Object> entry : callInfo.entrySet()) {
			CallRecordRefEnum callRecordField = CallRecordRefEnum.findCallRecordRefEnum(entry.getKey());
			if (callRecordField!=null)
				callRecord.put(callRecordField.getCallRecordField(), entry.getValue());
		}
		return callRecord;
	}

}
