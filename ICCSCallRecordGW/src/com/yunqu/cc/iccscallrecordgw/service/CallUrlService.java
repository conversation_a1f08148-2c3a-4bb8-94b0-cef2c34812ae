package com.yunqu.cc.iccscallrecordgw.service;

import java.util.Arrays;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.iccscallrecordgw.base.CommLogger;
import com.yunqu.cc.iccscallrecordgw.threads.IntelFlagSubmit;

/**
 * 任务来电弹屏服务
 * 
 * // event.eventId //呼叫id，全局唯一 // event.eventName //呼叫id，全局唯一 //
 * event.callSerialId //呼叫id，全局唯一 // event.busiOrderId //业务订购ID，全局唯一 //
 * event.entId //企业Id，全局唯一 // event.custId //客户ID // event.createCause //呼叫创建原因
 * 
 * <AUTHOR>
 *
 */
public class CallUrlService extends IService {
	private Logger logger = CommLogger.logger;

	@Override
	/**
	 * 
	 * 
	 * JSONObject eventInfo = new JSONObject(); eventInfo.put("eventId",
	 * getEvent(messageId)); //事件 eventInfo.put("eventName",
	 * getEventName(messageId)); //事件名称 eventInfo.put("caller",
	 * iccsObject.getString("caller")); //主叫 eventInfo.put("called",
	 * data.getString("called")); //被叫 eventInfo.put("station",
	 * iccsObject.getString("telephone")); //分机号 eventInfo.put("callSerialId",
	 * userData.getString("callSerialId")); //呼叫序列ID
	 * eventInfo.put("busiOrderId", userData.getString("busiOrderId")); //业务订购ID
	 * if(userData.containsKey("taskId")) eventInfo.put("taskId",
	 * userData.getString("taskId")); //任务ID if(userData.containsKey("objId")){
	 * eventInfo.put("objId", userData.getString("objId")); //任务对象ID
	 * eventInfo.put("custId", getCustId(entId,userData.getString("objId"))); }
	 * eventInfo.put("createCause", iccsObject.getString("createCause"));
	 * //呼叫创建原因 eventInfo.put("busiId", userData.getString("busiId"));
	 * eventInfo.put("custTempId", userData.getString("custTempId"));
	 * eventInfo.put("agentId",iccsObject.getString("agentId"));
	 * eventInfo.put("entId", entId);
	 * 
	 * 
	 * 
	 * JSONArray urls = new JSONArray(); // JSONObject url1 = new JSONObject();
	 * // url1.put("id", "myAutoTask"); // url1.put("title", "来电弹屏"); //
	 * url1.put("type", "1"); // url1.put("url", "www.sina.com.cn"); //
	 * urls.add(url1); // JSONObject url2 = new JSONObject(); // url2.put("id",
	 * "crmUrl"); // url2.put("title", "新窗口弹屏"); // url2.put("type", "2"); //
	 * url2.put("url", "www.sina.com.cn"); // urls.add(url2);
	 */
	public JSONObject invoke(JSONObject jsonObject) throws ServiceException {

		if (jsonObject == null) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 无法返回来电弹屏url,请求json为空!");
			return null;
		}
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 收到来电弹屏服务请求:" + jsonObject.toJSONString());

		// String caller = jsonObject.getString("caller");
		String called = jsonObject.getString("called");
		String custPhone = jsonObject.getString("custPhone");
		String sessionRecordId = "";

		JSONObject userData = jsonObject.getJSONObject("userData");
		if (userData != null) {
			sessionRecordId = userData.getString("connId");
		}

		JSONArray urls = new JSONArray();
		String url = "";

		JSONObject taskUrl = new JSONObject();
		taskUrl.put("id", "voiceCallIn");
		taskUrl.put("title", "客服来电弹屏");
		taskUrl.put("unique", "1");

		String number = "075728690680,075728690681,075728690682,075728690683,075728690684,075728690685,075728690686,075728690687,075728690688,075728690689,075728690690,075728690691,075728690692,075728690693,075728690694,075728690695,075728690696,075728690697,075728690698,075728690699";
		String[] split = number.split(",");
		// 安得物流
		if (called.endsWith("28690678")) {
			url = "https://ics.annto.com/index.html#/mod/service/hotlineOrders?phone=" + custPhone;
			taskUrl.put("type", "2");
//		} else if (called.endsWith("29396148")) {
//			url = "http://lcs.annto.com//index.html#/mod/service/hotlineOrders?phone=" + custPhone;
//			taskUrl.put("type", "2");
		}else if(endsWithLoop(split,called)) {
			url = "https://ics.annto.com/index.html#/mod/service/hotlineOrders?phone=" + custPhone;
			taskUrl.put("type", "2");
//		}
//		else if (called.endsWith("29396116") || called.endsWith("64850667")) {// 南海东芝
//			url = "http://service.4008886666.cn/afterService.do?para=callCenterRequisit&callTel=" + custPhone;
//			taskUrl.put("type", "2");
		} else {
			custPhone=getPhone(custPhone);
			
			if (userData.get("manual") != null) {
				if (userData.getString("manual").equals("1")) {// 接入单机器人报装
					url = "/iccportal5/portal?action=voice" + "&phoneNum=" + custPhone + "&sessionRecordId="
							+ sessionRecordId + "&custPhone=" + custPhone + "&manual=true";
					taskUrl.put("type", "1");
				} else if (userData.get("busi_data") != null) {
					String jsonStr = userData.getString("busi_data");
					logger.info(CommonUtil.getClassNameAndMethod(this) + " 转人工" + jsonStr);
					jsonStr = jsonStr.replaceAll("\\|", ",");
					JSONObject content = JSONObject.parseObject(jsonStr);

					if (content.getString("manual").equals("2") && content.get("orderId") != null) {// 机器人回访弹屏
						url = "/neworder/servlet/revisit?action=RobotRevisitSheetDetail&id=" + content.get("orderId")
								+ "&manual=true";
						taskUrl.put("type", "1");
						IVRMgr.getInstance().putData(content);
						logger.error(CommonUtil.getClassNameAndMethod(this) + " 写入消息队列中的对象!" + content.toJSONString());

					} else if (content.getString("manual").equals("3") && content.get("orderNo") != null) {// 机器人净水弹屏
						url = "/neworder/servlet/planreMind?action=PlanremindDetail&planRemindOrderNo="
								+ content.get("orderNo") + "&manual=true";
						taskUrl.put("type", "1");
						IVRMgr.getInstance().putData(content);
						logger.error(CommonUtil.getClassNameAndMethod(this) + " 写入消息队列中的对象!" + content.toJSONString());
					} else {// 录单页面
						url = "/iccportal5/portal?action=voice" + "&phoneNum=" + custPhone + "&sessionRecordId="
								+ sessionRecordId + "&custPhone=" + custPhone+ "&manual=true";
						taskUrl.put("type", "1");
					}
				} else {// 录单页面
					url = "/iccportal5/portal?action=voice" + "&phoneNum=" + custPhone + "&sessionRecordId="
							+ sessionRecordId + "&custPhone=" + custPhone;
					taskUrl.put("type", "1");
				}
			} else {
				url = "/iccportal5/portal?action=voice" + "&phoneNum=" + custPhone + "&sessionRecordId="
						+ sessionRecordId + "&custPhone=" + custPhone;
				taskUrl.put("type", "1");
			}
		}
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 返回来电弹屏服务请求:" + url);

		taskUrl.put("url", url);
		urls.add(taskUrl);

		JSONObject retObj = new JSONObject();
		retObj.put("urls", urls);
		Thread IntelFlag = new Thread(new IntelFlagSubmit(custPhone, jsonObject));
		IntelFlag.start();

		return retObj;

	}
	/**
	 * 手机号去掉0开通
	 * @param phone
	 * @return
	 */
	public static String getPhone(String phone){
		if(StringUtils.isNotBlank(phone)){
			if(phone.startsWith("01")&&!phone.startsWith("010")){
				phone = phone.substring(1);
			}
		}
		return phone;
	}
	public static void main(String[] args) {
		System.out.println(getPhone("011010101"));
//		String data = "{\"caller\":\"013326882131\",\"agentId\":\"3005@1000\",\"custTempId\":\"\",\"userData\":{\"ITEM\":\"1111\",\"UD_DNIS\":\"111\",\"custTempId\":\"\",\"callSerialId\":\"84129962714368584812440\",\"busiId\":\"003\",\"thisQueue\":\"1001\",\"connId\":\"007202f833a5f079\",\"busiOrderId\":\"84673143755829998431640\",\"custPhone\":\"013326882131\",\"manual\":\"1\",\"content\":\"\\\"brandCode\\\":\\\"MIDEA\\\"}\",\"busi_data\":\"{\\\"orderId\\\":\\\"1101310974\\\"|\\\"manual\\\":\\\"2\\\"}\"},\"called\":\"1001\",\"callSerialId\":\"84129962714368584812440\",\"entId\":\"1000\",\"busiId\":\"003\",\"busiOrderId\":\"84673143755829998431640\",\"custPhone\":\"013326882131\"}";
//		System.out.println(data);
//		JSONObject jsonObject = JSON.parseObject(data);
//		JSONObject userData = jsonObject.getJSONObject("userData");
//		;
//		if (userData.get("manual") != null) {
//			if (userData.getString("manual").equals("1")) {// 接入单机器人报装
//				System.out.println(1);
//			} else if (userData.get("busi_data") != null) {
//				String jsonStr = userData.getString("busi_data");
//				jsonStr = jsonStr.replaceAll("\\|", ",");
//				JSONObject content = JSONObject.parseObject(jsonStr);
//				if (content.getString("manual").equals("2") && content.get("orderId") != null) {// 机器人回访弹屏
//					System.out.println(2);
//				} else if (content.getString("manual").equals("3") && content.get("orderNo") != null) {// 机器人净水弹屏
//					System.out.println(3);
//				} else {// 录单页面
//					System.out.println(0);
//				}
//			} else {// 录单页面
//				System.out.println(0);
//			}
//		} else {
//			System.out.println(-1);
//		}

	}

	public static boolean endsWithLoop(String[] arr, String targetValue) {
		for (String s : arr) {
			if (targetValue.endsWith(s)) {
				return true;
			}
		}
		return false;
	}

}
