package com.yunqu.cc.iccscallrecordgw.service;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.iccscallrecordgw.base.CommLogger;
import com.yunqu.cc.iccscallrecordgw.base.Constants;


public class IVRMgr extends Thread{
	private Logger logger = CommLogger.getCommLogger();
	protected static EasyQuery getQuery()
	{
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	}
	
	private boolean start = true;
	
	public  boolean isStart() {
		return start;
	}
	public  void setStart(boolean start) {
		this.start = start;
	}
	private  BlockingQueue<String> blockingQueue = new LinkedBlockingQueue<String>();
	
	private static IVRMgr instance = new IVRMgr();
	
	private IVRMgr(){}
	
	
	@Override
	public void run() {
		while(start){
			//阻塞接收处理消息
			String msg;
			try {
				msg = blockingQueue.poll(1, TimeUnit.SECONDS);//阻塞1分钟
				if(StringUtils.isBlank(msg)){
					logger.error(CommonUtil.getClassNameAndMethod(this)+" 从消息队列中获取消息为空!");
					continue;
				}
				JSONObject json = JSONObject.parseObject(msg);
				if(json==null){
					logger.error(CommonUtil.getClassNameAndMethod(this)+" 从消息队列中获取消息不为json!");
					continue;
				}
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 从消息队列中获取消息对象!"+msg);
				if(json.getString("manual").equals("2")){//回访
					JSONObject jsonData = new JSONObject();
					jsonData.put("IVR_MANUAL", "Y");
					jsonData.put("ROBOT_VISIT_STATUS", "3");
					jsonData.put("NEED_CALLBACK_LIST_ID", json.getString("orderId"));
					EasyRecord record = new EasyRecord("CC_NO_ROBOT_VISIT_ORDER", "NEED_CALLBACK_LIST_ID","ROBOT_VISIT_STATUS").setColumns(jsonData);
					try {
						this.getQuery().update(record);
						logger.info(json.getString("orderId")+"单号，回访转人工成功");
					} catch (Exception e) {
						logger.error(json.getString("orderId")+"，回访转人工成功 记录保存失败 "+e.getMessage(),e);
						e.printStackTrace();
					}
				}else if(json.getString("manual").equals("3")){//净水
					JSONObject jsonData= new JSONObject();
					jsonData.put("IVR_MANUAL", "Y");
					jsonData.put("ROBOT_VISIT_STATUS", "3");
					jsonData.put("PLAN_REMIND_ORDER_NO", json.getString("orderNo"));
					EasyRecord record = new EasyRecord("CC_NO_ROBOT_REMIND_ORDER", "PLAN_REMIND_ORDER_NO","ROBOT_VISIT_STATUS").setColumns(jsonData);
					try {
						this.getQuery().update(record);
						logger.info(json.getString("orderNo")+"，净水转人工成功");
					} catch (Exception e) {
						logger.error(json.getString("orderNo")+"，净水转人工成功 记录保存失败 "+e.getMessage(),e);
						e.printStackTrace();
					}
				}
				
			} catch (InterruptedException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			
		}
	}
	
	public synchronized static IVRMgr getInstance(){
		return instance;
	}

	public  void putData(JSONObject data) {
		try {
			blockingQueue.offer(data.toJSONString());
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}


	public void startJob() {
		this.start();
		
	}


	public void stopJob() {
		this.start = false;
		
	}

	
}
