package com.yunqu.cc.iccscallrecordgw.bean;

public enum CallRecordRefEnum {
	
	connId("SESSION_ID"),
	callId("CALL_ID"),
	callUuid("CALL_UUID"),
	caller("CALLER"),
	called("CALLED"),
	serviceGroupId("SKILL_CODE"),
	agent<PERSON>o("AGENT_NO"),
	agent<PERSON><PERSON>tingTime("RINGING_TIME"),
	agentAnswerTime("ANSWER_TIME"),
	endTime("END_TIME"),
	createCause("DIRECTION"),
	recordFileName("VOICE_URL"),
	hangupType("HANGUP_TYPE"),
	isTranfer("IS_TRANSFER"),
	inType("IN_TYPE");

	private String callRecordField;
	
	CallRecordRefEnum(String callRecordField) {
		this.callRecordField = callRecordField;
	}

	public String getCallRecordField() {
		return callRecordField;
	}

	public void setCallRecordField(String callRecordField) {
		this.callRecordField = callRecordField;
	}
	
	public static CallRecordRefEnum findCallRecordRefEnum(String key) {
		CallRecordRefEnum[] fields = values();
		for (CallRecordRefEnum field: fields) {
			if (field.name().equals(key)) {
				return field;
			}
		}
		return null;
	}
	
}
