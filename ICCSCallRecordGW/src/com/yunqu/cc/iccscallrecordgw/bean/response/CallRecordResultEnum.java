package com.yunqu.cc.iccscallrecordgw.bean.response;

import com.alibaba.fastjson.JSONObject;

public enum CallRecordResultEnum {
	SUCCESS("000", "操作成功"),
	FAIL("999", "其他异常"),
	FORMATTER_FAIL("001", "消息格式错误"),
	UNDEFINED_COMMAND("002", "请求方标识不合法"),
	PASSWORD_FAIL("003", "请求方系统接入密码不合法");
	
	private String resultCode;
	private String resultMsg;
	CallRecordResultEnum(String resultCode, String resultMsg) {
		this.resultCode = resultCode;
		this.resultMsg = resultMsg;
	}
	public String getResultCode() {
		return resultCode;
	}
	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}
	public String getResultMsg() {
		return resultMsg;
	}
	public void setResultMsg(String resultMsg) {
		this.resultMsg = resultMsg;
	}
	public JSONObject parseJSON() {
		JSONObject json = new JSONObject();
		json.put("resultCode", getResultCode());
		json.put("resultMsg", getResultMsg());
		return json;
	}
	
}
