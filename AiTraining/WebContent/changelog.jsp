<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="renderer" content="webkit|ie-comp|ie-stand">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=0.5,maximum-scale=3.0,user-scalable=true" />
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="Cache-Control" content="no-cache,no-siteapp"/>
	<LINK rel="Bookmark" href="/yc-portal/favicon.ico" />
	<LINK rel="Shortcut Icon" href="/yc-portal/favicon.ico" />
	<title>更新日志</title>
	<style>
			.timeline>.timeline-item {
				list-style: none;
			}
			.timeline-item {
				position: relative;
				margin-bottom: 20px
			}
			
			.timeline-item .circle {
				width: 14px;
				height: 14px;
				border-radius: 50%;
				border: 1px solid #00CC44;
				position: absolute;
				left: 0px;
				top: 6px;
			}
			
			.timeline-item:before {
				content: '';
				width: 1px;
				height: 100%;
				background-color: #e2e2e2;
				position: absolute;
				left: 8px;
				top: 24px;
			}
			
			.timeline-item:last-child:before {
				display: none;
			}
			
			.timeline-item .content {
				padding-left: 35px
			}
			
			.timeline-item .title {
				color: #333;
				margin-top: 0px;
				margin-bottom: 4px;
				font-size: 24px;
				font-weight: 500
			}
			
			.timeline-item .title .date {
				height: 18px;
				color: #666;
				border: 1px solid #eee;
				font-size: 12px;
				margin-left: 15px;
				padding: 2px 4px;
			}
			
			.timeline-item .content .list {
				margin-top: 20px;
				padding-left: 20px
			}
			
			.timeline-item .content .list li {
				color: #666;
				margin-top: 5px;
				list-style-type: disc;
			}
		</style>
</head>
<body>
		<div style="padding:20px 40px;margin: 0 15%">
		    <h3>更新日志</h3>
			<ul class="timeline">
				<li class="timeline-item">
					<div class="circle"></div>
					<div class="content">
						<h4 class="title">1.0.7<span class="date">2018-02-02</span></h4>
						<ul class="list">
							<li>修改用户添加bug</li>
							<li>修改企业外呼计费单位</li>
						</ul>
					</div>
				</li>
				<li class="timeline-item">
					<div class="circle"></div>
					<div class="content">
						<h4 class="title">1.0.6<span class="date">2018-01-31</span></h4>
						<ul class="list">
							<li>修改查询列表输入框点击ENTER键自动刷新页面问题</li>
							<li>修改了H码后台sql代码，添加了所属运营商查询</li>
						</ul>
					</div>
				</li>
				<li class="timeline-item">
					<div class="circle"></div>
					<div class="content">
						<h4 class="title">1.0.5<span class="date">2018-01-29</span></h4>
						<ul class="list">
							<li>在企业外呼配置添加BPO呼叫前缀和BPO自动外呼坐席工号两个字段</li>
							<li>优化企业资源配置的逻辑代码</li>
						</ul>
					</div>
				</li>
				<li class="timeline-item">
					<div class="circle"></div>
					<div class="content">
						<h4 class="title">1.0.4<span class="date">2018-01-27</span></h4>
						<ul class="list">
							<li>解决坐席工号数bug</li>
							<li>解决话机数bug</li>
						</ul>
					</div>
				</li>
				<li class="timeline-item">
					<div class="circle"></div>
					<div class="content">
						<h4 class="title">1.0.3<span class="date">2018-01-26</span></h4>
						<ul class="list">
							<li>解决运营后台的企业监控bug</li>
							<li>解决运营后台任务统计报表的导出报表bug</li>
							<li>解决运营后台企业话务量统计报表bug</li>
							<li>调整运营后台的坐席监控页面</li>
							<li>优化大数据模块的部分SQL语句</li>
						</ul>
					</div>
				</li>
				<li class="timeline-item">
					<div class="circle"></div>
					<div class="content">
						<h4 class="title">1.0.2<span class="date">2018-01-25</span></h4>
						<ul class="list">
							<li>解决portal的任务统计报表bug</li>
							<li>解决portal的任务统计报表的导出报表功能报500错误</li>
							<li>解决portal的企业坐席话务量统计bug</li>
							<li>解决portal的坐席监控bug</li>
							<li>解决portal的企业监控的数据与运营后台的监控不准确的问题</li>
						</ul>
					</div>
				</li>
				<li class="timeline-item">
					<div class="circle"></div>
					<div class="content">
						<h4 class="title">1.0.1<span class="date">2018-02-02</span></h4>
						<ul class="list">
							<li>运营后台增加企业计费统计报表</li>
							<li>菜单访问地址：/yc-center/pages/report/ent_bill_stat.jsp</li>
						</ul>
					</div>
				</li>
			</ul>
		</div>
		
</body>
</html>