<%@ page language="java" contentType="text/html;charset=UTF-8" %>
	<%@ include file="/pages/common/global.jsp" %>

		<EasyTag:override name="head">
			<title>人员选择</title>
			<style>
				.ztree .line {
					border: none
				}
			</style>
		</EasyTag:override>
		<EasyTag:override name="content">
			<form action="" id="searchForm" name="searchForm" class="form-inline" style="width: 800px">
				<input class="hidden" id="dept_id" name="dept_id" value="">

				<div class="row">
					<div style="background-color: #fff; margin-left: 15px; width: 200px; float: left; height: 480px">
						<div style="height: 52px; line-height: 52px; padding: 0px 15px; border-bottom: 1px solid #eee;">
							<span class="glyphicon glyphicon-home"></span> 部门
						</div>
						<div class="ztree" id="ztree" data-mars="Dept.getDeptZtree"
							data-setting="{check: {enable: ${enable }},callback: {onClick:dept.onClick,onDblClick :dept.onDblClick}}"
							style="max-height: 480px; overflow: hidden; padding: 10px; width: auto; height: 480px;">
						</div>
					</div>
					<div style="height: 450px; width: 530px; float: left; margin-left: 15px;">
						<div class="ibox ">
							<div class="ibox-title clearfix">
								<div class="form-group">
									<!--  <h5><span class="glyphicon glyphicon-list"></span> 班级人员</h5> -->
									<div class="input-group input-group-sm">
										<span class="input-group-addon">员工账号/名称</span> <input type="text"
											name="userInfo" id="" class="form-control input-sm" style="width: 90px">
									</div>
									<div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="searchData()">
											<span class="glyphicon glyphicon-search"></span> 查询
										</button>
									</div>
								</div>
							</div>
							<div class="ibox-content">
								<table class="table table-auto table-bordered table-hover table-condensed"
									style="margin-bottom: 0; width: 500px;">
									<thead>
										<tr>
											<th width="50">序号</th>
											<th width="150">员工账号</th>
											<th width="150">员工名称</th>
										</tr>
									</thead>
								</table>
								<div id="userList" style="height: 340px; overflow-y: scroll; padding: 0; width: 510px">
									<table class="table table-auto table-bordered table-hover table-condensed"
										id="tableHead" data-mars="user.getUser" style="margin-bottom: 0; width: 500px;">
										<tbody id="dataList">
										</tbody>
									</table>
								</div>
								<script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr onclick="save('{{:USER_ACCT}}','{{:USERNAME}}')">
											<div >
											<td width="50">{{:#index+1}}</td>
											<td width="150">{{:USER_ACCT}}</td> 
											<td width="150">{{:USERNAME}}</td>                                         
											<div>                                      
									    </tr>
								    {{/for}}					         
							 </script>

							</div>
						</div>
					</div>

					<script id="labelList-template" type="text/x-jsrender">

			</script>
				</div>
			</form>
			<div class="layer-foot text-c">
				<button class="btn btn-sm btn-default ml-20" type="button" id="backbut"
					onclick="popup.layerClose()">关闭</button>
			</div>
		</EasyTag:override>
		<EasyTag:override name="script">
			<script type="text/javascript" src="/easitline-static/lib/jquery/jquery.slimscroll.min.js"></script>
			<script type="text/javascript">
				jQuery.namespace("dept");
				$(function () {
					$("#selectUser").slimScroll({ color: '#aaa', height: 380 });
					$("#userList").slimScroll({ color: '#aaa', height: 340 });
					requreLib.setplugs('slimscroll,ztree', function () {
						$('#ztree').slimScroll({
							height: '430px',
							color: '#ddd'
						});
					});
					var userIds = "";
					var allowDept = "";//不为空则拥有所有权限
					$('#searchForm').render({
						success: function () {
							var treeObj = $.fn.zTree.getZTreeObj("ztree");
							var nodes = treeObj.getNodes();
							if ('${expandAll}' == "true") {//展示所以数据
								$.fn.zTree.getZTreeObj("ztree").expandAll(true);
							} else {
								if (nodes.length > 0) {
									for (var i = 0; i < nodes.length; i++) {
										treeObj.expandNode(nodes[i], true, false, false);
									}
								}
							}
						}
					});
				});
				function searchData() {
					if ('${enable }' == 'true') {//多选
						var selectid = "";
						treeObj = $.fn.zTree.getZTreeObj("ztree");
						nodes = treeObj.getCheckedNodes(true);
						for (var i = 0; i < nodes.length; i++) {
							if (nodes[i].id != -1)
								if (nodes[i].children && nodes[i].children.length <= 0) {//支取最后分支
									selectid = (selectid == "" ? nodes[i].id : selectid + "," + nodes[i].id);
								}
						}
						$("#dept_id").val(selectid);
					} else {
						var selectedNodes = $.fn.zTree.getZTreeObj("ztree").getSelectedNodes();
						if (selectedNodes == "" || selectedNodes[0].id == '-1')
							$("#dept_id").val('');
						else
							$("#dept_id").val(selectedNodes[0].id);
					}
					$("#searchForm").searchData();
				}
				dept.onClick = function (e, treeId, treeNode) {
					var zTree = $.fn.zTree.getZTreeObj("ztree");
					zTree.expandNode(treeNode);
				}
				function save(selectid, selectName) {
					window.parent.common.setUser1(selectid, selectName);
					popup.layerClose();
				}
				dept.onDblClick = function () {
					if ('${enable }' == 'true') {//多选

					} else {
						searchData();
					}
				}

			</script>
		</EasyTag:override>
		<%@ include file="/pages/common/layout_list.jsp" %>