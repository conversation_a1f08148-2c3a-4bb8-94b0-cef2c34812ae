<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>

<EasyTag:override name="head">
    <title>人员选择</title>
    <style>
        .ztree .line {
            border: none
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form action="" id="searchForm" name="searchForm" class="form-inline">
        <input class="hidden" id="dept_id" name="dept_id" value="">
        <div class="row">
            <div style="background-color: #fff; margin-left: 15px; width: 200px; float: left; height: 480px">
                <div style="height: 52px; line-height: 52px; padding: 0px 15px; border-bottom: 1px solid #eee;">
                    <span class="glyphicon glyphicon-home"></span> 部门
                </div>
                <div class="ztree" id="ztree" data-mars="Dept.getDeptZtree"
                     data-setting="{check: {enable: ${enable }},callback: {onClick:dept.onClick,onCheck:dept.onClick}}"
                     style="max-height: 480px; overflow: hidden; padding: 10px; width: auto; height: 480px;">
                </div>
            </div>
            <div style="height: 450px; width: 530px; float: left; margin-left: 15px;">
                <div class="ibox ">
                    <div class="ibox-title clearfix">
                        <div class="form-group">
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon">员工账号/名称</span> <input type="text"
                                                                                            name="userInfo"
                                                                                            id="userInfo"
                                                                                            class="form-control input-sm"
                                                                                            style="width: 90px">
                            </div>
                            <div class="input-group input-group-sm">
                                <button type="button" class="btn btn-sm btn-default" onclick="searchData()">
                                    <span class="glyphicon glyphicon-search"></span> 查询
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <table class="table table-auto table-bordered table-hover table-condensed"
                               style="margin-bottom: 0; width: 500px;">
                            <thead>
                            <tr>
                                <th class="text-c" width="50">
                                    <input type="checkbox" name="checkAll" style="zoom:150%;" value="">
                                </th>
                                <th width="50">序号</th>
                                <th width="150">员工账号</th>
                                <th width="150">员工名称</th>
                            </tr>
                            </thead>
                        </table>
                        <div id="userList" style="height: 340px; overflow-y: scroll; padding: 0; width: 510px">
                            <table class="table table-auto table-bordered table-hover table-condensed"
                                   id="tableHead" data-mars="user.getUser" style="margin-bottom: 0; width: 500px;">
                                <tbody id="dataList">

                                </tbody>
                            </table>
                        </div>
                        <script id="list-template" type="text/x-jsrender">
                            {{for  list}}
                                 <tr>
                                     <div>
                                     <td width="50" class="text-c">{{CHECK:USER_ACCT USERNAME}}</td>
                                     <td width="50">{{:#index+1}}</td>
                                     <td width="150">{{:USER_ACCT}}</td>
                                     <td width="150">{{:USERNAME}}</td>
                                     <div>
                                 </tr>
                             {{/for}}
                        </script>
                    </div>
                </div>
            </div>
            <div style="background-color: #fff; margin-left: 15px; width: 200px; float: left; height: 480px">
                <div style="height: 52px; line-height: 52px; padding: 0px 15px; border-bottom: 1px solid #eee;">
                    <span class="glyphicon glyphicon-home"></span> 已选人员
                </div>
                <div data-template="labelList-template" id="labelTableHead">
                    <div class="clearfix labelListDiv">
                        <div data-container="#labelTableHead" data-template="labelList-template"
                             id="selectUser"></div>
                    </div>
                </div>
            </div>

            <script id="labelList-template" type="text/x-jsrender">

            </script>
        </div>
    </form>
    <div class="layer-foot text-c">
        <button class="btn btn-sm btn-primary" type="button" onclick="save()">确认</button>
        <button class="btn btn-sm btn-default ml-20" type="button" id="backbut"
                onclick="popup.layerClose()">关闭
        </button>
        <button class="btn btn-sm btn-default hidden" id="clean" type="button" id="backbut">刷新</button>
    </div>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/jquery/jquery.slimscroll.min.js"></script>
    <script type="text/javascript">
        jQuery.namespace("dept");
        $(function () {
            $('#userInfo').bind('keypress', function (event) {
                if (event.keyCode == 13) {
                    searchData();
                }

            });

            setOldIds();
            $("#selectUser").slimScroll({color: '#aaa', height: 380});
            $("#userList").slimScroll({color: '#aaa', height: 340});
            requreLib.setplugs('slimscroll,ztree', function () {
                $('#ztree').slimScroll({
                    height: '430px',
                    color: '#ddd'
                });

            });
            var userIds = "";
            var allowDept = "";//不为空则拥有所有权限
            $('#searchForm').render({
                success: function () {
                    var treeObj = $.fn.zTree.getZTreeObj("ztree");
                    var nodes = treeObj.getNodes();
                    if ('${expandAll}' == "true") {//展示所以数据
                        $.fn.zTree.getZTreeObj("ztree").expandAll(true);
                    } else if (nodes.length > 0) {
                        for (var i = 0; i < nodes.length; i++) {
                            treeObj.expandNode(nodes[i], true, false, false);
                        }
                    }
                }
            });
        });

        function searchData() {
            if ('${enable }' == 'true') {//多选
                var selectid = "";
                treeObj = $.fn.zTree.getZTreeObj("ztree");
                nodes = treeObj.getCheckedNodes(true);
                for (var i = 0; i < nodes.length; i++) {
                    if (nodes[i].id != -1)
                        if (nodes[i].children && nodes[i].children.length <= 0) {//支取最后分支
                            selectid = (selectid == "" ? nodes[i].id : selectid + "," + nodes[i].id);
                        }
                }
                $("#dept_id").val(selectid);
            } else {
                var selectedNodes = $.fn.zTree.getZTreeObj("ztree").getSelectedNodes();
                if (selectedNodes == "" || selectedNodes[0].id == '-1')
                    $("#dept_id").val('');
                else
                    $("#dept_id").val(selectedNodes[0].id);
            }
            $("#searchForm").searchData();
        }

        $.views.converters("CHECK", function (id, name) {
            if (selectUser[id] != null) {
                return '<input onclick="update(this)"  type="checkbox"   style="zoom:150%;" checked="checked" data-id="' + id + '" data-name="' + name + '">';
            } else {
                return '<input onclick="update(this)" type="checkbox"  style="zoom:150%;" data-id="' + id + '" data-name="' + name + '">';
            }
        });
        $("input[name='checkAll']").click(function () {
            var ifChecked = $(this).prop("checked");
            $("#dataList input:checkbox").prop("checked", ifChecked);
            if (ifChecked) {//选中
                var ids = $("#dataList").find("input[type='checkbox']");
                for (var i = 0; i < ids.length; i++) {
                    selectUser[$(ids[i]).attr("data-id")] = $(ids[i]).attr("data-name");
                }
                selectUserlist();
            } else {
                var ids = $("#dataList").find("input[type='checkbox']");
                for (var i = 0; i < ids.length; i++) {
                    delete selectUser[$(ids[i]).attr("data-id")];
                }
                selectUserlist();
            }
        })
        dept.onClick = function (e, treeId, treeNode) {
            var zTree = $.fn.zTree.getZTreeObj("ztree");
            zTree.expandNode(treeNode);
            searchData();

        }

        var selectUser = {};//旧的值
        function update(data) {
            if (data.checked) {//选中
                selectUser[data.getAttribute('data-id')] = data.getAttribute('data-name');
            } else {
                delete selectUser[data.getAttribute('data-id')];
            }
            selectUserlist();
        }

        function selectUserlist() {
            var content = "";
            for (var key in selectUser) {
                content = content + add(selectUser[key]);
            }
            $("#selectUser").html(content);
        }

        function add(name) {
            var content = '<div class="clearfix labelListDiv">' +
                '<div class="pull-left">' + name + '</div>' +
                '</div>';
            return content;
        }

        function save() {
            var selectid = "";
            var selectName = "";
            var telcount = 0;
            for (var key in selectUser) {
                selectName = (selectName == "" ? selectUser[key] + "(" + key + ")" : selectName + "," + selectUser[key] + "(" + key + ")");
                selectid = (selectid == "" ? key : selectid + "," + key);
                telcount = telcount + 1;
            }
            console.log(telcount);
            window.parent.common.setUser(selectid, selectName, telcount);
            popup.layerClose();
        }

        function setOldIds() {//加载已选中人
            var ids = '${oldIds}';
            var oldIds = '${oldIds}'.split(',');
            if (window.parent.olduser != null && window.parent.olduser != "") {
                ids = window.parent.olduser;
                oldIds = window.parent.olduser.split(',');
            }
            ajax.remoteCall("${ctxPath}/servlet/User?action=getUser", {ids: ids}, function (result) {
                var data = result.data;
                for (var key in data) {
                    selectUser[key] = data[key]
                }
                selectUserlist();
            });
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>