<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>测试</title>
	<style>
	    .labelListDiv{height: 32px;line-height: 32px;padding: 0px 10px;font-size: 13px;}
		.labelListDiv.active{background-color:#f8f8f8}
		.labelListDiv a{text-decoration: none;color: #666;}
		.ibox-content table tr td label+label{margin-left:10px}
	    .ztree .line{border:none}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="testForm" data-mars="" data-pk="" method="post"
		autocomplete="off" data-mars-prefix="redBlock.">
		<input name="redBlock.SOURCE" class="form-control input-sm" type="hidden" value="1">
		<input name="sequenceNo" class="form-control input-sm" type="hidden" value="${sequenceNo }">
						ID<input  id="depart_id" data-rules="required|digits"  name="redBlock.USER_NO" class="form-control input-sm" type="text">
						值<input  id="depart_text" data-rules="required|digits"  name="redBlock.USER_NO" class="form-control input-sm" type="text">
						<button id="depart_false_false" type="button" class="btn btn-sm btn-success " onclick="common.depart(false,false,false)">
							<i class="glyphicon glyphicon-export"></i> 部门选择（单选）（不展开）
						</button>
						<button id="depart_false_true" type="button" class="btn btn-sm btn-success " onclick="common.depart(false,true,false)">
							<i class="glyphicon glyphicon-export"></i> 部门选择（单选）（全部展开）
						</button>
							<button id="depart_false_true_single" type="button" class="btn btn-sm btn-success " onclick="common.depart(false,true,true)">
							<i class="glyphicon glyphicon-export"></i> 部门选择（单选）（全部展开）（选中判断是否最低级只适用单选）
						</button>
						<br>
						编号<input  id="departs_id" data-rules="required|digits"  name="redBlock.USER_NO" class="form-control input-sm" type="text">
						值<input  id="departs_text" data-rules="required|digits"  name="redBlock.USER_NO" class="form-control input-sm" type="text">
						<button id="depart_true" type="button" class="btn btn-sm btn-success " onclick="common.depart(true,false,false)">
							<i class="glyphicon glyphicon-export"></i> 部门选择（多选）（不展开）
						</button>
						<button id="depart_true" type="button" class="btn btn-sm btn-success " onclick="common.depart(true,true,false)">
							<i class="glyphicon glyphicon-export"></i> 部门选择（多选）（全部展开）
						</button>
						<br>
						ID<input  id="role_id" data-rules="required|digits"  name="redBlock.USER_NO" class="form-control input-sm" type="text">
						值<input  id="role_text" data-rules="required|digits"  name="redBlock.USER_NO" class="form-control input-sm" type="text">
						<button id="role_false_false" type="button" class="btn btn-sm btn-success " onclick="common.role(false,false,false)">
							<i class="glyphicon glyphicon-export"></i> 角色选择（单选）（不展开）
						</button>
						<button id="role_false_true" type="button" class="btn btn-sm btn-success " onclick="common.role(false,true,false)">
							<i class="glyphicon glyphicon-export"></i> 角色选择（单选）（全部展开）
						</button>
							<button id="role_false_true_single" type="button" class="btn btn-sm btn-success " onclick="common.role(false,true,true)">
							<i class="glyphicon glyphicon-export"></i> 角色选择（单选）（全部展开）（选中判断是否最低级只适用单选）
						</button>
						<br>
						编号<input  id="roles_id" data-rules="required|digits"  name="redBlock.USER_NO" class="form-control input-sm" type="text">
						值<input  id="roles_text" data-rules="required|digits"  name="redBlock.USER_NO" class="form-control input-sm" type="text">
						<button id="role_true" type="button" class="btn btn-sm btn-success " onclick="common.role(true,false,false)">
							<i class="glyphicon glyphicon-export"></i> 角色选择（多选）（不展开）
						</button>
						<button id="role_true" type="button" class="btn btn-sm btn-success " onclick="common.role(true,true,false)">
							<i class="glyphicon glyphicon-export"></i> 角色选择（多选）（全部展开）
						</button>
						<br>
						编号<input  id="user_id" data-rules="required|digits"  name="redBlock.USER_NO" class="form-control input-sm" type="text">
						值<input  id="user_text" data-rules="required|digits"  name="redBlock.USER_NO" class="form-control input-sm" type="text">
						<button id="user_false_false" type="button" class="btn btn-sm btn-success " onclick="common.user(false,false,false)">
							<i class="glyphicon glyphicon-export"></i> 用户选择（部门单选）
						</button>
							<button id="user_false_false" type="button" class="btn btn-sm btn-success " onclick="common.user(true,false,false)">
							<i class="glyphicon glyphicon-export"></i> 用户选择（部门多选选）
						</button>
						<button id="user_false_false" type="button" class="btn btn-sm btn-success " onclick="common.userSingle(false,false,false,true)">
							<i class="glyphicon glyphicon-export"></i> 用户选择（部门单选,人员单选）
						</button>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	jQuery.namespace("common");//common不能更改
	common.depart = function(enable,expandAll,single) {//是否多选,是否全部展开,是否判断最低一级（适用单选）
		var oldIds="";//默认选中的值
	if($("#departs_id").val()!=""){
		oldIds=$("#departs_id").val();
	}
	popup.layerShow({
		type : 2,
		title : "部门选择",
		offset : '20px',
		area : [ '400px', '510px' ]
	}, "${ctxPath}/servlet/Dept?action=depart", {enable:enable,expandAll:expandAll,single:single,oldIds:oldIds});
}
	common.role = function(enable,expandAll,single) {//是否多选,是否全部展开,是否判断最低一级（适用单选）
		var oldIds="";
	if($("#roles_id").val()!=""){
		oldIds=$("#roles_id").val();
	}
	popup.layerShow({
		type : 2,
		title : "角色选择",
		offset : '20px',
		area : [ '400px', '510px' ]
	}, "${ctxPath}/servlet/Role?action=role", {enable:enable,expandAll:expandAll,single:single,oldIds:oldIds});
}
	common.user = function(enable,expandAll,single,userSingle) {//是否多选,是否全部展开,是否判断最低一级（适用单选）
	var oldIds="";
	var oldName="";
	if($("#user_id").val()!=""){
		oldIds=$("#user_id").val();
		oldName=$("#user_text").val();
	}
	popup.layerShow({
		type : 2,
		title : "用户选择",
		offset : '20px',
		area : [ '1000px', '600px' ]
	}, "${ctxPath}/servlet/User?action=user", {enable:enable,expandAll:expandAll,single:single,oldIds:oldIds,oldName:oldName,userSingle:userSingle});
}
	common.userSingle = function(enable,expandAll,single,userSingle) {//是否多选,是否全部展开,是否判断最低一级（适用单选）
		var oldIds="";
		var oldName="";
		if($("#user_id").val()!=""){
			oldIds=$("#user_id").val();
			oldName=$("#user_text").val();
		}
		popup.layerShow({
			type : 2,
			title : "用户选择",
			offset : '20px',
			area : [ '790px', '600px' ]
		}, "${ctxPath}/servlet/User?action=user", {enable:enable,expandAll:expandAll,single:single,oldIds:oldIds,oldName:oldName,userSingle:userSingle});
	}
	
	
		common.setDepart = function(acc,name){//接收单选
			$("#depart_id").val(acc);
			$("#depart_text").val(name);

		}	
		common.setDeparts = function(acc,name){//接收多选
			$("#departs_id").val(acc);
			$("#departs_text").val(name);
		}	
		common.setRole = function(acc,name){//接收单选
			$("#role_id").val(acc);
			$("#role_text").val(name);

		}	
		common.setRoles = function(acc,name){//接收多选
			$("#roles_id").val(acc);
			$("#roles_text").val(name);
		}	
		common.setUser = function(acc,name){//接收用户信息
			$("#user_id").val(acc);
			$("#user_text").val(name);
		}	
		
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>