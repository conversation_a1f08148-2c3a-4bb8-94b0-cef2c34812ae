<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>选择部门</title>
	<style>
.labelListDiv {
	height: 32px;
	line-height: 32px;
	padding: 0px 10px;
	font-size: 13px;
}

.labelListDiv.active {
	background-color: #f8f8f8
}

.labelListDiv a {
	text-decoration: none;
	color: #666;
}

.ibox-content table tr td label+label {
	margin-left: 10px
}

.ztree .line {
	border: none
}
</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form action="" id="searchForm" name="searchForm" class="form-inline">
		<div class="row">
			<div
				style="height: 52px; line-height: 52px; padding: 0px 15px; border-bottom: 1px solid #eee;">
				<span class="glyphicon glyphicon-home"></span> 选择部门
			</div>
			<div class="slimScrollDiv"
				style="position: relative; overflow: hidden; width: auto; height: 330px;">
				<div class="ztree" id="ztree" data-mars="Role.getRoleZtree"
					data-setting="{check: {enable: ${enable }},callback: {onClick:dept.onClick,onDblClick :dept.onDblClick}}"
					style="max-height: 330px; overflow: hidden; padding: 10px; width: auto; height: 330px;">
				</div>
			</div>
			<div class="layer-foot text-c">
				<button class="btn btn-sm btn-primary" type="button"
					onclick="dept.save()">保存</button>
				<button class="btn btn-sm btn-default ml-20" type="button"
					id="backbut" onclick="popup.layerClose();">关闭</button>
			</div>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	jQuery.namespace("dept");
		$(function() {
			requreLib.setplugs('slimscroll,ztree', function() {
				$('#ztree').slimScroll({});
			});
			$('#searchForm').render({
				success : function() {
					var treeObj = $.fn.zTree.getZTreeObj("ztree");
					var nodes = treeObj.getNodes();
					var oldIds='${oldIds}';
					if(oldIds!=""&&'${enable}'=="true"){//多选默认选择之前的选项
						var vals = oldIds.split(",");
						for (var i = 0; i < vals.length; i++) {
							var treenode = treeObj.getNodeByParam("id", vals[i], null);
							treeObj.expandNode(treenode, true, true, true);
							treeObj.checkNode(treenode, true, true, true);
							treeObj.selectNode(treenode);
						}
					}
					if('${expandAll}'=="true"){//展示所以数据
						$.fn.zTree.getZTreeObj("ztree").expandAll(true);
					}else{
						if (nodes.length > 0) {
							for (var i = 0; i < nodes.length; i++) {
								treeObj.expandNode(nodes[i], true, false, false);
							}
						}
					}
				}
			});
		});
		dept.onClick= function(e,treeId, treeNode) {
			var zTree = $.fn.zTree.getZTreeObj("ztree");
			zTree.expandNode(treeNode);
			}
		dept.onDblClick= function() {
			if('${enable }'=='true'){//多选
			}else{
			dept.save();
			}
		}
		 dept.save = function(){
				if('${enable }'=='true'){//多选
					var selectname="" ;
					var selectid="";
					treeObj = $.fn.zTree.getZTreeObj("ztree");
					nodes = treeObj.getCheckedNodes(true);
					for (var i = 0; i < nodes.length; i++) {
						if(nodes[i].children ==null){
							selectid=(selectid==""?nodes[i].id:selectid+","+nodes[i].id);
							selectname=(selectname==""?nodes[i].name:selectname+","+nodes[i].name);
						}
					}
					window.parent.common.setRoles(selectid,selectname);
					popup.layerClose();
				}else{//单选
					var zTreeObj = $.fn.zTree.getZTreeObj("ztree");
					var selectedNodes = zTreeObj.getSelectedNodes();
					if('${single}'=='true'){//验证是否最低级
						if(selectedNodes[0].children.length>0){
							alert("选择无效")
							return;
						}
					}
					window.parent.common.setRole(selectedNodes[0].id,selectedNodes[0].name);
					popup.layerClose();
				}
		 }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>