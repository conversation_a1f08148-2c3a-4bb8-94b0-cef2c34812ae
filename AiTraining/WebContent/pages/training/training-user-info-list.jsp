<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>培训用户列表</title>
    <style>
        .red-text {
            color: red;
        }

        #prodMenuContent {
            display: none;
            position: absolute;
            border: 1px solid rgb(170, 170, 170);
            max-width: 220px;
            max-height: 350px;
            z-index: 10;
            overflow: auto;
            background-color: #f4f4f4
        }

        #serTypeMenuContent {
            display: none;
            position: absolute;
            border: 1px solid rgb(170, 170, 170);
            max-width: 220px;
            max-height: 350px;
            z-index: 10;
            overflow: auto;
            background-color: #f4f4f4
        }

        #areaMenuContent {
            display: none;
            position: absolute;
            border: 1px solid rgb(170, 170, 170);
            max-width: 220px;
            max-height: 350px;
            z-index: 10;
            overflow: auto;
            background-color: #f4f4f4
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form action="" method="post" name="trainingUserInfoFrom" class="form-inline" id="trainingUserInfoFrom"
          onsubmit="return false"
          autocomplete="off">
        <div class="ibox">
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <h5><span class="glyphicon glyphicon-list"></span> 培训用户列表</h5>
                </div>
                <hr style="margin:5px -15px">
            </div>
            <div class="ibox-content table-responsive">
                <div class="row table-responsive">
                    <table class="table table-auto table-bordered table-hover table-condensed"
                           id="trainingUserInfo_table"  data-task-id=${param.taskId}>
                        <thead>
                        <tr>
                            <th>陪练人名称</th>
                            <th>陪练人账号</th>
                        </tr>
                        </thead>
                        <tbody id="dataList">

                        </tbody>
                        <script id="list-template" type="text/x-jsrender">
                            {{for list}}
                                 <tr class="tr_trainingUserInfo">
                                     <td class="td_TRAINING_USER_NAME">{{:TRAINING_USER_NAME}}</td>
                                     <td class="td_TRAINING_USER_ID">{{:TRAINING_USER_ID}}</td>
                                 </tr>
                            {{/for}}
                        </script>
                    </table>
                </div>
                <div class="row paginate" id="page">
                    <jsp:include page="/pages/common/pagination.jsp">
                        <jsp:param value="10" name="pageSize"/>
                    </jsp:include>
                </div>
            </div>
        </div>
    </form>
    <div class="text-c mb-5 mt-5" style="margin-right:auto;margin-left:auto;">
        <button type="button" class="btn btn-default btn-sm" onclick="trainingUserInfo.close()">关闭</button>
    </div>
</EasyTag:override>

<EasyTag:override name="script">
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css"/>
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/select2/css/select2-bootstrap.min.css"/>
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/select2/css/select2.min.css"/>
    <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/select2/js/select2.min.js"></script>
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css"/>
    <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
    <link type="text/css" rel="stylesheet"
          href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css"/>
    <script type="text/javascript"
            src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
    <script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>

    <script type="text/javascript">
        jQuery.namespace("trainingUserInfo");
        requreLib.setplugs("wdate")
        $(document).ready(function () {
            trainingUserInfo.search();
            trainingUserInfo.init();
        });
        $(function () {
            trainingUserInfo.multiSetting = {
                buttonWidth: '220px',
                allSelectedText: "全部",
                nonSelectedText: "--请选择--",
                nSelectedText: "个被选中",
                selectAllNumber: false,
                maxHeight: 350,
                includeSelectAllOption: true,
                selectAllText: '全选',
                enableFiltering: true
            };
            trainingUserInfo.multiSetting1 = {
                buttonWidth: '220px',
                allSelectedText: "全部",
                nonSelectedText: "--请选择--",
                nSelectedText: "个被选中",
                selectAllNumber: false,
                maxHeight: 350,
                includeSelectAllOption: true,
                selectAllText: '全选',
                enableFiltering: true
            };
        })
        trainingUserInfo.taskId = '${param.taskId}'
        trainingUserInfo.oldAreaNum = '';
        trainingUserInfo.pid = '';//父级id
        trainingUserInfo.init = function () {
            console.log("test");
            $("#trainingUserInfo_table").attr("data-mars", "trainingUser.getUserInfoByTaskId");
            trainingUserInfo.flag = false;
        }
        trainingUserInfo.search = function () {
            $("#trainingUserInfo_table").attr("data-mars", "trainingUser.getUserInfoByTaskId");
            trainingUserInfo.flag = false;
            console.log($("#trainingUserInfoFrom"));
            $("#trainingUserInfoFrom").searchData();
        }

        trainingUserInfo.close = function () {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>