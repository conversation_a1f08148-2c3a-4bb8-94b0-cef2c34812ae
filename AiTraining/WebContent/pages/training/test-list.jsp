<%@ page language="java" contentType="text/html;charset=UTF-8" %>
    <%@ include file="/pages/common/global.jsp" %>
        <EasyTag:override name="head">
            <title>试卷查询</title>
            <style>
                .red-text {
                    color: red;
                }

                #prodMenuContent {
                    display: none;
                    position: absolute;
                    border: 1px solid rgb(170, 170, 170);
                    max-width: 220px;
                    max-height: 350px;
                    z-index: 10;
                    overflow: auto;
                    background-color: #f4f4f4
                }

                #serTypeMenuContent {
                    display: none;
                    position: absolute;
                    border: 1px solid rgb(170, 170, 170);
                    max-width: 220px;
                    max-height: 350px;
                    z-index: 10;
                    overflow: auto;
                    background-color: #f4f4f4
                }

                #areaMenuContent {
                    display: none;
                    position: absolute;
                    border: 1px solid rgb(170, 170, 170);
                    max-width: 220px;
                    max-height: 350px;
                    z-index: 10;
                    overflow: auto;
                    background-color: #f4f4f4
                }
            </style>
        </EasyTag:override>
        <EasyTag:override name="content">
            <form action="" method="post" name="testFrom" class="form-inline" id="testFrom" onsubmit="return false"
                autocomplete="off">
                <div class="ibox">
                    <div class="ibox-title clearfix">
                        <div class="form-group">
                            <h5><span class="glyphicon glyphicon-list"></span> 试卷查询</h5>
                        </div>
                        <hr style="margin:5px -15px">
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-addon">测试名称</span>
                                <input type="text" name="examPaperName" id="examPaperName" class="form-control input-sm"
                                    style="width:110px">
                            </div>
                            <div class="input-group">
                                <span class="input-group-addon">创建人</span>
                                <input type="text" name="createBy" id="createBy" class="hidden" style="">
                                <input placeholder="不限" type="text" name="createName" id="createName"
                                       readonly="readonly" class="form-control input-sm" style="">
                                <span class="input-group-addon" onclick="common.user()">
                                <i class="glyphicon glyphicon-search"></i></span>
                            </div>
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon width-30">创建日期</span>
                                <input type="text" name="startDate" id="startDate" class="form-control input-sm Wdate"
                                    onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',startDate:'%y-%M-%d 00:00:00',maxDate:'#F{$dp.$D(\'endDate\')}'})">
                                <span class="input-group-addon">-</span>
                                <input type="text" name="endDate" id="endDate" class="form-control input-sm Wdate"
                                    onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',endDate:'%y-%M-%d 23:59:59',minDate:'#F{$dp.$D(\'startDate\')}'})">
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="input-group input-group-sm pull-left mr-10">
                                <button type="button" class="btn btn-sm btn-success" onclick="test.reset()">
                                    重置</button>
                            </div>
                            <c:if test="${param.ok eq '1'}">
                                <div class="input-group input-group-sm pull-left" id="test_ok">
                                    <button type="button" class="btn btn-sm btn-success" onclick="test.sure()">
                                        确定</button>
                                </div>
                            </c:if>
                            <div class="input-group input-group-sm pull-right ml-10">
                                <button type="button" class="btn btn-sm btn-default" onclick="test.search()"><span
                                        class="glyphicon glyphicon-search"></span> 搜索</button>
                            </div>
                        </div>
                    </div>
                    <div class="ibox-content table-responsive">
                        <div class="row table-responsive">
                            <table class="table table-auto table-bordered table-hover table-condensed" id="test_table">
                                <thead>
                                    <tr>
                                        <th>选择</th>
                                        <th>测试ID</th>
                                        <th>测试名称</th>
                                        <th>及格分数</th>
                                        <th>创建人</th>
                                        <th>创建时间</th>
                                        <th>测验生成方式</th>
                                    </tr>
                                </thead>
                                <tbody id="dataList">

                                </tbody>
                                <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr class="tr_test">
											<td>
												<label class="radio radio-success">
													<input type="radio" name="test_radio" data-examDuration="{{:examDuration}}" ">
													<span></span>
												</label>
											</td>
											<td class="td_examPaperId">{{:examPaperId}}</td>
											<td class="td_examPaperName">{{:examPaperName}}</td>
											<td class="td_passingScore">{{:passingScore}}</td>
											<td class="td_createBy">{{:createBy}}</td>
											<td class="td_createTime">{{:createTime}}</td>
											<td class="td_examPaperGenerateType">{{:examPaperGenerateType}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
                            </table>
                        </div>
                        <div class="row paginate" id="page">
                            <jsp:include page="/pages/common/pagination.jsp">
                                <jsp:param value="10" name="pageSize" />
                            </jsp:include>
                        </div>
                    </div>
                </div>
            </form>
            <div class="text-c mb-5 mt-5" style="margin-right:auto;margin-left:auto;">
                <button type="button" class="btn btn-default btn-sm" onclick="test.close()">关闭</button>
            </div>
        </EasyTag:override>

        <EasyTag:override name="script">
            <link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css" />
            <link type="text/css" rel="stylesheet" href="/easitline-static/lib/select2/css/select2-bootstrap.min.css" />
            <link type="text/css" rel="stylesheet" href="/easitline-static/lib/select2/css/select2.min.css" />
            <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
            <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
            <script type="text/javascript" src="/easitline-static/lib/select2/js/select2.min.js"></script>
            <link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css" />
            <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
            <link type="text/css" rel="stylesheet"
                href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css" />
            <script type="text/javascript"
                src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
            <script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>

            <script type="text/javascript">
                jQuery.namespace("test");
                requreLib.setplugs("wdate")
                $(document).ready(function () {
                });
                $(function () {
                    var startDate = getTodayDate(-7);
                    var endDate = getTodayDate();
                    $("#startDate").val(startDate + " 00:00:00");
                    $("#endDate").val(endDate + " 23:59:59");
                    test.multiSetting = {
                        buttonWidth: '220px',
                        allSelectedText: "全部",
                        nonSelectedText: "--请选择--",
                        nSelectedText: "个被选中",
                        selectAllNumber: false,
                        maxHeight: 350,
                        includeSelectAllOption: true,
                        selectAllText: '全选',
                        enableFiltering: true
                    };
                    test.multiSetting1 = {
                        buttonWidth: '220px',
                        allSelectedText: "全部",
                        nonSelectedText: "--请选择--",
                        nSelectedText: "个被选中",
                        selectAllNumber: false,
                        maxHeight: 350,
                        includeSelectAllOption: true,
                        selectAllText: '全选',
                        enableFiltering: true
                    };
                })
                test.flag = true;
                test.examPaperId = '${param.examPaperId}'
                test.examPaperName = '${param.examPaperName}'
                test.passingScore = '${param.passingScore}';
                test.createBy = '${param.createBy}';
                test.createTime = '${param.createTime}';
                test.examPaperGenerateType = '${param.examPaperGenerateType}';
                test.examDuration = '${param.examDuration}';
                test.oldAreaNum = '';
                test.pid = '';//父级id
                test.opType = '${param.type}';//打开类型 type==1 为接入单 默认维修
                test.init = function () {
                    $("#proCodeTreeHidden").val('');
                    $("#serviceTreeHidden").val('');
                    $("#test_areaName").blur(function () {
                        if ($("#test_areaName").val() == '') {
                            $("#test_areaCode").val('');
                        }
                    });
                    $("#test_proCodeShow").blur(function () {
                        if ($("#test_proCodeShow").val() == '') {
                            $("#test_proCode").val('');
                        }
                    });
                    $("#test_serviceTypeShow").blur(function () {
                        if ($("#test_serviceTypeShow").val() == '') {
                            $("#test_serviceType").val('');
                        }
                    });
                }
                jQuery.namespace("common");
                common.user = function () {
                    var oldIds = "";
                    var oldName = "";
                    if ($("#createBy").val() != "") {
                        oldIds = $("#createBy").val();
                        oldName = $("#createName").val();
                    }
                    popup.layerShow({
                        type: 2,
                        title: "用户选择",
                        offset: '20px',
                        area: ['800px', '650px']
                    }, "${ctxPath}/servlet/User?action=user", {enable: true, oldIds: "", oldName: '', userSingle: 'true'});
                }
                common.setUser = function (acc, name) {//接收用户信息
                    $("#createBy").val(acc);
                    $("#createName").val(name);
                }
                test.search = function () {
                    if ($("#examPaperName").val() === "" && $("#createBy").val() === "" && ($("#startDate").val() === "" || $("#endDate").val() === "")) {
                        alert("缺少搜索条件：试卷名称，创建人和创建时间，三选一必填");
                        return;
                    }
                    if (test.checkCondition()) {
                        if (test.flag) {
                            $("#test_table").attr("data-mars", "trainingRecord.getExamList");
                            test.flag = false;
                        }
                        $("#testFrom").searchData();
                    }
                }
                /*重置*/
                test.reset = function () {
                    $("#examPaperName").val('');
                    $("#createBy").val('');
                    $("#createName").val('');
                    $("#startDate").val('');
                    $("#endDate").val('');
                }

                test.sure = function () {
                    var checkedProcess = $("#test_table #dataList").find("input[type='radio']:checked");
                    if (checkedProcess.length < 1) {
                        alert('请先选择条记录！');
                        return;
                    } else {
                        var data = test.getTableData(checkedProcess);
                        if (test.examPaperId) {
                            parent.$("#" + test.examPaperId).val(data.examPaperId);
                        }
                        if (test.examPaperName) {
                            parent.$("#" + test.examPaperName).val(data.examPaperName);
                            parent.$("#" + test.examPaperName).change();
                        }
                        if (test.passingScore) {
                            parent.$("#" + test.passingScore).val(data.passingScore);
                            parent.$("#" + test.passingScore).change();
                        }
                        if (test.createBy) {
                            parent.$("#" + test.createBy).val(data.createBy);
                        }
                        if (test.createTime) {
                            parent.$("#" + test.createTime).val(data.createTime);
                        }
                        if (test.examPaperGenerateType) {
                            parent.$("#" + test.examPaperGenerateType).val(data.examPaperGenerateType);
                        }
                        if (test.examDuration) {
                            parent.$("#" + test.examDuration).val(data.examDuration);
                            parent.$("#" + test.examDuration).change();
                        }
                        test.close();
                    }
                }

                test.getTableData = function (checkedProcess) {
                    var data = {};
                    data.examDuration = $(checkedProcess).attr("data-examDuration");
                    var parentTr = $(checkedProcess).parents(".tr_test");
                    data.examPaperId = $(parentTr).find(".td_examPaperId").html();
                    data.examPaperName = $(parentTr).find(".td_examPaperName").html();
                    data.passingScore = $(parentTr).find(".td_passingScore").html();
                    data.createBy = $(parentTr).find(".td_createBy").html();
                    data.createTime = $(parentTr).find(".td_createTime").html();
                    data.examPaperGenerateType = $(parentTr).find(".td_examPaperGenerateType").html();
                    return data;
                }

                test.changeOrg = function (obj) {
                    $("#test_proCode").val('');
                    $("#test_proCodeShow").val('');
                    test.prodCodeRender(obj);
                    test.serviceTreeRender(obj);

                }

                test.checkCondition = function () {
                    var start = $("#startTime").val();
                    var end = $("#endTime").val();
                    if (start != "" && end != "" && start > end) {
                        layer.msg("开始时间应小于结束时间", { icon: 5 });
                        var time = end.substring(0, 10) + start.substring(10);
                        $("#startTime").val(time);
                        return false;
                    }
                    return true;
                }

                function maxTime(e) {
                    var start = $("#startTime").val();
                    var end = $("#endTime").val();
                    if (start != "" && end != "" && start > end) {
                        layer.msg("开始时间应小于结束时间", { icon: 5 });
                        var time = end.substring(0, 10) + start.substring(10);
                        $("#startTime").val(time);
                    }
                }

                test.close = function () {
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                }
            </script>
        </EasyTag:override>
        <%@ include file="/pages/common/layout_list.jsp" %>