<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>

<EasyTag:override name="head">
    <title>名单导入</title>
    <style>
        .ztree .line {
            border: none
        }
    </style>
</EasyTag:override>

<EasyTag:override name="content">
    <form action="" id="importForm" method="post" name="uploadForm" class="form-inline" id="uploadForm"
          enctype="multipart/form-data">
        <table class="table  table-vzebra mt-10">
            <tbody>
            <tr>
                <td width="60px">Excel文件</td>
                <td><input class="hidden" type="file" id="file" name=file
                           accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel">
                    <button class="btn btn-xs btn-info" type="button"
                            onclick="$('#file').click()">选择文件
                    </button>
                    <a
                            class="btn btn-sm btn-link" href="JavaScript:exc.download()">下载导入模板</a>
                </td>
            </tr>
            </tbody>
        </table>
        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary" type="button"
                    onclick="exc.upload()">保存
            </button>
            <button class="btn btn-sm btn-default ml-20" type="button"
                    id="backbut" onclick="exc.close()">关闭
            </button>
        </div>
    </form>

</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript">
        jQuery.namespace("exc");
        $(function () {
            if (window !== window.parent) {
                console.log("当前页面有父窗口");
            } else {
                console.log("当前页面是顶层窗口，没有父窗口");
            }
            try {
                var common = window.parent.common;
                if (common) {
                    console.log("common对象存在");
                } else {
                    console.log("common对象不存在");
                }
            } catch (e) {
                console.error("无法访问window.parent.common", e);
            }
            if (window.parent.common && typeof window.parent.common.setUser === 'function') {
                console.log("setUser方法存在并且是函数");
            } else {
                console.log("setUser方法不存在或不是函数");
            }
        });
        exc.close = function () {
            popup.layerClose();
        }
        exc.download = function () {
            location.href = "${ctxPath}/training/record?action=download";
        }
        exc.upload = function () {
            if ($("#file").val() != "") {
                $("#importForm").attr("enctype", "multipart/form-data");
                var formData = new FormData($("#importForm")[0]);
                var index = layer.load(1, {
                    shade: [0.1, '#fff'] //0.1透明度的白色背景
                });
                $.ajax({
                    url: '${ctxPath}/training/record?action=upload',
                    type: 'POST',
                    data: formData,
                    async: true, cache: false, contentType: false, processData: false,
                    success: function (result) {
                        $("#importForm").removeAttr("enctype");
                        if (result.state === 1) {
                            window.parent.common.setUser(result.data.userAcc, result.data.userName, result.data.telcount);
                            exc.close();
                        } else {
                            exc.close();
                            top.layer.msg(result.msg,
                            {
                                icon: 2,
                                time: 2000,
                                shade: 0.3, // 遮罩层透明度
                                offset: '100px' // 提示信息距离顶部的偏移量
                            });
                        }
                    }
                });
            } else {
                alert('请上传文件');
            }
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>