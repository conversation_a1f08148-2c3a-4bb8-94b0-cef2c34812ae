<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>发布</title>

    <style>
        /* <!--
        #menuContent1 {
            display: none;
            position: absolute;
            border: 1px solid rgb(170, 170, 170);
            max-width: 220px;
            max-height: 350px;
            z-index: 10;
            overflow: auto;
            background-color: #f4f4f4
        }

        .pagination pagination-sm pageNumV {
            float: right
        }

        .select2-selection__rendered {
            text-align: left;
        }

        .select2-container--bootstrap {
            width: 1300px;
            z-index: 100000000
        }

        .select2-container--bootstrap .select2-selection {
            font-size: 13px;
        }

        .select2-selection {
            background-color: #fff !important;
        }
        --> */
    </style>
</EasyTag:override>
<EasyTag:override name="content">

    <form id="publishForm" data-mars="" method="post" autocomplete="off" data-mars-prefix="">
        <table class="table  table-vzebra mt-10">
            <tbody>
            <tr>
                <td class="required">任务名称</td>
                <td><input id="taskName" name="taskName" data-rules="required"
                           class="form-control input-sm"></td>
                <td class="required">任务类型</td>
                <td>
                    <select name="taskType" id="taskType" data-rules="required" onchange=""
                            class="form-control input-sm">
                        <option value="">请选择</option>
                        <option value="STUDY">陪练</option>
                        <option value="EXAM">测验</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="required">测试名称</td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="text" name="examPaperGenerateType" id="examPaperGenerateType" class="hidden"
                               style="">
                        <input type="text" name="examPaperId" id="examPaperId" class="hidden" style="">
                        <input type="text" name="examPaperName" id="examPaperName" readonly="readonly"
                               class="form-control input-sm" style="" data-rules="required">
                        <span class="input-group-addon" onclick="publish.test()"><i
                                class="glyphicon glyphicon-search"></i></span>
                    </div>
                </td>
                <td id="referenceOptions1">是否展示参考答案</td>
                <td id="referenceOptions2">
                    <!-- 单选框替换 -->
                    <div>
                        <input type="radio" id="showReferenceYes" name="needReference" value="true" checked>
                        <label for="showReferenceYes">是</label>
                        &nbsp;&nbsp;
                        <input type="radio" id="showReferenceNo" name="needReference" value="false">
                        <label for="showReferenceNo">否</label>
                    </div>
                </td>
            </tr>
            <tr>
                <td>及格分数（分）</td>
                <td><input type="text" id="passingScore" data-rules="digits" name="passingScore"
                           class="form-control input-sm" readonly="readonly"></td>
                <td>考试时长（分钟）</td>
                <td><input type="text" id="examDuration" data-rules="digits" name="examDuration"
                           class="form-control input-sm" readonly="readonly"></td>
            </tr>
            <tr>
                <td class="required">任务开始时间</td>
                <td><input type="text" name="examPublishStartTime" id="examPublishStartTime" data-mars=""
                           class="form-control input-sm Wdate" value="" data-rules="required"
                           onclick="WdatePicker({maxDate:'#F{$dp.$D(\'examPublishEndTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                </td>
                <td class="required">任务结束时间</td>
                <td><input type="text" name="examPublishEndTime" id="examPublishEndTime" data-mars=""
                           class="form-control input-sm Wdate" value="" data-rules="required"
                           onclick="WdatePicker({minDate:'#F{$dp.$D(\'examPublishStartTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                </td>
            </tr>
            <tr>
                <td>提醒时间</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="reminderTime" value="1" checked>
                        提前15分钟</label>
                    <br>
                    <label class="radio-inline"><input type="radio" name="reminderTime" value="2">
                        提前30分钟</label>
                    <br>
                    <label class="radio-inline" id="customReminderLabel">
                        <input type="radio" name="reminderTime" value="custom" id="reminderTimeRadioCustom">
                        <input type="text" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})"
                               name="customReminderTime" id="reminderTimeInputCustom"
                               class="form-control input-sm Wdate">
                    </label>
                </td>
                <td class="required">答题模式</td>
                <td>
                    <select name="responseMode" id="responseMode" data-rules="required" onchange=""
                            class="form-control input-sm">
                        <option value="">请选择</option>
                        <option value="VOICE">语音</option>
                        <option value="TEXT">文本</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="required">陪练人员</td>
                <td colspan="3">
                    <div class="input-group input-group-sm">
                        <input type="text" name="user_acc" id="publish_user_acc" class="hidden" style="">
                        <textarea placeholder="不限" name="user_name" id="publish_user_name" rows="5"
                                  class="form-control input-sm" style="min-height: 120px !important;"
                                  data-rules="required"></textarea>
                        <span class="input-group-addon" onclick="common.importData()">
                                        <i class="glyphicon glyphicon-export"></i></span>
                        <span class="input-group-addon" onclick="common.user()">
                                        <i class="glyphicon glyphicon-search"></i></span>
                    </div>
                </td>
            </tr>
            <tr class="">
                <!-- 统计数 -->
                <th colspan="4">
                    <div>
                        <input type="number" name="telcount" id="telcount" data-rules="digits"
                               class="hidden">
                        <div style="float: left; padding-left: 10px">用户数：<span style="color: red" id="displayTelcount"></span></div>
                    </div>
                </th>
            </tr>
            </tbody>
        </table>

        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary" type="button" onclick="publish.ajaxSubmitForm()">确认</button>
            <button class="btn btn-sm btn-default ml-20" type="button" id="backbut"
                    onclick="layer.closeAll();">关闭
            </button>
        </div>

    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css"/>
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/select2/css/select2-bootstrap.min.css"/>
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/select2/css/select2.min.css"/>
    <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/select2/js/select2.min.js"></script>
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css"/>
    <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
    <link type="text/css" rel="stylesheet"
          href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css"/>
    <script type="text/javascript"
            src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>

    <script type="text/javascript">
        jQuery.namespace("publish");
        requreLib.setplugs("wdate")
        var telcount = 0;
        $(function () {
            var startDate = getTodayDate();
            var endDate = getTodayDate(+7);
            $("#examPublishStartTime").val(startDate + " 00:00:00");
            $("#examPublishEndTime").val(endDate + " 23:59:59");
            publish.multiSetting = {
                buttonWidth: '220px',
                allSelectedText: "全部",
                nonSelectedText: "--请选择--",
                nSelectedText: "个被选中",
                selectAllNumber: false,
                maxHeight: 350,
                includeSelectAllOption: true,
                selectAllText: '全选',
                enableFiltering: true
            };
            publish.multiSetting1 = {
                buttonWidth: '220px',
                allSelectedText: "全部",
                nonSelectedText: "--请选择--",
                nSelectedText: "个被选中",
                selectAllNumber: false,
                maxHeight: 350,
                includeSelectAllOption: true,
                selectAllText: '全选',
                enableFiltering: true
            };
            $('#publish_branch_code').multiselect(publish.multiSetting);


            $("#publishForm").render({
                success: function (result) {
                }

            });
        })
        $(document).ready(function () {
            // 默认隐藏参考答案框
            $('#referenceOptions1').hide();
            $('#referenceOptions2').hide();
            // 当任务类型选择改变时触发的函数
            $('#taskType').on('change', function () {
                const taskType = $(this).val();

                // 如果任务类型为'STUDY'，显示参考答案框
                if (taskType === 'STUDY') {
                    $('#referenceOptions1').show();
                    $('#referenceOptions2').show();
                } else {
                    // 其他情况下，隐藏参考答案框
                    $('#referenceOptions1').hide();
                    $('#referenceOptions2').hide();
                }
            });

            // 初始化时检查当前任务类型
            const initialTaskType = $('#taskType').val();
            if (initialTaskType === 'STUDY') {
                $('#referenceOptions').show();
            } else {
                $('#referenceOptions').hide();
            }

            $('#reminderTimeRadioCustom').prop('checked', false);

            // 当自定义时间radio被选中时，显示输入框；其他单选被选中时，隐藏输入框并清空其值
            $('input[name="reminderTime"]').on('change', function () {
                if ($(this).val() === 'custom') {
                    $('#reminderTimeRadioCustom').prop('checked', true);
                } else {
                    $('#reminderTimeInputCustom').val('');
                }
            });

            // 维持逻辑，当自定义时间输入框失去焦点时，更新关联的data-value
            $('#reminderTimeInputCustom').on('blur', function () {
                const inputValue = $(this).val();
                $('#reminderTimeRadioCustom').attr('data-value', inputValue);
            });


        });
        document.onkeydown = function (e) {
            console.log("onkeydown")
            var ev = document.all ? window.event : e;
            if (ev.keyCode == 13) {
                var people = $("#publish_user_name").val();
                if (people != '') {
                    var data = {};
                    console.log(people);
                    var arr = people.replace(/,/ig, ';').split(";");
                    for (j = 0, len = arr.length; j < len; j++) {
                        var p = /\(([^()]+)\)/g;
                        while (r = p.exec(arr[j])) {
                            arr.splice(j, 1, r[1]);
                        }
                    }
                    data.id = arr;
                    ajax.remoteCall("${ctxPath}/servlet/User/?query=getUsers", data, function (result) {
                            //debugger;
                            if (result.state == 1) {
                                var dataList = result.data;
                                var info = "";
                                var infoId = "";
                                for (var i = 0; i < dataList.length; i++) {
                                    info += dataList[i].userName + "(" + dataList[i].userAcct + ")" + ",";
                                    infoId += dataList[i].userAcct + ",";
                                }
                                var receive = info.length > 0 ? info.substring(0, info.length - 1) : "";
                                var userId = infoId.length > 0 ? infoId.substring(0, infoId.length - 1) : "";
                                $("#publish_user_name").val(receive);
                                $("#publish_user_acc").val(userId);
                                $("#displayTelcount").text(dataList.length);
                                document.getElementById("displayTelcount").innerText = dataList.length;
                            } else {
                                layer.alert(result.msg, {icon: 5});
                            }
                        }
                    );
                } else {
                    $("#user_id").val('');

                    $("#BC_ID").attr("disabled", false);

                }
            }
        }
        document.getElementById("displayTelcount").innerText = telcount;
        publish.ajaxSubmitForm = function () {
            if (!form.validate("#publishForm")) {
                return;
            }
            ;
            var data = form.getJSONObject("publishForm");
            data.telcount = document.getElementById("displayTelcount").innerText;
            if (data.reminderTime === "1") {
                let examPublishStartTime = new Date(data.examPublishStartTime); // 确保examPublishStartTime是Date对象
                let customReminderTime = new Date(examPublishStartTime.getTime() - 900000); // 减去15分钟（以毫秒计）
                data.customReminderTime = getStrByDate(customReminderTime);
            } else if (data.reminderTime === "2") {
                let examPublishStartTime = new Date(data.examPublishStartTime); // 确保examPublishStartTime是Date对象
                let customReminderTime = new Date(examPublishStartTime.getTime() - 1800000); // 减去30分钟（以毫秒计）
                data.customReminderTime = getStrByDate(customReminderTime);
            }
            if (!data.customReminderTime || data.customReminderTime === '') {
                layer.alert("请输入正确的提醒时间！", {icon: 5});
                return;
            }
            ajax.remoteCall("${ctxPath}/training/record?action=publishSave", data, function (result) {
                if (result.state == 1) {
                    layer.closeAll();
                    layer.msg(result.msg, {icon: 1});
                    training.setPublishId(result.data)
                    training.newreload();
                } else {
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }
        publish.loadBranchCode = function (data) {
            var sel2 = $("#publish_branch_code").data('select2');
            $("#publish_branch_code").data({
                "mars": "comm.getBranchCode('" + data.value + "')",
            });
            $("#publish_branch_code").multiselect('destroy');
            $("#publish_branch_code").empty();
            $("#publish_branch_code").render({
                success: function (result) {
                    $('#publish_branch_code').multiselect(publish.multiSetting);
                }
            });
            $("#publish_branch_code").data('select2', sel2);
        }
        publish.test = function () {
            popup.layerShow({
                type: 2,
                title: '测验选择',
                area: ['920px', '700px'],
                offset: '20px'
            }, "${ctxPath}/pages/training/test-list.jsp?&ok=1&examPaperId=examPaperId&examPaperName=examPaperName&passingScore=passingScore&createBy=createBy&createTime=createTime&taskGenerationMethod=taskGenerationMethod&examDuration=examDuration&examPaperGenerateType=examPaperGenerateType");
            console.log("#examPaperName");
            console.log("#passingScore");
        }
        var olduser = "";
        jQuery.namespace("common");
        common.user = function () {
            var oldIds = "";
            var oldName = "";
            if ($("#publish_user_acc").val() != "") {
                oldIds = $("#publish_user_acc").val();
                oldName = $("#publish_user_name").val();
                olduser = $("#publish_user_acc").val();
            }
            popup.layerShow({
                type: 2,
                title: "用户选择",
                offset: '20px',
                area: ['1000px', '650px']
            }, "${ctxPath}/servlet/User?action=user", {enable: true, oldIds: "", oldName: ''});
        }
        common.importData = function () {
            popup.layerShow({
                type : 2,
                title : "名单导入",
                offset : '20px',
                area : [ '420px', '200px' ]
            }, "${ctxPath}/training/record?action=importData", null);
        }
        common.setUser = function (acc, name, telcount) {//接收用户信息
            $("#publish_user_acc").val(acc);
            $("#publish_user_name").val(name);
            $("#displayTelcount").text(telcount);
            $("#telcount").text(telcount);
        }
        var taskOption = [
            {value: "考试", label: "考试"},
            {value: "练习", label: "练习"}
        ];
        document.addEventListener('DOMContentLoaded', function () {
            var selectElement = document.getElementById('conversion_type');

            taskOption.forEach(function (item) {
                var option = document.createElement('option');
                option.value = item.value;
                option.textContent = item.label;

                selectElement.appendChild(option);
            });
        });
        var pcNodes1;
        var pcSetting1 = {
            data: {
                simpleData: {
                    enable: true,
                    idKey: "id",
                    pIdKey: "pId",
                    rootPId: 0
                },
                key: {
                    name: "name"
                }
            },
            callback: {
                onClick: zTreeOnclick1
            },
            async: {
                enable: true,
                type: "post",
                url: "${ctxPath}/servlet/comm?query=proCode2Level",
                autoParam: ["id", "level"]
            }
        };
        var srNodes1;
        var srSetting1 = {
            data: {
                simpleData: {
                    enable: true,
                    idKey: "id",
                    pIdKey: "pId",
                    rootPId: 0
                },
                key: {
                    name: "name"
                }
            },
            callback: {
                onClick: zTreeOnclick1
            },
            async: {
                enable: true,
                type: "post",
                url: "${ctxPath}/servlet/comm?query=serviceRequire2Level",
                autoParam: ["id", "level"],
                otherParam: ["orgCode", function () {
                    return $("#orgCode").val()
                }]
            }
        };

        function onBodyDownPc1(event) {
            if (!(event.target.id == "menuContent1" || event.target.id == "prodName" || $(event.target).parents("#menuContent1").length > 0)) {
                hideMenu1('menuContent1');
            }
        }

        //点击事件
        function zTreeOnclick1(event, treeId, treeNode) {
            if (treeNode.isParent) {
                var ztreeObj = $.fn.zTree.getZTreeObj(treeId);
                ztreeObj.expandNode(treeNode);
            } else {
                $("#prodCode").val(treeNode.id).change();
                $("#prodName").val(treeNode.name);
                hideMenu1('menuContent1');
            }
        }

        //显示菜单
        function showMenu(obj, treeId) {
            var leftPx = $(obj).offset().left;
            var topPx = $(obj).offset().top;
            var heightPx = $(obj).height() + $(obj).innerHeight() / 2;
            $("#" + treeId).slideDown("fast");
            $("body").bind("mousedown", onBodyDownPc1);
        }

        //隐藏菜单
        function hideMenu1(divId) {
            $("#" + divId).fadeOut("fast");
            $("body").unbind("mousedown", onBodyDownPc1);
        }

        function cleanVal(data, id) {//如果值为空则清空某个id的值
            if ($(data).val() == "") {
                $("#" + id).val('');
            }
        }

        $(function () {
            $("#pulish_num").keydown(function (event) {
                if (!(event.keyCode == 46) && !(event.keyCode == 8) && !(event.keyCode == 37) && !(event.keyCode == 39)) {
                    if (!((event.keyCode >= 48 && event.keyCode <= 57) || (event.keyCode >= 96 && event.keyCode <= 105))) {
                        return false;
                    }
                }
            }).keyup(function (e) {
                this.value = this.value.replace(/^0+/, '')
            });
        })

        function queryServer() {
            var orgCode = $("#orgCode").val();
            var prodCode = $("#prodCode").val();
            var laseServiceItem = $("#contactOrderServTypeCode").val();
            var text = "";//带入搜索框的内容
            text = $("#contactOrderSerItem2Name").val();
            var isExistUrge = false;
            popup.layerShow({
                type: 2,
                title: '服务请求',
                area: ['400px', '450px'],
                offset: '20px',
                shadeClose: false
            }, "${ctxPath}/pages/access/service-query.jsp?training=1&orgCode=" + orgCode + "&prodCode=" + prodCode + "&laseServiceItem=" + laseServiceItem + "&isExistUrge=" + isExistUrge + "&text=" + text);
        }

        function changecontactOrderServTypeCode(ths) {
            $("#contactOrderSerItem2Code").val("");
        }

        function getFeedbackSubItemCodeByTpye(ths, id) {
            if ($(ths).val() == "") {
                $("#" + id).data({
                    "mars": "comm.feedbackitemByType('-1')",
                });
                $("#" + id).render({});
            } else {
                $("#" + id).data({
                    "mars": "comm.feedbackitemByType('" + $(ths).val() + "')",
                });
                $("#" + id).render({});
            }
        }

        function getFeedbackSubItemCode(ths, id) {
            if ($(ths).val() == "") {
                $("#" + id).data({
                    "mars": "comm.feedbackitem('-1')",
                });
                $("#" + id).render({});
            } else {
                $("#" + id).data({
                    "mars": "comm.feedbackitem('" + $(ths).val() + "')",
                });
                $("#" + id).render({});
            }
        }

        publish.changheSources = function (ths) {
            if ($(ths).val() == "0") {//导入
                $(".systemOrder").hide()
                $(".fwOrder").hide()
            } else if ($(ths).val() == "1") {//系统
                $(".systemOrder").show()
                $(".fwOrder").hide()
            } else if ($(ths).val() == "2") {//系统纯服务单
                $(".systemOrder").hide()
                $(".fwOrder").show()
            } else {//其他 （空）
                $(".systemOrder").show()
                $(".fwOrder").show()
            }
            publish.cleanOrder();

        }
        publish.cleanOrder = function () {
            $("#wom_archive_type").val("");
            $("#client_name").val("");
            $("#prodCode").val("");
            $("#prodName").val("");
            $("#brand_code").val("");
            $("#publish_website_code").val("");
            $("#publish_website_name").val("");
            $("#attribute8").val("");
            $("#publish_service_time").val("");
            $("#callback_result").val("");
            $("#attribute7").val("");
            $("#archives_num").val("");
            $("#contactOrderServTypeCode").val("");
            $("#contactOrderSerItem2Name").val("");
            $("#contactOrderSerItem2Code").val("");
            $("#feedbackItemCode").val("");
            $("#feedbackItemType").val("");
            $("#feedbackSubItemCode").val("");
            $("#serviceTypeCode").val("");
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>