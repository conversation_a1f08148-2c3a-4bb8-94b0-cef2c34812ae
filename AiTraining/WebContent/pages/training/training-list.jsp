<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>AI培训列表查询</title>
    <style>
        #menuContent {
            display: none;
            position: absolute;
            border: 1px solid rgb(170, 170, 170);
            max-width: 220px;
            max-height: 350px;
            z-index: 10;
            overflow: auto;
            background-color: #f4f4f4
        }

        .pagination pagination-sm pageNumV {
            float: right
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form autocomplete="off" action="" method="post" name="searchForm" class="form-inline" id="searchForm"
          onsubmit="return false" data-toggle="">
        <div class="ibox">
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <h5><span class="glyphicon glyphicon-list"></span> AI培训列表查询</h5>
                </div>
                <hr style="margin:5px -15px">
                <div class="form-group moreSearch">
                    <div class="input-group input-group-sm" style="width: 200px;">
                        <span class="input-group-addon" style="width: 80px;">任务名称</span>
                        <input type="text" id="taskName" name="taskName" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm" style="width: 200px;">
                        <span class="input-group-addon" style="width: 80px;">任务类型</span>
                        <select class="form-control input-sm" id="taskTypes" name="taskType" data-mars="">
                        </select>
                    </div>
                    <div class="input-group input-group-sm" style="width: 250px;">
                        <span class="input-group-addon" style="width: 80px;">发布人</span>
                        <input type="text" name="publisherAcc" id="publisherAcc" class="hidden" style="">
                        <input placeholder="不限" type="text" name="publisherName" id="publisherName"
                               readonly="readonly" class="form-control input-sm" style="">
                        <span class="input-group-addon" onclick="clearPublisher()">
                        <i class="glyphicon glyphicon-remove-circle"></i>
                    </span>
                        <span class="input-group-addon" onclick="common.user()">
                                    <i class="glyphicon glyphicon-search"></i></span>
                    </div>
                    <div class="input-group input-group-sm" style="width: 200px;">
                        <span class="input-group-addon" style="width: 80px;">启用状态</span>
                        <select class="form-control input-sm" id="activeStatuss" name="activeStatus"
                                data-mars="">
                        </select>
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon" style="width: 80px;">发布时间</span>
                        <input type="text" name="publishStartTime" id="publishStartTime" data-mars=""
                               class="form-control input-sm Wdate" value=""
                               onclick="WdatePicker({maxDate:'#F{$dp.$D(\'publishEndTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                        <span class="input-group-addon">-</span>
                        <input type="text" name="publishEndTime" id="publishEndTime" data-mars=""
                               class="form-control input-sm Wdate" value=""
                               onclick="WdatePicker({minDate:'#F{$dp.$D(\'publishStartTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon" style="width: 80px;">测验有效时间</span>
                        <input type="text" name="examStartTime" id="examStartTime" data-mars=""
                               class="form-control input-sm Wdate" value=""
                               onclick="WdatePicker({maxDate:'#F{$dp.$D(\'examEndTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                        <span class="input-group-addon">-</span>
                        <input type="text" name="examEndTime" id="examEndTime" data-mars=""
                               class="form-control input-sm Wdate" value=""
                               onclick="WdatePicker({minDate:'#F{$dp.$D(\'examStartTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                    </div>
                    <!-- <div class="input-group input-group-sm pull-right ml-10">
                        <button type="button" id="moreBtn" class="btn btn-sm btn-link" onclick="toggleMore()"
                            title="展开"> 展开 <span
                                class="glyphicon glyphicon glyphicon-menu-up"></span>&nbsp;</button>
                    </div> -->
                </div>
                <!-- <div class="form-group moreSearch" style="display:none">
                </div> -->
                <div class="form-group">
                    <div class="input-group input-group-sm pull-left mr-10">
                        <button type="button" id="neworder_visit_resetting" class="btn btn-sm btn-success"
                                onclick="resetting()">重置
                        </button>
                    </div>
                        <%--                            <EasyTag:res resId="neworder_visit_publish">--%>
                    <div class="input-group input-group-sm pull-left mr-10">
                        <button type="button" id="neworder_visit_publish" class="btn btn-sm btn-success"
                                onclick="training.publish()">发布
                        </button>
                    </div>
                        <%--                        </EasyTag:res>--%>
                        <%--                        <EasyTag:res resId="neworder_contact_excel">--%>
                    <div class="input-group input-group-sm pull-left mr-10">
                        <button type="button" class="btn btn-sm btn-success"
                                onclick="training.excel()"><span class="glyphicon glyphicon-export"></span>
                            导出
                        </button>
                    </div>
                    <div class="input-group input-group-sm pull-left mr-10">
                        <button type="button" class="btn btn-sm btn-success"
                                onclick="reload()">查询
                        </button>
                    </div>
                        <%--                            </EasyTag:res>--%>
                        <%--                            <div class="input-group input-group-sm pull-right">--%>
                        <%--                                <button type="button" class="btn btn-sm btn-default" onclick="reload()"><span--%>
                        <%--                                        class="glyphicon glyphicon-search"></span> 搜索</button>--%>
                        <%--                            </div>--%>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row table-responsive">
                    <table class="table table-auto table-bordered table-hover table-condensed text-c"
                           id="tableHead">
                        <thead>
                        <tr>
                            <th class="text-c">任务ID</th>
                            <th class="text-c">任务名称</th>
                            <th class="text-c">任务发布时间</th>
                            <th class="text-c">发布人</th>
                            <th class="text-c">习题集开始时间</th>
                            <th class="text-c">习题集结束时间</th>
                            <th class="text-c">任务类型</th>
                            <th class="text-c">测验时长</th>
                            <th class="text-c">任务生成方式</th>
                            <th class="text-c">陪练总人数</th>
                            <th class="text-c">启用状态</th>
                            <th class="text-c">操作</th>
                        </tr>
                        </thead>
                        <tbody id="dataList">
                        </tbody>
                    </table>
                    <script id="list-template" type="text/x-jsrender">
                        {{for list}}
                             <tr>
                                <td>{{:TASK_ID}}</td>
                                <td>{{:TASK_NAME}}</td>
                                <td>{{:TASK_PUBLISH_TIME}}</td>
                                <td>{{:PUBLISHER_NAME}}</td>
                                <td>{{:EXAM_START_TIME}}</td>
                                <td>{{:EXAM_END_TIME}}</td>
                                <td>{{:TASK_TYPE}}</td>
                                <td>{{:TEST_DURATION}}</td>
                                <td>{{:TASK_GENERATION_METHOD}}</td>
                                <td>{{:TRAINING_TOTAL}}</td>
                                <td>{{:ENABLE_STATUS}}</td>
                                <td class="ss_td_a">
                                    <a href="javascript:void(0);" class="getUserInfo" data-task-id="{{:TASK_ID}}" data-status="{{:ENABLE_STATUS}}">查看</a>
                                    {{if ENABLE_STATUS==='启用'}}
                                        <a href="javascript:void(0);" class="status-change" data-task-id="{{:TASK_ID}}" data-status="{{:ENABLE_STATUS}}">禁用</a>
                                    {{else}}
                                        <a href="javascript:void(0);" class="status-change" data-task-id="{{:TASK_ID}}" data-status="{{:ENABLE_STATUS}}">启用</a>
                                    {{/if}}
                                </td>
                             </tr>
                        {{/for}}
                    </script>
                </div>
                <div class="row paginate" id="page">
                    <jsp:include page="/pages/common/pagination.jsp">
                        <jsp:param value="10" name="pageSize"/>
                    </jsp:include>
                </div>
            </div>
        </div>
    </form>

</EasyTag:override>

<EasyTag:override name="script">
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css"/>
    <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
    <script type="text/javascript" src="/iccportal5/static/js/privacyUtil.js"></script>
    <script type="text/javascript" src="/neworder/static/js/elementui/axios.js"></script>
    <script type="text/javascript">
        jQuery.namespace("training");
        requreLib.setplugs('wdate')//加载时间控件
        var org_code;
        var wom_archive_type;
        var conversion_type;
        var ListproCodeTree;
        var taskTypes = [];
        var activeStatuss = [];
        training.setPublishId = function (val) {
            $("#c_no_publish_id").val(val)
        }
        training.newreload = function (val) {
            if ($("#taskName").val() === "" && $("#publisherAcc").val() === "" && ($("#examStartTime").val() === "" || $("#examEndTime").val() === "")) {
                alert("查询条件未填写，任务名称、发布人和有效时间三选一必填");
                return;
            }
            $("#tableHead").data("mars", "trainingRecord.getAiTrainingRecordList");
            $("#searchForm").searchData();
        }

        function reload() {
            //console.log("taskName = " + $("#taskName").val());
            //console.log("publisherAcc = " + $("#publisherAcc").val());
            if ($("#taskName").val() === "" && $("#publisherAcc").val() === "" && ($("#examStartTime").val() === "" || $("#examEndTime").val() === "")) {
                alert("查询条件未填写，任务名称、发布人和有效时间三选一必填");
                return;
            }
            $("#c_no_publish_id").val('');
            $("#tableHead").data("mars", "trainingRecord.getAiTrainingRecordList");
            $("#searchForm").searchData();
        }

        function clearPublisher() {
            $("#publisherAcc").val("");
            $("#publisherName").val("");
        }

        $(document).on('click', '.status-change', function () {
            var taskId = $(this).data('task-id');
            var currentStatus = $(this).data('status');
            changeTaskStatus(taskId, currentStatus);
        });
        $(document).on('click', '.getUserInfo', function () {
            var taskId = $(this).data('task-id');
            getUserInfo(taskId);
        });
        $(function () {
            var startDate = getTodayDate(-90);
            var endDate = getTodayDate();
            $("#examStartTime").val(startDate + " 00:00:00");
            $("#examEndTime").val(endDate + " 23:59:59");
            $("#publishStartTime").val(startDate + " 00:00:00");
            $("#publishEndTime").val(endDate + " 23:59:59");

            var reqJson1 = {
                params: {"param[0]": "TASK_TYPE"},
                controls: ["dict.getDictList"],
            };
            var encodedParam = new URLSearchParams();
            encodedParam.append("data", JSON.stringify(reqJson1));

            webcall("/yq_common/webcall", encodedParam, function (result) {
                let data = result.data;
                if (data) {
                    let orgData = data["dict.getDictList"];
                    console.log(orgData);
                    let index = 1;
                    let optionDict = orgData.data;
                    if (optionDict) {
                        $('#taskTypes').append('<option value="">请选择</option>');
                        for (let key in optionDict) {
                            let label = optionDict[key];
                            let option = '<option value="' + key + '" key="' + (parseInt(key) + 1) + '">' + label + '</option>';
                            $('#taskTypes').append(option);
                        }
                    }
                }
            });

            var reqJson2 = {
                params: {"param[0]": "ACTIVE_STATUS"},
                controls: ["dict.getDictList"],
            };
            var encodedParam2 = new URLSearchParams();
            encodedParam2.append("data", JSON.stringify(reqJson2));

            webcall("/yq_common/webcall", encodedParam2, function (result) {
                let data = result.data;
                if (data) {
                    let orgData = data["dict.getDictList"];
                    console.log(orgData);
                    let index = 1;
                    let optionDict = orgData.data;
                    if (optionDict) {
                        $('#activeStatuss').append('<option value="">请选择</option>');
                        for (let key in optionDict) {
                            let label = optionDict[key];
                            let option = '<option value="' + key + '" key="' + (parseInt(key) + 1) + '">' + label + '</option>';
                            $('#activeStatuss').append(option);
                        }
                    }
                }
            });

            function webcall(url, param, callback) {
                axios
                    .post(url, param, {
                        headers: {
                            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                        },
                    })
                    .then((result) => {
                        callback && callback(result);
                    });
            };
        });
        jQuery.namespace("common");
        common.user = function () {
            var oldIds = "";
            var oldName = "";
            if ($("#publisherAcc").val() != "") {
                oldIds = $("#publisherAcc").val();
                oldName = $("#publisherName").val();
            }
            popup.layerShow({
                type: 2,
                title: "用户选择",
                offset: '20px',
                area: ['1000px', '650px']
            }, "${ctxPath}/servlet/User?action=user", {enable: true, oldIds: "", oldName: '', userSingle: 'true'});
        }
        common.setUser = function (acc, name) {//接收用户信息
            $("#publisherAcc").val(acc);
            $("#publisherName").val(name);
        }

        function toggleMore() {
            var btn = $("#moreBtn").find(".glyphicon");
            $(".moreSearch").slideToggle('fast');
            btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
        }

        training.publish = function () {
            popup.layerShow({
                type: 1,
                title: '发布',
                area: ['700px', '500px'],
                offset: '20px'
            }, "${ctxPath}/training/record/?action=trainingPublish");
        }
        jQuery.namespace("recipient");
        var pcNodes;
        var ListpcNodes;
        var srNodes;
        var srSetting = {
            data: {
                simpleData: {
                    enable: true,
                    idKey: "id",
                    pIdKey: "pId",
                    rootPId: 0
                },
                key: {
                    name: "name"
                }
            },
            callback: {
                onClick: zTreeOnclick
            },
            async: {
                enable: true,
                type: "post",
                url: "${ctxPath}/servlet/comm?query=serviceRequire2Level",
                autoParam: ["id", "level"],
                otherParam: ["orgCode", function () {
                    return $("#orgCode").val()
                }]
            }
        };

        function changeTaskStatus(taskId, enableStatus) {
            var data = {
                taskId: taskId,
                enableStatus: enableStatus
            };
            ajax.remoteCall("${ctxPath}/training/record/?action=changeTaskStatus", data, function (result) {
                if (result.state === 1) {
                    layer.msg(result.msg, {icon: 1});
                    training.newreload();
                } else {
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        function getUserInfo(taskId) {
            popup.layerShow({
                type: 2,
                title: '用户信息',
                area: ['920px', '700px'],
                offset: '20px'
            }, "${ctxPath}/pages/training/training-user-info-list.jsp?&ok=1&taskId=" + taskId);

        }

        function onBodyDownPc(event) {
            if (!(event.target.id == "menuContent" || event.target.id == "proCodeShow" || $(event.target).parents("#menuContent").length > 0)) {
                hideMenu('menuContent');
            }
        }

        //点击事件
        function zTreeOnclick(event, treeId, treeNode) {
            if (treeNode.isParent) {
                var ztreeObj = $.fn.zTree.getZTreeObj(treeId);
                ztreeObj.expandNode(treeNode);
            } else {
                $("#prod_code").val(treeNode.id);
                $("#proCodeShow").val(treeNode.name);
                hideMenu('menuContent');
            }
        }

        //点击事件
        function ListzTreeOnclick(event, treeId, treeNode) {
            selectAll = false;
            if (treeNode.isParent) {
                var ztreeObj = $.fn.zTree.getZTreeObj(treeId);
                ztreeObj.expandNode(treeNode);
            }
        }

        training.excel = function () {
            var length = $("#tableHead #dataList tr").length;
            console.log(length);
            var size = $("._pagination .totalRow").html();
            console.log(size);
            if (length <= 0 || size == '' || size == '0') {
                alert("当前暂无数据！");
                return;
            }
            if (size > 50000) {
                alert("导出数据量过大，请保证总条数小于50000！");
                return;
            }
            auditLog(JSON.stringify({
                "model": "AiTraining",
                "url": "/AiTraining/pages/training/training-list.jsp",
                "action": "download",
                "describe": "用户{USER_ACC}导出[接入单查询列表]数据"
            }));
            var data = form.getJSONObject("searchForm");
            data.pageSize = size;
            downloadExl(data, '${ctxPath}/training/record/?action=constactExport&');
        }

        var selectAll = false;

        //异步加载后（是否全选）
        function ajaxDataFilter(treeId, parentNode, responseData) {
            //当每次加载成功后且点击全选按钮时，将新加载后的全部节点选中，responseData表示每次新加载的所有节点
            if (responseData && selectAll) {
                var selectid = $("#prod_code").val();
                var selectname = $("#proCodeShow").val();
                ;
                for (var i = 0; i < responseData.length; i++) {
                    responseData[i].checked = true;
                    selectid = (selectid == "" ? responseData[i].id : selectid + "," + responseData[i].id);
                    selectname = (selectname == "" ? responseData[i].name : selectname + "," + responseData[i].name);
                }
                $("#prod_code").val(selectid);
                $("#proCodeShow").val(selectname);
            }
            ListproCodeTree = $.fn.zTree.getZTreeObj("proCodeTree");
            return responseData;
        };

        //勾选事件

        function zTreeOnCheck(event, treeId, treeNode) {
            //全选
            selectAll = true;//
            if (treeNode.isParent) {
                var ztreeObj = ListproCodeTree;
                if (ztreeObj != null) {
                    ztreeObj.expandNode(treeNode);
                }
            }
            var selectid = "";
            var selectname = "";
            var treeObj = ListproCodeTree;
            var nodes = treeObj.getCheckedNodes(true);
            for (var i = 0; i < nodes.length; i++) {
                if (!nodes[i].isParent) {
                    selectid = (selectid == "" ? nodes[i].id : selectid + "," + nodes[i].id);
                    selectname = (selectname == "" ? nodes[i].name : selectname + "," + nodes[i].name);
                }
            }
            $("#prod_code").val(selectid);
            $("#proCodeShow").val(selectname);

        }

        //显示菜单
        function showMenu(obj, treeId) {
            var leftPx = $(obj).offset().left;
            var topPx = $(obj).offset().top;
            var heightPx = $(obj).height() + $(obj).innerHeight() / 2;
            $("#" + treeId).css({left: leftPx, top: topPx + heightPx}).slideDown("fast");
            $("body").bind("mousedown", onBodyDownPc);
        }

        //隐藏菜单
        function hideMenu(divId) {
            $("#" + divId).fadeOut("fast");
            $("body").unbind("mousedown", onBodyDownPc);
        }

        function loadBranchCode(data) {
            $("#branch_code").data("mars", "comm.getBranchCode('" + data.value + "')");
            $("#branch_code").render();
        }

        $.views.converters("ORG_CODE", function (val) {
            var req = org_code;
            if (typeof (req.data) == "undefined") {
                return val;
            } else {
                return req.data[val];
            }
        });

        $.views.converters("WOM_ARCHIVE_TYPE", function (val) {
            var req = wom_archive_type;
            if (typeof (req.data) == "undefined") {
                return val;
            } else {
                return req.data[val];
            }
        });
        $.views.converters("CONVERSION_TYPE", function (val) {
            var req = conversion_type;
            if (typeof (req) == "undefined") {
                return val;
            } else {
                return req[val];
            }
        });

        function resetting() {
            document.searchForm.reset();
            var startDate = getTodayDate(-7);
            var endDate = getTodayDate();

            $("#tableHead").data("mars", "trainingRecord.getAiTrainingRecordList");
            $("#searchForm").searchData();
        }

        function maxTime(e) {
            var start = $("#callback_select_syn_start_time").val();
            var end = $("#callback_select_syn_end_time").val();
            if (start != "" && end != "" && start > end) {
                layer.msg("开始时间应小于结束时间", {icon: 5});
                var time = end.substring(0, 10) + start.substring(10);
                $("#callback_select_syn_start_time").val(time);
            }
        }

        function maxTime1(e) {
            var start = $("#receive_start_time").val();
            var end = $("#receive_end_time").val();
            if (start != "" && end != "" && start > end) {
                layer.msg("开始时间应小于结束时间", {icon: 5});
                var time = end.substring(0, 10) + start.substring(10);
                $("#receive_start_time	").val(time);
            }
        }

        function changecontactOrderServTypeCode(ths) {
            $("#contactOrderSerItem2Name").val("");
            $("#contactOrderSerItem2Code").val("");
        }

        function showDetail(id, name, phone, address) {
            showDetailCommon({
                "model": "neworder",
                "url": "/neworder/servlet/training?action=list",
                "action": "acces",
                "describe": "用户查询[投诉处理查询]数据，看客户[{{name}}]敏感信息：[客户号码：{{phone}},用户地址：{{address}}]"
            }, id, name, address, phone);
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>