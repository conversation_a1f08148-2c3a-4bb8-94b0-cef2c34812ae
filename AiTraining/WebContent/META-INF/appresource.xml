<?xml version="1.0" encoding="UTF-8"?>
<!-- 
	id:必须全局唯一，命名方式：应用名称+菜单名称
	name：菜单或功能名称
	type：资源类型，1：应用 ，2：菜单，3：功能，9: 其它
	state：状态，0:正常，缺省 ，1：暂停
	portal:所属应用，不填写缺省为通用portal，根据菜单的归属，可以填写具体的所属的portal，例如：my_portal
	index:排序，菜单的显示按照升序进行排序。
	icon：菜单图标
 -->
<resources>
    <resource id="AiTraining" name="AI陪练" url="" type="2" portal="" state="0" order="4" index="10">
        <resource id="training-list" name="AI陪练列表" url="/AiTraining/pages/training/training-list.jsp" type="2"
                  portal="" icon="" state="0" index="1">
            <resource id="training_publish" name="发布" url="" type="3" portal="" icon="" state="0" index="1"/>
        </resource>
        <resource id="training-user-list" name="所有任务列表"
                  url="/AiTraining/pages/training/training-all-user-list.jsp" type="2" portal="" icon="" state="0"
                  index="2">
        </resource>
        <resource id="training-all-user-list" name="我的任务列表"
                  url="/AiTraining/pages/training/training-user-list.jsp" type="2" portal="" icon="" state="0"
                  index="3">
        </resource>
        <resource id="question-base" name="试题库管理"
                  url="/AiTraining/pages/training/question-base.jsp" type="2" portal="" icon="" state="0"
                  index="4">
        </resource>
        <resource id="exampaper-base" name="试卷库管理"
                  url="/AiTraining/pages/training/exampaper-base.jsp" type="2" portal="" icon="" state="0"
                  index="5">
        </resource>
        <resource id="mistake-question" name="错题分析"
                  url="/AiTraining/pages/training/mistake-question.jsp" type="2" portal="" icon="" state="0"
                  index="6">
        </resource>
        <resource id="self-test" name="自助陪练"
                  url="/AiTraining/pages/training/self-test.jsp" type="2" portal="" icon="" state="0"
                  index="7">
        </resource>
        <resource id="self-test" name="标签库页面"
                  url="/AiTraining/pages/training/tag-base.jsp" type="2" portal="" icon="" state="0"
                  index="8">
        </resource>
    </resource>
</resources>
