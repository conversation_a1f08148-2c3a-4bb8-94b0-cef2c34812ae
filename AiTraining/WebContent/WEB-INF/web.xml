<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd"
         metadata-complete="false" version="3.1">
    <display-name>AiTraining</display-name>
    <description>
        AiTraining
    </description>
    <servlet>
        <servlet-name>EasyDaoBaseServlet</servlet-name>
        <servlet-class>org.easitline.common.core.web.EasyDaoBaseServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>EasyDaoBaseServlet</servlet-name>
        <url-pattern>/webcall/*</url-pattern>
    </servlet-mapping>
    <listener>
        <listener-class>org.easitline.common.core.dao.DaoContextListener</listener-class>
    </listener>
    <servlet>
        <servlet-name>DruidStatView</servlet-name>
        <servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>DruidStatView</servlet-name>
        <url-pattern>/servlet/monitor/druid/*</url-pattern>
    </servlet-mapping>
    <security-constraint>
        <display-name>allpage</display-name>
        <web-resource-collection>
            <web-resource-name>allpage</web-resource-name>
            <description>allpage</description>
            <url-pattern>/webcall/*</url-pattern>
            <url-pattern>/servlet/*</url-pattern>
            <url-pattern>/pages/*</url-pattern>
            <http-method>GET</http-method>
            <http-method>POST</http-method>
        </web-resource-collection>
        <auth-constraint>
            <description>These are the roles who have access</description>
            <role-name>everyone</role-name>
        </auth-constraint>
        <user-data-constraint>
            <description>This is how the user data must be transmitted</description>
            <transport-guarantee>NONE</transport-guarantee>
        </user-data-constraint>
    </security-constraint>
    <login-config>
        <auth-method>FORM</auth-method>
        <form-login-config>
            <form-login-page>/index.jsp</form-login-page>
            <form-error-page>/index.jsp</form-error-page>
        </form-login-config>
    </login-config>
    <security-role>
        <description>everyone role</description>
        <role-name>everyone</role-name>
    </security-role>
    <session-config>
        <session-timeout>500</session-timeout>
    </session-config>
    <error-page>
        <error-code>404</error-code>
        <location>/pages/common/404.jsp</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/pages/common/500.jsp</location>
    </error-page>
    <welcome-file-list>
        <welcome-file>/index.jsp</welcome-file>
    </welcome-file-list>
</web-app>