package com.yunqu.cc.aiTraining.dao;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.TreeNode;
import com.yq.busi.common.user.DeptMgr;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.aiTraining.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;

import java.util.ArrayList;

@WebObject(name="Dept")

public class DeptDao extends AppDaoContext {

	@WebControl(name = "getDeptZtree", type = Types.TREE)
	public JSONObject getUsersByDeptCode() {
		String depCode = UserUtil.getUser(this.request).getEpCode();
		TreeNode usersByDeptCode = DeptMgr.getDeptTreeNode( new ArrayList<TreeNode>(), depCode,true,false,true);
		JSONObject jsonObject = JsonUtil.toJSONObject(usersByDeptCode);
		return getTree(jsonObject);
	}
}
