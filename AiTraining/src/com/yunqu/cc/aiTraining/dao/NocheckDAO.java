package com.yunqu.cc.aiTraining.dao;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.DeptModel;
import com.yq.busi.common.user.DeptMgr;
import com.yunqu.cc.aiTraining.base.AppDaoContext;
import com.yunqu.cc.aiTraining.base.CommonLogger;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@WebObject(name="nocheck")
public class NocheckDAO extends AppDaoContext {
    private Logger logger = CommonLogger.logger;

    @WebControl(name = "getDept", type = Types.RECORD)
    public JSONObject getDept() {
        Object levelO = param.get("level");
        Object parentCodeO = param.get("parentCode");

        int level = 4;
        String parentCode = null;

        if (levelO != null) {
            level = Integer.parseInt(levelO + "");
        }
        if (parentCodeO != null) {
            parentCode = String.valueOf(parentCodeO).trim();
        }

        List<DeptModel> list = DeptMgr.getDeptByLevel(parentCode, level, false);
        Map<String, Object> ma = new HashMap<>();
        for (DeptModel deptModel : list) {//把值先存map然后在把值丢到json里面
            ma.put(deptModel.getDeptCode(), deptModel.getDeptName());
        }
        JSONObject json = new JSONObject();
        json.put("data", ma);

        return json;
    }
}