package com.yunqu.cc.aiTraining.dao;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.aiTraining.base.AppDaoContext;
import com.yunqu.cc.aiTraining.base.CommonLogger;
import com.yunqu.cc.aiTraining.util.CsUtil;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasySQL;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@WebObject(name = "trainingRecord")

public class AiTrainingRecordDao extends AppDaoContext {
    private Logger logger = CommonLogger.logger;

    /**
     * 查询陪练任务记录
     */
    @WebControl(name = "getAiTrainingRecordList", type = Types.LIST)
    public JSONObject getAiTrainingRecordList() {
        EasySQL sql = this.getEasySQL(" select * FROM C_AI_TRAINING_RECORD WHERE 1=1 ");
        sql.appendLike(param.getString("taskName"), "and TASK_NAME like ? ");
        sql.append(param.getString("taskType"), "and TASK_TYPE = ? ");
        sql.append(param.getString("publishAcc"), "and publishAcc = ? ");
        sql.append(param.getString("activeStatus"), "and ENABLE_STATUS = ? ");
        sql.append(param.getString("publishStartTime"), " and TASK_PUBLISH_TIME >= ? ");
        sql.append(param.getString("publishEndTime"), " and TASK_PUBLISH_TIME <= ? ");
        sql.append(param.getString("examStartTime"), " and EXAM_END_TIME >= ? ");
        sql.append(param.getString("examEndTime"), " and EXAM_START_TIME <= ? ");
        sql.append(" ORDER BY CREATE_TIME desc ");
        logger.info("查询任务数据： " + sql.getSQL() + ";" + Arrays.toString(sql.getParams()));
        JSONObject result = this.queryForPageList(sql.getSQL(), sql.getParams(), null);
        JSONArray jsonArray = result.getJSONArray("data");
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject obj = jsonArray.getJSONObject(i);
            if (obj.getString("TASK_TYPE") != null && !obj.getString("TASK_TYPE").isEmpty() && obj.getString("TASK_TYPE").equals("EXAM")) {
                obj.put("TASK_TYPE", "测验");
            } else {
                obj.put("TASK_TYPE", "陪练");
            }
            if (obj.getString("ENABLE_STATUS") != null && !obj.getString("ENABLE_STATUS").isEmpty() && obj.getString("ENABLE_STATUS").equals("ENABLE")) {
                obj.put("ENABLE_STATUS", "启用");
            } else {
                obj.put("ENABLE_STATUS", "停用");
            }
            if (obj.getString("TASK_GENERATION_METHOD") != null && !obj.getString("TASK_GENERATION_METHOD").isEmpty() && obj.getString("TASK_GENERATION_METHOD").equals("CUSTOMIZE")) {
                obj.put("TASK_GENERATION_METHOD", "自定义配置");
            } else {
                obj.put("TASK_GENERATION_METHOD", "自动生成");
            }
            jsonArray.set(i, obj);
        }
        result.put("data", jsonArray);
        return result;
    }

    /**
     * 查询试卷列表
     */
    @WebControl(name = "getExamList", type = Types.LIST)
    public JSONObject getExamList() {
        JSONObject result;
        JSONObject requestJson = new JSONObject();
        Map<String, Object> params = new HashMap<>();
        requestJson.put("command", "getExamList");
        int pageNo = param.getIntValue("pageIndex") == -1 ? 1 : param.getIntValue("pageIndex");
        params.put("pageNo", pageNo);
        params.put("pageSize", param.getIntValue("pageSize"));
        params.put("examPaperName", param.getString("examPaperName"));
        params.put("createBy", param.getString("createBy"));
        params.put("createTimeStart", param.getString("startDate"));
        params.put("createTimeEnd", param.getString("endDate"));
        requestJson.put("data", params);
        try {
            IService service = ServiceContext.getService("MIXGW_IOP_INTERFACE");
            logger.info("开始获取试卷列表：" + requestJson.toJSONString());
            result = service.invoke(requestJson);
        } catch (ServiceException e) {
            throw new RuntimeException(e);
        }
        logger.info("试卷列表返回值：" + result.toJSONString());
        Integer total = result.getJSONObject("respData").getJSONObject("data").getInteger("totalCount");
        JSONArray list = result.getJSONObject("respData").getJSONObject("data").getJSONArray("list");
        result = CsUtil.getData(result, Types.LIST);
        result.put("total", total);
        result.put("totalRow", total);
        result.put("totalPage", total / param.getIntValue("pageSize") + (total % param.getIntValue("pageSize") > 0 ? 1 : 0));
        result.put("pageNumber", pageNo);
        result.put("pageSize", param.getIntValue("pageSize"));
        result.put("data", list);
        return result;
    }

}
