package com.yunqu.cc.aiTraining.dao;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.aiTraining.base.AppDaoContext;
import com.yunqu.cc.aiTraining.base.CommonLogger;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.Optional;

@WebObject(name = "trainingUser")

public class AiTrainingUserDao extends AppDaoContext {
    private Logger logger = CommonLogger.logger;

    /**
     * 查询陪练任务记录
     */
    @WebControl(name = "getAiTrainingUserList", type = Types.LIST)
    public JSONObject getAiTrainingUserList() {
        EasySQL sql = this.getEasySQL(" select T1.TASK_ID, T1.TASK_STATUS, T1.TASK_NAME, T2.TASK_PUBLISH_TIME, T2.PUBLISHER_NAME, T2.EXAM_START_TIME, " +
                "T2.EXAM_END_TIME, T2.TASK_TYPE, T2.TEST_NAME, T1.TRAINING_RESULT, T1.TRAINING_SCORE, T1.TASK_FINISH_TIME, T1.TRAINING_USER_NAME, " +
                "T1.TRAINING_USER_ID, T2.TASK_URL, T2.ENABLE_STATUS, T4.DEPT_NAME, T5.DEPT_NAME AS P_DEPT_NAME " +
                "FROM C_AI_TRAINING_USER T1 LEFT JOIN C_AI_TRAINING_RECORD T2 ON T1.TASK_ID = T2.TASK_ID " +
                "INNER JOIN mars.EASI_USER_LOGIN T6 ON T6.USER_ACCT = T1.TRAINING_USER_ID " +
                "INNER JOIN mars.EASI_DEPT_USER T3 ON T3.USER_ID = T6.USER_ID " +
                "INNER JOIN mars.EASI_DEPT T4 ON T3.DEPT_ID = T4.DEPT_ID " +
                "INNER JOIN mars.EASI_DEPT T5 ON T4.P_DEPT_CODE = T5.DEPT_CODE");
        sql.append("WHERE 1=1 ");
        sql.append(param.getString("areaCode"), "and T5.DEPT_CODE = ? ");
        sql.append(param.getString("deptCode"), "and T4.DEPT_CODE = ? ");
        sql.appendLike(param.getString("taskName"), "and T1.TASK_NAME like ? ");
        sql.append(param.getString("taskType"), "and T2.TASK_TYPE = ? ");
        sql.append(param.getString("publisherAcc"), "and T2.PUBLISHER_ACC = ? ");
        if (param.getString("trainingUserId") == null) {
            sql.append(UserUtil.getUser(this.request).getUserAcc(), "and T1.TRAINING_USER_ID = ? ");
        } else {
            sql.append(param.getString("trainingUserId"), "and T1.TRAINING_USER_ID = ? ");
        }
        sql.append(param.getString("trainingResult"), "and T1.TRAINING_RESULT = ? ");
        sql.append(param.getString("taskStatus"), "and T1.TASK_STATUS = ? ");
        sql.append(param.getString("activeStatus"), "and T2.ENABLE_STATUS = ? ");
        sql.append(param.getString("publishStartTime"), " and T2.TASK_PUBLISH_TIME >= ? ");
        sql.append(param.getString("publishEndTime"), " and T2.TASK_PUBLISH_TIME <= ? ");
        sql.append(param.getString("taskStartTime"), " and T2.EXAM_END_TIME >= ? ");
        sql.append(param.getString("taskEndTime"), " and T2.EXAM_START_TIME <= ? ");
        sql.append(param.getInteger("minScore"), " and T1.TRAINING_SCORE >= ? ");
        sql.append(param.getInteger("maxScore"), " and T1.TRAINING_SCORE <= ? ");
        sql.append(" ORDER BY T1.CREATE_TIME desc ");
        logger.info("查询人员任务数据： " + sql.getSQL() + ";" + Arrays.toString(sql.getParams()));
        JSONObject result = this.queryForPageList(sql.getSQL(), sql.getParams(), null);
        JSONArray jsonArray = result.getJSONArray("data");
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject obj = jsonArray.getJSONObject(i);
            if (obj.getString("ENABLE_STATUS") != null && !obj.getString("ENABLE_STATUS").isEmpty() && obj.getString("ENABLE_STATUS").equals("ENABLE")) {
                obj.put("ENABLE_STATUS", "启用");
            } else {
                obj.put("ENABLE_STATUS", "停用");
            }
            if (obj.getString("TASK_TYPE") != null && !obj.getString("TASK_TYPE").isEmpty() && obj.getString("TASK_TYPE").equals("EXAM")) {
                obj.put("TASK_TYPE", "测验");
            } else {
                obj.put("TASK_TYPE", "陪练");
            }
            if (obj.getString("TRAINING_RESULT") != null && !obj.getString("TRAINING_RESULT").isEmpty() && obj.getString("TRAINING_RESULT").equals("PASS")) {
                obj.put("TRAINING_RESULT", "及格");
            } else if (obj.getString("TRAINING_RESULT") != null && !obj.getString("TRAINING_RESULT").isEmpty() && obj.getString("TRAINING_RESULT").equals("FLUNK")){
                obj.put("TRAINING_RESULT", "不及格");
            }
            if (obj.getString("TASK_STATUS") != null && !obj.getString("TASK_STATUS").isEmpty() && obj.getString("TASK_STATUS").equals("DONE")) {
                obj.put("TASK_STATUS", "已完成");
            } else if (obj.getString("TASK_STATUS") != null && !obj.getString("TASK_STATUS").isEmpty() && obj.getString("TASK_STATUS").equals("UNDONE")){
                obj.put("TASK_STATUS", "未完成");
            }
            //判断坐席是否可以进行考试（在考试时间内，并且考试是有效的，并且坐席未完成考试）
            String startTimeStr = obj.getString("EXAM_START_TIME");
            String endTimeStr = obj.getString("EXAM_END_TIME");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime startDateTime = LocalDateTime.parse(startTimeStr, formatter);
            LocalDateTime endDateTime = LocalDateTime.parse(endTimeStr, formatter);
            LocalDateTime now = LocalDateTime.now();
            boolean isEnableTime = now.isAfter(startDateTime) && now.isBefore(endDateTime);
            if (!isEnableTime) {
                obj.put("enableCheck", 0);
            } else {
                obj.put("enableCheck", 1);
            }
            jsonArray.set(i, obj);
        }
        result.put("data", jsonArray);
        return result;
    }

    /**
     * 通过taskId查询人员列表
     */
    @WebControl(name = "getUserInfoByTaskId", type = Types.LIST)
    public JSONObject getUserInfoByTaskId() {
        EasySQL sql = this.getEasySQL(" select TRAINING_USER_ID, TRAINING_USER_NAME from C_AI_TRAINING_USER ");
        sql.append("WHERE 1=1 ");
        sql.append(param.getString("taskId"), "and TASK_ID = ? ");
        logger.info("通过taskId查询人员列表： " + sql.getSQL() + ";" + Arrays.toString(sql.getParams()));
        return this.queryForPageList(sql.getSQL(), sql.getParams(), null);
    }
}
