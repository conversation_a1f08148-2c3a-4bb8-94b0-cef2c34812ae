package com.yunqu.cc.aiTraining.dao;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.TreeNode;
import com.yq.busi.common.user.DeptMgr;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.aiTraining.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import java.util.ArrayList;

@WebObject(name="user")

public class UserDao extends AppDaoContext {
	 

	@WebControl(name = "getDeptZtree", type = Types.TREE)
	public JSONObject getUsersByDeptCode() {
		String depCode = UserUtil.getUser(this.request).getEpCode();
		TreeNode usersByDeptCode = DeptMgr.getDeptTreeNode( new ArrayList<TreeNode>(), depCode,true,false,true);
		JSONObject jsonObject = JsonUtil.toJSONObject(usersByDeptCode);
		return getTree(jsonObject);
	}
	@WebControl(name = "getUser", type = Types.LIST)
	public JSONObject getUser() {
		EasySQL sql = this.getEasySQL("select  DISTINCT ru.user_id,ru.username,ed.dept_id,eul.user_acct,BAKUP ");
		sql.append(" from mars.EASI_USER  ru left  join  mars.Easi_Dept_User edu on ru.user_id= edu.user_id   left  join mars.EASI_dept ed  on edu.dept_id=ed.dept_id  left join mars.easi_user_login eul on ru.user_id=eul.user_id ");
		sql.append(" left join (SELECT  T.BAKUP,C_YG_EMPLOYEE.USER_ACC FROM  C_YG_EMPLOYEE ");//
		sql.append(" ,(select BAKUP,code from  C_CF_DICT  where DICT_GROUP_ID=(select  id  from  C_CF_DICTGROUP where  code='EMP_STAR' and ENABLE_STATUS='Y') ) t  ");//
		sql.append(" where C_YG_EMPLOYEE.STAR=T.code AND C_YG_EMPLOYEE.STATUS !='04' ) TA  on eul.user_acct=TA.USER_ACC  ");//已离职
		sql.append(" where  ru.state!='9' ");
		sql.appendLike(param.getString("userInfo"), " and ( ru.username like ? or eul.user_acct like ? )");
		if(!"".equals(param.getString("dept_id"))){
			String[] deptIds = param.getString("dept_id").split( ","); 
			if(deptIds.length>0){
				sql.append( "  and   DEPT_CODE in ("+param.getString("dept_id")+")");
			}
		}else{
			sql.appendRLike(param.getString("dept_id"), "  and   DEPT_CODE LIKE (?)");
		}

		String depCode = UserUtil.getUser(this.request).getEpCode();
		sql.appendRLike(depCode, " and  DEPT_CODE LIKE ?");
		
		return this.queryForList(sql.getSQL(), sql.getParams(), null);
	}
	
	 

}
