package com.yunqu.cc.aiTraining.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yunqu.cc.aiTraining.base.CommonLogger;
import com.yunqu.cc.aiTraining.enums.ErrorBack;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public class CsUtil {

	private static EasyCache cache = CacheManager.getMemcache();
	private static Logger logger = CommonLogger.logger;
		
	/**
	 * IService返回格式化
	 * @param data 原始数据
	 * @param objs key-value 数组
	 * @return
	 */
	public static JSONObject getData(JSONObject data,Types type,String msg){
		String respCode = data.getString("respCode");
		if(GWConstants.RET_CODE_SUCCESS.equals(respCode)){
			//System.out.println("返回数据="+data.getJSONObject("respData"));
			JSONObject o =data.getJSONObject("respData");
			o.put("msg", msg);
			o.put("state", Integer.valueOf(1));
			o.put("pageType", Integer.valueOf(1));
			o.put("pageNumber", o.get("pageIndex"));
			o.put("type", type);
			return o;
		}else {
			switch(type){
			case LIST:
				data = JSONObject.parseObject(ErrorBack.OK_LIST.getValue());
				break;
			case RECORD:
				data = JSONObject.parseObject(ErrorBack.OK_RECORD.getValue());
				break;
			case DICT:
				data = JSONObject.parseObject(ErrorBack.OK_DISC.getValue());
				break;
			default:
				break;
			}
			
		}
		return data;
	}
	
	public static JSONObject getData(JSONObject data){
		return getData(data,Types.DICT);
	}
	public static JSONObject getData(JSONObject data,Types type){
		return getData(data,type,"\u8BF7\u6C42\u6210\u529F!");
	}
	
	public static JSONObject getDataTotal(JSONObject data){
		String respCode = data.getString("respCode");
		if(GWConstants.RET_CODE_SUCCESS.equals(respCode)){
			//System.out.println("返回数据="+data.getJSONObject("respData"));
			JSONObject o =new JSONObject();
			o.put("total", data.getJSONObject("respData").getString("total"));
			o.put("msg", "\u8BF7\u6C42\u6210\u529F!");
			o.put("state", Integer.valueOf(1));
			return o;
		}
		return data;
	}
	
	/**
	 * IService返回树取值
	 * @param data 原始数据
	 * @param objs key-value 数组
	 * @return
	 */
	public static JSONArray getTreeData(JSONObject data){
		String respCode = data.getString("respCode");
		if(GWConstants.RET_CODE_SUCCESS.equals(respCode)){
			//System.out.println(data.getJSONObject("respData"));
			return data.getJSONObject("respData").getJSONArray("data");
		}
		return new JSONArray();
	}
	
	/**
	 * 格式化服务请求的返回数据
	 * @param obj
	 */
	public static void formatServiceRequire(JSONObject obj,boolean isParent){
		JSONArray arr = obj.getJSONArray("data");
		JSONArray resArr = new JSONArray();
		List<Object> check = new ArrayList<Object>();
		for(int i=0;i<arr.size();i++){
			JSONObject ztree = new JSONObject();
			Object id = arr.getJSONObject(i).get("serviceRequireItemCode");
			if(check.contains(id)){
				continue;
			}else{
				check.add(id);
				ztree.put("id", id);
				ztree.put("name", arr.getJSONObject(i).get("serviceRequireItemName"));
				ztree.put("pId", arr.getJSONObject(i).get("parentServiceRequireCode"));
				ztree.put("isParent", isParent);
				resArr.add(ztree);
			}
		}
		obj.put("totalRow", resArr.size());
		obj.put("total", resArr.size());
		obj.put("data", resArr);
	}
	
	/**
	 * 接入单列表查询请求体删除多余数据
	 * @param obj
	 * @return
	 */
	public static JSONObject deleteBase(JSONObject obj){
		if(obj!=null){
			obj.remove("container");
			obj.remove("template");
			obj.remove("mars");
			obj.remove("autoFill");
			obj.remove("toggle");
			obj.remove("marsDone");
			obj.remove("data-mars");
			obj.remove("param[0]");
			obj.remove("textModel");
		}
		return obj;
	}
	
	/**
	 * 检查售后接口是否可以访问
	 * @param cacheName 用于缓存的接口名
	 * @param userAcc 坐席账号
	 * @param allowNumber 允许访问次数
	 * @return
	 */
	public static boolean isAllowToAccessCSSInterface(String cacheName,String userAcc,int allowNumber) {
		String key = cacheName+userAcc+"checkNum";
		Integer currentNum = cache.get(key);
		if(currentNum==null||currentNum==0) {
			currentNum = 1;
		}else {
			if(currentNum > allowNumber) {
				return false;
			}
			currentNum++;
		}
		//获取当前时间到明天0点时长
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_YEAR, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		int second = (int)(calendar.getTimeInMillis() - System.currentTimeMillis()) / 1000;
		cache.put(key, currentNum, second);
		return true;
	}
	
	public static JSONObject formatData(String data){
		return formatData(data,"list","total",false);
	}
	
	public static JSONObject formatData(String data,boolean pageOpen){
		return formatData(data,"list","total",pageOpen);
	}
	
	public static JSONObject formatData(String data,String listName){
		return formatData(data,listName,"total",false);
	}
	
	public static JSONObject formatData(String data,String listName,boolean pageOpen){
		return formatData(data,listName,"total",pageOpen);
	}
	public static JSONObject formatData(String data,String listName,String totalName) {
		return formatData(data,listName,totalName,false);
	}
	/**
	 * 格式化售后接口返回数据
	 * @param data 返回数据体
	 * @param listName 集合对象名
	 * @param totalName 总数对象名
	 * @param pageOpen 是否分页，分页取totalName的值，不分页取集合的size
	 * @return
	 */
	public static JSONObject formatData(String data,String listName,String totalName,boolean pageOpen) {
		JSONObject result = JSONObject.parseObject(data);
		boolean status = result.getBoolean("status");
		if (status) {
			JSONArray arr = result.getJSONArray(listName);
			result.put("data", arr);
			result.put("totalRow", pageOpen ?result.getString(totalName) : arr.size());
			result.put("totalPage", result.getString("pageCount"));
			result.remove("list");
			result.remove(listName);
			result.remove("pageCount");
			result.remove("errorCode");
			result.remove("errorMsg");
			result.remove("status");
			result.remove("pageSizeCur");
			return result;
		} else {
			result.clear();
			result.put("totalRow", 0);
			result.put("totalPage", 1);
			result.put("total", 0);
			result.put("data", new JSONArray());
			return result;
		}
	}
	
	
	
	public static JSONObject contactOrderListTab(JSONObject j1) {
		JSONObject result = new JSONObject();
		result.put("msg", "\u8BF7\u6C42\u6210\u529F!");

		JSONArray list1 = j1.getJSONArray("list");
		JSONArray dataResult = new JSONArray();
		if(list1!=null&&list1.size()>0) {
			dataResult = orderFormat(list1,dataResult);
		}
		result.put("data", dataResult);
		result.put("state", 1);
		return result;
	}
	
	private static JSONArray orderFormat(JSONArray arr,JSONArray j) {
		for(int i = 0; i < arr.size(); i++) {
			JSONObject singleObj = new JSONObject();
			JSONObject contactOrderVO = arr.getJSONObject(i).getJSONObject("contactOrderVO");
			JSONArray contactUserRequireVOList = arr.getJSONObject(i).getJSONArray("contactUserRequireVOList");
			JSONObject contactUserRequireVO = contactUserRequireVOList.getJSONObject(0);
			singleObj.put("contactOrderCode", contactOrderVO.getString("contactOrderCode"));//接入单号
			singleObj.put("contactPubCreateDate", StringUtil.formatTimestamp(contactOrderVO.getString("pubCreateDate")));//创建时间
			singleObj.put("customerMobilephone1", contactOrderVO.getString("customerMobilephone1"));//用户号码
			singleObj.put("customerName", contactOrderVO.getString("customerName"));//用户姓名
			singleObj.put("areaNum", contactOrderVO.getString("areaNum"));//区号
			singleObj.put("customerAddress", contactOrderVO.getString("customerAddress"));//用户地址
			singleObj.put("contactOrderStatus", contactOrderVO.getString("contactOrderStatus"));//接入单状态
			singleObj.put("sourceOrderCode", contactOrderVO.getString("sourceOrderCode"));//来源订单号
			singleObj.put("disposeType", contactUserRequireVO.getString("disposeType"));//处理方式
			singleObj.put("processDesc", contactOrderVO.getString("processDesc"));//处理结果
			singleObj.put("contactTypeCode", contactOrderVO.getString("contactTypeCode"));//接入方式
			singleObj.put("orgCode", contactUserRequireVO.getString("orgCode"));//主体
			singleObj.put("branchName", contactOrderVO.getString("branchName"));//分中心
			singleObj.put("brandName", contactUserRequireVO.getString("brandName"));//产品品牌
			singleObj.put("prodName", contactUserRequireVO.getString("prodName"));//产品品类
			
			singleObj.put("pubCreatePerson", contactUserRequireVO.getString("pubCreatePerson"));//诉求受理人
			singleObj.put("pubModiPerson", contactUserRequireVO.getString("pubModiPerson"));//诉求处理人
			
			singleObj.put("pubCreateDate", StringUtil.formatTimestamp(contactUserRequireVO.getString("pubCreateDate")));//诉求创建时间
			singleObj.put("contactOrderServTypeName", contactUserRequireVO.getString("contactOrderServTypeName"));//服务请求类别
			singleObj.put("contactOrderSerItem", contactUserRequireVO.getString("contactOrderSerItem1Name")+"-"+contactUserRequireVO.getString("contactOrderSerItem2Name"));//服务请求项目
			singleObj.put("contactOrderServiceDesc", contactUserRequireVO.getString("contactOrderServiceDesc"));//服务请求描述
			singleObj.put("pubRemark", contactUserRequireVO.getString("pubRemark"));//备注
			singleObj.put("turnWebDepict", contactUserRequireVO.getString("turnWebDepict"));//备注
			singleObj.put("complaintLevel", contactUserRequireVO.getString("complaintLevel"));//投诉等级
			
			singleObj.put("feedbackNo", contactUserRequireVO.get("feedbackNo"));
			singleObj.put("flowStatus", contactUserRequireVO.get("flowStatus"));
			singleObj.put("backSeatFeedback", contactUserRequireVO.get("backSeatFeedback"));
			singleObj.put("busiDepartmentFeedback", contactUserRequireVO.get("busiDepartmentFeedback"));
			singleObj.put("closeFlag", contactUserRequireVO.get("closeFlag"));
			singleObj.put("closeDesc", contactUserRequireVO.get("closeDesc"));
			j.add(singleObj);
		}
		return j;
	}
	
}
