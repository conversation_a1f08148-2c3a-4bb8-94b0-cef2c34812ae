package com.yunqu.cc.aiTraining.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.aiTraining.enums.DatePattern;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.utils.kit.RandomKit;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class StringUtil {
	/**
	 * 根据输入流判断字符集
	 * @param is
	 * @return
	 */
	public static String checkCharSet(int temp){
		String charset = null; 
		if (temp == 0xefbb) {
			charset = "UTF-8";
	    } else if (temp == 0xfffe) {
	    	charset = "Unicode";
	    } else if (temp == 0xfeff) {
	    	charset = "UTF-16BE";
	    } else {
	    	charset = "GBK";
	    }
		return charset;
	}
	/**
	 * 生成ID
	 * @param num ID长度 ，>24
	 * @return
	 */
	public static String getId(int num){
		return RandomKit.next()+RandomKit.smsAuthCode(num-20);
	}
	/**
	 * 判断是否非空
	 * @param str
	 * @return
	 */
	public static boolean checkNull(String str){
		if(str==null || "".equals(str)){
			return true;
		}else{
			return false;
		}
	}
	
	/**
	 * 从JSONObject 中获取值
	 * @return
	 */
	public static String getValueFromMap(JSONObject obj ,String key){
		String value = "";
		if(obj!=null){
			value = obj.getString(key);
		}
		if(checkNull(value)){
			value = key;;
		}
		return value;
	}
	/**
	 * 格式化空值
	 * @param str
	 * @return
	 */
	public static String formatNull(String str,String defaultValue){
		if(str==null ||"null".equals(str) ||"undefined".equals(str)){
			return defaultValue;
		}
		return str.replaceAll(" ", "");
	}
	
	/**
	 * 格式化空值
	 * @param str
	 * @return
	 */
	public static String formatNull(Object str,String defaultValue){
		if(str==null ||"null".equals(str) ||"undefined".equals(str)){
			return defaultValue;
		}
		return str.toString().replaceAll(" ", "");
	}

	
	/**
	 * 格式化空值
	 * @param str
	 * @return
	 */
	public static String formatNull(String str){
		return formatNull(str,"");
	}
	
	/**
	 * 格式化数据库空值
	 * @param str
	 * @return
	 */
	public static String formatDBNull(Object obj,String defaultValue){
		if(obj==null ||"null".equals(obj) ||"undefined".equals(obj)){
			return defaultValue;
		}
		return obj.toString();
	}
	
	/**
	 * 格式化数据库空值
	 * @param str
	 * @return
	 */
	public static String formatDBNull(Object obj){
		return formatDBNull(obj,"");
	}
	/**
	 * 格式化数据库布尔型
	 * @param str
	 * @return
	 */
	public static String formatDBBoolean(Object str){
		if("true".equals(str) || "Y".equals(str)){
			return "1";
		}
		return "0";
	}
	/**
	 * 格式化时间戳
	 * @param timestamp
	 * @return
	 */
	public static String formatTimestamp(String timestamp,String pattern){
		if(timestamp != null && !"".equals(timestamp)){
			return data2Str(new Date(Long.parseLong(timestamp)));
		}
		return "";
	}
	
	public static String formatTimestamp(String timestamp){
		if(timestamp != null && !"".equals(timestamp)){
			return data2Str(new Date(Long.parseLong(timestamp)),"yyyy-MM-dd HH:mm:ss");
		}
		return "";
	}
	/**
	 * 时间转化为字符串
	 * @param date
	 * @param pattern
	 * @return
	 */
	public static String data2Str(Date date,String pattern){
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
		return sdf.format(date);
	}
	
	public static String data2Str(Date date){
		return data2Str(date,DatePattern.PAT_DATETIME1.getPattern());
	}
	
	/**
	 * 短信封装
	 * @param value
	 * @param separator
	 * @return
	 */
	public static List<JSONObject> spiltByRegex(String value ,String regex,String content){
		List<JSONObject> list = new ArrayList<JSONObject>();
		if(!checkNull(value) && !checkNull(regex)){
			String[] str = value.split(regex);
			for(String s : str){
				JSONObject obj = new JSONObject();
				obj.put("receiver", s);
				obj.put("content", content);
				obj.put("userType", "");
				list.add(obj);
			}
		}
		return list;
	}
	
	 /**
     * 判断字符是否是数字
     * @param str
     * @return
     */
	public static boolean isNumeric(String str){ 
		if (isNull(str)) {
			return false; 
		}
	    Pattern pattern = Pattern.compile("[0-9]*"); 
	    Matcher isNum = pattern.matcher(str);
	    if( !isNum.matches() ){
	       return false; 
	    } 
	    return true; 
	}
	public static Integer getInt(String str) {
	       try {
	           return Integer.valueOf(Integer.parseInt(str.trim()));
	       } catch (Exception var1) {
	           return 0;
	       }
	}
    public static Double getDouble(String str) {
           try {
               return Double.valueOf(Double.parseDouble(str.trim()));
           } catch (Exception var1) {
               return 0.0;
           }
     }
    public static Float getFloat(String str) {
           try {
               return Float.valueOf(Float.parseFloat(str.trim()));
           } catch (Exception var1) {
               return (float) 0.00;
           }
    }

     /**
      * 判断是否空值
      * @param object
      * @return
      */
    public static boolean isNull(Object object) {
           return null == object || "".equals(object.toString().trim()) || "null".equals(object.toString());
    }
    
	public static String joinStr(JSONArray array){
		String str = "";
		if (array != null) {
			String[] codeKey = array.toArray(new String[array.size()]);
			str = StringUtils.join(codeKey, ",");
		}
		return str;
	}
	public static String joinStr(String[] array){
		String str = "";
		if (array != null) {
			str = StringUtils.join(array, ",");
		}
		return str;
	}
	
	public static String joinSqlStr(JSONArray array){
		String str = "";
		if (array != null && array.size() > 0) {
			for(int i = 0 ; i< array.size() ; i++){
				str += "'" + array.getString(i) + "',";
			}
			if(!"".equals(str)) {
				str = str.substring(0,str.length()-1);
			}
		}
		return str;
	}
	
	public static String joinSqlStr(String[]  array){
		String str = "";
		if (array != null && array.length > 0) {
			for(int i = 0 ; i< array.length ; i++){
				if( !array[i].equals("")){
					str += "'" + array[i] + "',";

				}
			}
			if(!"".equals(str)) {
				str = str.substring(0,str.length()-1);
			}
		}
		return str;
	}
	
    @SuppressWarnings("rawtypes")
	public static void addPrefix(JSONObject json,String prefix){
 		   JSONObject dataJson=json.getJSONObject("data");
           Iterator iterator=dataJson.keySet().iterator();
           JSONObject newDataJson=new JSONObject();
	 	   while(iterator.hasNext()){
	 		    String key = (String) iterator.next();
	 		    String value = dataJson.getString(key);
	 		    newDataJson.put(prefix+"."+key, formatNull(value));
	 	   }
 		   json.put("data", newDataJson);
 	}

	public static boolean isInteger(String str) {
		Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
		return pattern.matcher(str).matches();
	}


}
