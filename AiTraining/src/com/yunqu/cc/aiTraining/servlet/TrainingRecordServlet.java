package com.yunqu.cc.aiTraining.servlet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.OrderCommand;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.aiTraining.base.AppBaseServlet;
import com.yunqu.cc.aiTraining.base.CommonLogger;
import com.yunqu.cc.aiTraining.base.Constants;
import com.yunqu.cc.aiTraining.css.ExportBase;
import com.yunqu.cc.aiTraining.css.TrainingRecordExport;
import com.yunqu.cc.aiTraining.enums.DatePattern;
import com.yunqu.cc.aiTraining.util.CsUtil;
import com.yunqu.cc.aiTraining.util.StringUtil;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.easitline.common.annotation.Types;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.Part;
import java.io.File;
import java.io.PrintWriter;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

@SuppressWarnings("serial")
@MultipartConfig
@WebServlet("/training/record/*")
public class TrainingRecordServlet extends AppBaseServlet {

    Logger logger = new CommonLogger().getLogger();

    public String actionForList() {
        return "/pages/training/training-list.jsp";
    }

    public String actionForTrainingPublish() {
        int day = ConfigUtil.getInt(Constants.APP_NAME, "DISTRIBUTION_TIME", 3);
        this.setAttr("distributionTime", day);
        return "/pages/training/training-publish.jsp";
    }

    public String actionForImportData() {
        return "/pages/training/publisher-import.jsp";
    }


    public JSONObject actionForPublishSave() {
        JSONObject requestData = getJSONObject();
        logger.info("publishSave入参：" + requestData.toJSONString());
        EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
        if (!requestData.containsKey("taskId")) {
            try {
                logger.info("publishSave入参：" + requestData.toJSONString());
                // 从requestData中提取任务相关参数，并获取当前用户的账号和姓名信息
                String taskName = requestData.getString("taskName");
                String taskPublishTime = EasyDate.getCurrentDateString();
                String examPaperId = requestData.getString("examPaperId");
                String examPaperName = requestData.getString("examPaperName");
                String publisherAcc = UserUtil.getUser(this.getRequest()).getUserAcc(); // 获取发布人账号
                String publisherName = UserUtil.getUser(this.getRequest()).getUserName(); // 获取发布人姓名
                String examStartTime = requestData.getString("examPublishStartTime");
                String examEndTime = requestData.getString("examPublishEndTime");
                String taskType = requestData.getString("taskType");
                String needReference = requestData.getString("needReference");
                String responseMode = requestData.getString("responseMode");
                String passScore = requestData.getString("passScore");
                String examDuration = requestData.getString("examDuration");
                String taskReminderTime = requestData.getString("customReminderTime");
                String taskGenerationMethod = requestData.getString("examPaperGenerateType");
                String trainingTotal = requestData.getString("telcount");
                String createAcc = UserUtil.getUser(this.getRequest()).getUserAcc(); // 获取创建人账号
                String createTime = EasyDate.getCurrentDateString(); // 获取当前时间作为创建时间
                String createDept = UserUtil.getUser(this.getRequest()).getDeptName(); // 获取创建人所属部门
                String[] trainingUserIds = requestData.getString("user_acc").split(",");
                String[] trainingUserNames = requestData.getString("user_name").split(",");

                //推送数据
                Map<String, Object> param = new HashMap<String, Object>();
                param.put("taskName", taskName);
                param.put("examPaperId", examPaperId);
                param.put("taskStartTime", examStartTime);
                param.put("taskEndTime", examEndTime);
                param.put("taskType", taskType);
                param.put("lineCode", "CC");
                param.put("studentMipList", trainingUserIds);
                param.put("examDuration", examDuration);
                param.put("needReference", needReference);
                param.put("responseMode", responseMode);
                JSONObject requestJson = new JSONObject();
                requestJson.put("command", "insertTraining");
                requestJson.put("data", param);
                IService service = ServiceContext.getService("MIXGW_IOP_INTERFACE");
                logger.info("开始推送数据：" + requestJson.toJSONString());
                JSONObject responseJson = service.invoke(requestJson);
                //推送成功则返回成功，反之则回滚
                if (responseJson.getString("respCode").equals("000")) {
                    logger.info("推送成功, 开始存数据库");
                    //存数据库
                    EasyRecord record = new EasyRecord("C_AI_TRAINING_RECORD");
                    String taskId = responseJson.getJSONObject("respData").getJSONObject("data").getString("taskId");
                    String taskUrl = responseJson.getJSONObject("respData").getJSONObject("data").getString("taskUrl");
                    record.set("TASK_ID", taskId);
                    record.set("TASK_NAME", taskName);
                    record.set("TASK_PUBLISH_TIME", taskPublishTime);
                    record.set("TEST_ID", examPaperId);
                    record.set("TEST_NAME", examPaperName);
                    record.set("PUBLISHER_ACC", publisherAcc);
                    record.set("PUBLISHER_NAME", publisherName);
                    record.set("EXAM_START_TIME", examStartTime);
                    record.set("EXAM_END_TIME", examEndTime);
                    record.set("TASK_TYPE", taskType);
                    record.set("PASS_SCORE", passScore);
                    record.set("TEST_DURATION", examDuration);
                    record.set("TASK_REMINDER_TIME", taskReminderTime);
                    record.set("TASK_GENERATION_METHOD", taskGenerationMethod);
                    record.set("TRAINING_TOTAL", trainingTotal);
                    record.set("CREATE_ACC", createAcc);
                    record.set("CREATE_TIME", createTime);
                    record.set("CREATE_DEPT", createDept);
                    record.set("TASK_URL", taskUrl);
                    record.set("ENABLE_STATUS", "ENABLE");
                    logger.info("开始保存陪练任务进数据库，数据：" + record.toJSONString());
                    if (query.save(record)) {
                        logger.info("陪练任务保存成功, 开始保存人员任务数据表");
                        for (int i = 0; i < trainingUserIds.length; i++) {
                            EasyRecord record1 = new EasyRecord("C_AI_TRAINING_USER");
                            record1.set("ID", RandomKit.randomStr());
                            record1.set("TASK_ID", taskId);
                            record1.set("TASK_NAME", taskName);
                            record1.set("TRAINING_USER_ID", trainingUserIds[i]);
                            record1.set("TRAINING_USER_NAME", trainingUserNames[i]);
                            record1.set("TASK_STATUS", "UNDONE");
                            record1.set("CREATE_ACC", createAcc);
                            record1.set("CREATE_TIME", createTime);
                            record1.set("CREATE_DEPT", createDept);
                            logger.info("开始保存人员任务数据表，数据：" + record1.toJSONString());
                            if (query.save(record1)) {
                                logger.info("人员任务数据表保存成功");
                            } else {
                                logger.info("人员任务数据表保存失败");
                            }
                        }
                        Logger jobsLogger = LogEngine.getLogger(Constants.APP_NAME, "AiTraining_jobs");
                        jobsLogger.info("开始推送任务消息给用户》》》");
                        String targetUserAcc = String.join(";", trainingUserIds);
                        jobsLogger.info("用户》》》" + targetUserAcc);
                        IService noticeService = ServiceContext.getService(ServiceID.NOTICE_INTERFACE);
                        jobsLogger.info("测试》》》");
                        JSONObject obj = new JSONObject();
                        obj.put("sender", "notice");
                        obj.put("password", "YQ_85521717");
                        obj.put("serialId", IDGenerator.getDefaultNUMID());
                        obj.put("command", ServiceCommand.NOTICE_ADD_USER_NOTICE); // serviceId:  ServiceID.NOTICE_INTERFACE;
                        obj.put("receiverType", "01");//接收类型 01-个人  02-	部门   03-	所有人
                        obj.put("userAcc", targetUserAcc);//接收通知的人员，如有多个，用;隔开
                        obj.put("deptCode", "000");//
                        obj.put("createUserAcc", "admin@mars");//
                        obj.put("createUserDeptCode", "000");//
                        obj.put("type", "你有一条新的AI陪练任务，请查看");
                        obj.put("module", Constants.APP_NAME);
                        obj.put("title", "提醒通知");
                        obj.put("content", "您有一条提醒通知:你有一条新的AI陪练任务，请查看");
                        obj.put("url", "/AiTraining/trainingUser/record?action=trainingUserList");
                        obj.put("method", "01");
                        jobsLogger.info("AI陪练提醒消息：" + obj.toJSONString());
                        JSONObject result = noticeService.invoke(obj);
                        jobsLogger.info("AI陪练提醒消息返回值:" + result.toJSONString());
                        return EasyResult.ok("新增成功");
                    } else {
                        logger.info("人员任务数据表保存失败");
                        return EasyResult.error(500, "新增失败");
                    }
                } else {
                    logger.info("推送失败");
                    return EasyResult.error(500, responseJson.getString("respDesc"));
                }
            } catch (SQLException | ServiceException e) {
                logger.info(e);
                throw new RuntimeException(e);
            }
        }
        return EasyResult.error(500, "新增失败");
    }


    public JSONObject actionForChangeTaskStatus() {
        JSONObject requestData = getJSONObject();
        EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
        try {
            if (requestData.getString("taskId") != null) {
                String taskId = requestData.getString("taskId");
                String enableStatus = requestData.getString("enableStatus").equals("启用") ? "DISABLE" : "ENABLE";
                Map<String, Object> param = new HashMap<String, Object>();
                param.put("taskId", taskId);
                param.put("activeStatus", enableStatus);
                JSONObject requestJson = new JSONObject();
                requestJson.put("command", "changeTaskStatus");
                requestJson.put("data", param);
                IService service = ServiceContext.getService("MIXGW_IOP_INTERFACE");
                service.invoke(requestJson);
                if (query.update(new EasyRecord("C_AI_TRAINING_RECORD", "TASK_ID").set("ENABLE_STATUS", enableStatus).set("TASK_ID", taskId))) {
                    return EasyResult.ok("修改成功");
                } else {
                    return EasyResult.error(500, "修改失败");
                }
            }
        } catch (SQLException | ServiceException e) {
            throw new RuntimeException(e);
        }
        return EasyResult.error(500, "修改失败");
    }

    /**
     * AI陪练任务导出
     */
    public void actionForConstactExport() {
        HttpServletResponse response = this.getResponse();
        String status = this.getRequest().getParameter("contactOrderStatus");
        EasySQL sql = new EasySQL(" select * FROM C_AI_TRAINING_RECORD WHERE 1=1 ");
        sql.appendLike(this.getRequest().getParameter("taskName"), "and TASK_NAME like ? ");
        sql.append(this.getRequest().getParameter("taskType"), "and TASK_TYPE = ? ");
        sql.append(this.getRequest().getParameter("publishAcc"), "and publishAcc = ? ");
        sql.append(this.getRequest().getParameter("activeStatus"), "and ENABLE_STATUS = ? ");
        sql.append(this.getRequest().getParameter("publishStartTime"), " and TASK_PUBLISH_TIME >= ? ");
        sql.append(this.getRequest().getParameter("publishEndTime"), " and TASK_PUBLISH_TIME <= ? ");
        sql.append(this.getRequest().getParameter("examStartTime"), " and EXAM_END_TIME >= ? ");
        sql.append(this.getRequest().getParameter("examEndTime"), " and EXAM_START_TIME <= ? ");
        sql.append(" ORDER BY CREATE_TIME desc ");
        logger.info(sql.getSQL() + ";" + Arrays.toString(sql.getParams()));
        try {
            List<EasyRow> list = getQuery().queryForList(sql.getSQL(), sql.getParams());
            logger.info(list.get(0).toJSONObject().toJSONString());
            JSONArray jsonArray = new JSONArray();
            for (EasyRow row : list) {
                jsonArray.add(row.toJSONObject());
            }
            logger.info(jsonArray.toJSONString());
            String fileName = "TRAINING_RECORD_" + StringUtil.data2Str(new Date(), DatePattern.PAT_DATE1.getPattern()) + ".xlsx";
            ExportBase export = new TrainingRecordExport();
            XSSFWorkbook book = export.exportExcel(jsonArray);
            downExcel(response, book, fileName);
        } catch (Exception e) {
            logger.error("导出失败, 原因: " + e.getMessage(), e);
            sendResponse("<script>alert('导出数据失败，请重新导出！')</script>", response);
        }
    }

    /**
     * 下载文件
     *
     * @param response
     * @param workbook
     * @param fileName
     */
    private void downExcel(HttpServletResponse response, XSSFWorkbook workbook, String fileName) {
        //设置文件ContentType类型,该设置会自动判断下载文件类型
        response.setContentType("application/vnd.ms-excel");
        //设置文件头
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
        try {
            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
            out.close();
            out.flush();
        } catch (Exception e1) {
            logger.error("往输出流中写文件失败：" + e1.getMessage());
            e1.printStackTrace();
        }

    }

    /**
     * 出流中输出对象
     *
     * @throws Exception
     */
    private void sendResponse(String str, HttpServletResponse response) {
        PrintWriter out = null;
        // 设置页面不缓存
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Cache-Control", "no-store");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);
        response.setContentType("application/json; charset=utf-8");
        try {
            String returnStr = "";
            if (str != null) {
                returnStr = str;
            }
            out = response.getWriter();
            out.write(returnStr);
            out.flush();
        } catch (Exception e) {
            logger.error("接口返回错误 -->"
                    + e.getMessage(), e);
            logger.error(e);
        } finally {
            if (str != null) {
                out.close();
            }
        }
    }

    public void actionForDownload() {
        File file = new File(this.getRequest().getServletContext().getRealPath("/pages/template/publisher.xlsx"));
        renderFile(file, "人员导入模板.xlsx");
    }

    public EasyResult actionForUpload() {
        EasyQuery query = this.getQuery();
        try {
            query.begin();
            Part part = getFile("file");
            Workbook workbook = WorkbookFactory.create(part.getInputStream());
            List<String> list = new ArrayList<>();
            Sheet sheet = workbook.getSheetAt(0);
            int maxLine = sheet.getLastRowNum();
            logger.info("最大行数：" + maxLine);
            for (int i = 1; i <= maxLine; i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    Cell cell = row.getCell(0);
                    if (cell != null) {
                        logger.info("第" + i + "行：" + row.getCell(0).getStringCellValue());
                        list.add(cell.getStringCellValue());
                    }
                }
            }
            boolean scuuess = true;// 是否正确
            String msg = "";
            if (list.isEmpty()) {
                scuuess = false;
                msg = "无导入信息";
            }
            if (!scuuess) {// 出错了不记录
                query.roolback();
                return EasyResult.error(500, "" + msg);
            } else {
                String userAcct = "";
                for (String s : list) {
                    userAcct = "".equals(userAcct) ? "'" + s + "'" : userAcct + ",'" + s + "'";
                }
                List<EasyRow> easyRows = new ArrayList<EasyRow>();
                StringBuffer sbsql = new StringBuffer();
                JSONObject result = new JSONObject();
                try {
                    String replacedWsercodes = userAcct;
                    String[] strs = replacedWsercodes.split(",");
                    List list1 = Arrays.asList(strs);
                    StringBuffer sqlString = new StringBuffer();
                    for (int i = 0; i < list1.size(); i++) {
                        if (i == (list1.size() - 1)) {
                            sqlString.append(list1.get(i)); //SQL拼装，最后一条不加“,”。
                        } else if ((i % 999) == 0 && i > 0) {
                            sqlString.append(list1.get(i)).append(") or EUL.USER_ACCT  in ("); //解决ORA-01795问题
                        } else {
                            sqlString.append(list1.get(i)).append(",");
                        }
                    }
                    logger.info(sqlString);
                    easyRows = query.queryForList("SELECT DISTINCT  EUL.USER_ACCT,EU.USERNAME   FROM mars.EASI_USER EU  LEFT JOIN mars.EASI_USER_LOGIN EUL ON EU.USER_ID=EUL.USER_ID WHERE EUL.USER_ACCT IN (" + sqlString.toString() + ") ", null);
                    StringBuilder userAcc = new StringBuilder();
                    StringBuilder userName = new StringBuilder();
                    for (EasyRow row : easyRows) {
                        logger.info("用户名：" + row.getColumnValue("USERNAME") + "，用户账号：" + row.getColumnValue("USER_ACCT"));
                        logger.info("用户名：" + userName + "，用户账号：" + userAcc);
                        userAcc.append(userAcc.length() == 0 ? row.getColumnValue("USER_ACCT") : "," + row.getColumnValue("USER_ACCT"));
                        userName.append(userName.length() == 0 ? row.getColumnValue("USERNAME") + "(" + row.getColumnValue("USER_ACCT") + ")" : "," + row.getColumnValue("USERNAME") + "(" + row.getColumnValue("USER_ACCT") + ")" );
                    }
                    result.put("userAcc", userAcc.toString());
                    result.put("userName", userName.toString());
                    result.put("telcount", easyRows.size());
                } catch (SQLException e) {
                    logger.error(e);
                }
                query.commit();
                return EasyResult.ok(result, "人员导入成功！");
            }
        } catch (Exception e) {
            try {
                query.roolback();
            } catch (SQLException e1) {
                logger.error("导入失败，原因：" + e1.getMessage(), e1);
            }
            logger.error("导入失败，原因：" + e.getMessage(), e);
            return EasyResult.error(500, "人员导入失败！" + e);
        }
    }

}
