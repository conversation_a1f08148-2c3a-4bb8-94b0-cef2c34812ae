package com.yunqu.cc.aiTraining.servlet;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.aiTraining.base.AppBaseServlet;
import com.yunqu.cc.aiTraining.base.CommonLogger;
import com.yunqu.cc.aiTraining.base.Constants;
import com.yunqu.cc.aiTraining.css.ExportBase;
import com.yunqu.cc.aiTraining.css.TrainingRecordExport;
import com.yunqu.cc.aiTraining.css.TrainingUserRecordExport;
import com.yunqu.cc.aiTraining.enums.DatePattern;
import com.yunqu.cc.aiTraining.util.CsUtil;
import com.yunqu.cc.aiTraining.util.StringUtil;
import org.apache.log4j.Logger;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.net.URLDecoder;
import java.sql.SQLException;
import java.util.*;

@SuppressWarnings("serial")
@MultipartConfig
@WebServlet("/trainingUser/record/*")
public class TrainingUserRecordServlet extends AppBaseServlet {

    Logger logger = new CommonLogger().getLogger();

    public String actionForList() {
        return "/pages/training/training-all-user-list.jsp";
    }

    public String actionForTrainingUserList() {
        return "/pages/training/training-user-list.jsp";
    }


    /**
     * 人员陪练任务导出
     */
    public void actionForConstactExport() {
        HttpServletResponse response = this.getResponse();
        try {
            EasySQL sql = new EasySQL(" select T1.TASK_ID, T1.TASK_STATUS, T1.TASK_NAME, T2.TASK_PUBLISH_TIME, T2.PUBLISHER_NAME, T2.EXAM_START_TIME, " +
                    "T2.EXAM_END_TIME, T2.TASK_TYPE, T2.TEST_NAME, T1.TRAINING_RESULT, T1.TRAINING_SCORE, T1.TASK_FINISH_TIME, T1.TRAINING_USER_NAME, " +
                    "T1.TRAINING_USER_ID, T2.TASK_URL, T4.DEPT_NAME AS DEPT_NAME, T5.DEPT_NAME AS AREA_NAME FROM C_AI_TRAINING_USER T1 LEFT JOIN C_AI_TRAINING_RECORD T2 ON T1.TASK_ID = T2.TASK_ID ");
            sql.append("INNER JOIN mars.EASI_USER_LOGIN T6 ON T6.USER_ACCT = T1.TRAINING_USER_ID " +
                    "INNER JOIN mars.EASI_DEPT_USER T3 ON T3.USER_ID = T6.USER_ID " +
                    "INNER JOIN mars.EASI_DEPT T4 ON T3.DEPT_ID = T4.DEPT_ID " +
                    "INNER JOIN mars.EASI_DEPT T5 ON T4.P_DEPT_CODE = T5.DEPT_CODE ");
            sql.append("WHERE 1=1 ");
            sql.appendLike(URLDecoder.decode(this.getRequest().getParameter("taskName"), "UTF-8"), "and T1.TASK_NAME like ? ");
            sql.append(this.getRequest().getParameter("taskType"), "and T2.TASK_TYPE = ? ");
            sql.append(this.getRequest().getParameter("publisherAcc"), "and T2.PUBLISHER_ACC = ? ");
            sql.append(this.getRequest().getParameter("trainingUserId"), "and T1.TRAINING_USER_ID = ? ");
            sql.append(this.getRequest().getParameter("trainingResult"), "and T1.TRAINING_RESULT = ? ");
            sql.append(this.getRequest().getParameter("taskStatus"), "and T1.TASK_STATUS = ? ");
            sql.append(this.getRequest().getParameter("activeStatus"), "and T2.ENABLE_STATUS = ? ");
            sql.append(this.getRequest().getParameter("publishStartTime"), " and T2.TASK_PUBLISH_TIME >= ? ");
            sql.append(this.getRequest().getParameter("publishEndTime"), " and T2.TASK_PUBLISH_TIME <= ? ");
            sql.append(this.getRequest().getParameter("taskStartTime"), " and T2.EXAM_END_TIME >= ? ");
            sql.append(this.getRequest().getParameter("taskEndTime"), " and T2.EXAM_START_TIME <= ? ");
            sql.append(this.getRequest().getParameter("minScore"), " and T1.TRAINING_SCORE >= ? ");
            sql.append(this.getRequest().getParameter("maxScore"), " and T1.TRAINING_SCORE <= ? ");
            sql.append(this.getRequest().getParameter("deptCode"), " and T4.DEPT_CODE = ? ");
            sql.append(this.getRequest().getParameter("areaCode"), " and T5.DEPT_CODE = ? ");
            if (this.getRequest().getParameter("isSelf").equals("true")) {
                sql.append(UserUtil.getUser(this.getRequest()).getUserAcc(), "and T1.TRAINING_USER_ID = ? ");
            }
            sql.append(" ORDER BY T1.CREATE_TIME desc ");
            logger.info(sql.getSQL() + ";" + Arrays.toString(sql.getParams()));
            EasyQuery query = getQuery();
            query.setMaxRow(9999);
            List<EasyRow> list = query.queryForList(sql.getSQL(), sql.getParams());
            JSONArray jsonArray = new JSONArray();
            for (EasyRow easyRow : list) {
                JSONObject obj = easyRow.toJSONObject();
                if (obj.getString("TASK_TYPE") != null && !obj.getString("TASK_TYPE").isEmpty() && obj.getString("TASK_TYPE").equals("EXAM")) {
                    obj.put("TASK_TYPE", "测验");
                } else {
                    obj.put("TASK_TYPE", "陪练");
                }
                if (obj.getString("TRAINING_RESULT") != null && !obj.getString("TRAINING_RESULT").isEmpty() && obj.getString("TRAINING_RESULT").equals("PASS")) {
                    obj.put("TRAINING_RESULT", "及格");
                } else if (obj.getString("TRAINING_RESULT") != null && !obj.getString("TRAINING_RESULT").isEmpty() && obj.getString("TRAINING_RESULT").equals("FLUNK")){
                    obj.put("TRAINING_RESULT", "不及格");
                }
                if (obj.getString("TASK_STATUS") != null && !obj.getString("TASK_STATUS").isEmpty() && obj.getString("TASK_STATUS").equals("DONE")) {
                    obj.put("TASK_STATUS", "已完成");
                } else if (obj.getString("TASK_STATUS") != null && !obj.getString("TASK_STATUS").isEmpty() && obj.getString("TASK_STATUS").equals("UNDONE")){
                    obj.put("TASK_STATUS", "未完成");
                }
                jsonArray.add(obj);
            }
            String fileName = "TRAINING_USER_RECORD_" + StringUtil.data2Str(new Date(), DatePattern.PAT_DATE1.getPattern()) + ".xlsx";
            ExportBase export = new TrainingUserRecordExport();
            XSSFWorkbook book = export.exportExcel(jsonArray);
            downExcel(response, book, fileName);
        } catch (Exception e) {
            logger.error("导出失败, 原因: " + e.getMessage(), e);
            sendResponse("<script>alert('导出数据失败，请重新导出！')</script>", response);
        }
    }

    /**
     * 下载文件
     *
     * @param response
     * @param workbook
     * @param fileName
     */
    private void downExcel(HttpServletResponse response, XSSFWorkbook workbook, String fileName) {
        //设置文件ContentType类型,该设置会自动判断下载文件类型
        response.setContentType("application/vnd.ms-excel");
        //设置文件头
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
        try {
            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
            out.close();
            out.flush();
        } catch (Exception e1) {
            logger.error("往输出流中写文件失败：" + e1.getMessage());
            e1.printStackTrace();
        }

    }

    /**
     * 出流中输出对象
     *
     * @param resp
     * @param rp
     * @throws Exception
     */
    private void sendResponse(String str, HttpServletResponse response) {
        PrintWriter out = null;
        // 设置页面不缓存
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Cache-Control", "no-store");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);
        response.setContentType("application/json; charset=utf-8");
        try {
            String returnStr = "";
            if (str != null) {
                returnStr = str;
            }
            out = response.getWriter();
            out.write(returnStr);
            out.flush();
        } catch (Exception e) {
            logger.error("接口返回错误 -->"
                    + e.getMessage(), e);
            logger.error(e);
        } finally {
            if (str != null) {
                out.close();
            }
        }
    }

}
