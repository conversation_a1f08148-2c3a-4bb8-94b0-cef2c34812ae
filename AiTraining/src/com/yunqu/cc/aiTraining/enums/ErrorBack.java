package com.yunqu.cc.aiTraining.enums;

public enum ErrorBack {
	FAIL("{\"msg\":\"\u8bf7\u6c42\u51fa\u9519!\",\"state\":0}"),
	OK_LIST("{\"msg\":\"\u8BF7\u6C42\u6210\u529F!\",\"state\":1,\"pageType\":1,\"data\":[],\"totalRow\":0,\"totalPage\":0,\"pageIndex\":-1,\"pageSize\":10}"),
	OK_RECORD("{\"msg\":\"\u8BF7\u6C42\u6210\u529F!\",\"state\":1,\"pageType\":2,\"data\":[]}"),
	OK_DISC("{\"msg\":\"\u8BF7\u6C42\u6210\u529F!\",\"state\":1,\"pageType\":3,\"data\":[]}");
	
	private ErrorBack(Object value){
		this.value = value;
	}
	
	private Object value ;

	public String getValue() {
		return value.toString();
	}

	public void setValue(Object value) {
		this.value = value;
	}
	
}
