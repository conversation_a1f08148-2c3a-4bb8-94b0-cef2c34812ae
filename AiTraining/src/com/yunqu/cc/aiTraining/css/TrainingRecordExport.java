package com.yunqu.cc.aiTraining.css;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.aiTraining.enums.Formats;
import com.yunqu.cc.aiTraining.util.StringUtil;

import java.util.ArrayList;
import java.util.List;

public class TrainingRecordExport extends ExportBase {

    @Override
    public String[] titles() {
        return new String[]{"任务ID", "任务名称", "任务发布时间", "发布人", "习题集开始时间", "习题集结束时间", "任务类型", "测验时长", "任务生成方式", "陪练总人数", "启用状态"};
    }

    @Override
    public Formats[] dataFormat() {
        return new Formats[]{Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT};
    }

    @Override
    public List<Object[]> dealJArrData(JSONArray array) {
        List<Object[]> list = new ArrayList<>();
        for (int j = 0; j < array.size(); j++) {
            Object[] obj = new Object[11];
            JSONObject ov = array.getJSONObject(j);
            obj[0] = ov.getString("TASK_ID");//任务ID
            obj[1] = ov.getString("TASK_NAME");//任务名称
            obj[2] = ov.getString("TASK_PUBLISH_TIME");//任务发布时间
            obj[3] = ov.getString("PUBLISHER_NAME");//发布人
            obj[4] = ov.getString("EXAM_START_TIME");//习题集开始时间
            obj[5] = ov.getString("EXAM_END_TIME");//习题集结束时间
            obj[6] = ov.getString("TASK_TYPE");//任务类型
            obj[7] = ov.getString("TEST_DURATION");//测验时长
            obj[8] = ov.getString("TASK_GENERATION_METHOD");//任务生成方式
            obj[9] = ov.getString("TRAINING_TOTAL");//陪练总人数
            obj[10] = ov.getString("ENABLE_STATUS");//启用状态
            list.add(obj);
        }
        return list;
    }


}