package com.yunqu.cc.aiTraining.css;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.aiTraining.enums.Formats;

import java.util.ArrayList;
import java.util.List;

public class TrainingUserRecordExport extends ExportBase {

    @Override
    public String[] titles() {
        return new String[]{"任务ID", "任务状态", "任务名称", "任务发布时间", "发布人", "任务开始时间", "任务结束时间", "任务类型", "试卷名", "陪练结果", "陪练成绩", "任务完成时间", "陪练人姓名", "陪练人账号", "班组", "区域"};
    }

    @Override
    public Formats[] dataFormat() {
        return new Formats[]{Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT, Formats.TEXT};
    }

    @Override
    public List<Object[]> dealJArrData(JSONArray array) {
        List<Object[]> list = new ArrayList<>();
        for (int j = 0; j < array.size(); j++) {
            Object[] obj = new Object[16];
            JSONObject ov = array.getJSONObject(j);
            obj[0] = ov.getString("TASK_ID");           // 任务ID
            obj[1] = ov.getString("TASK_STATUS");      // 任务状态
            obj[2] = ov.getString("TASK_NAME");         // 任务名称
            obj[3] = ov.getString("TASK_PUBLISH_TIME"); // 任务发布时间
            obj[4] = ov.getString("PUBLISHER_NAME");   // 发布人
            obj[5] = ov.getString("EXAM_START_TIME");   // 任务开始时间
            obj[6] = ov.getString("EXAM_END_TIME");     // 任务结束时间
            obj[7] = ov.getString("TASK_TYPE");         // 任务类型
            obj[8] = ov.getString("TEST_NAME");         // 试卷名
            obj[9] = ov.getString("TRAINING_RESULT");   // 陪练结果
            obj[10] = ov.getString("TRAINING_SCORE");  // 陪练成绩
            obj[11] = ov.getString("TASK_FINISH_TIME"); // 任务完成时间
            obj[12] = ov.getString("TRAINING_USER_NAME"); // 陪练人姓名
            obj[13] = ov.getString("TRAINING_USER_ID"); // 陪练人账号
            obj[14] = ov.getString("DEPT_NAME"); // 班组
            obj[15] = ov.getString("AREA_NAME"); // 区域
            list.add(obj);
        }
        return list;
    }


}