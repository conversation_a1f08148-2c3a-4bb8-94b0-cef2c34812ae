package com.yunqu.cc.aiTraining.css;

import com.alibaba.fastjson.JSONArray;
import com.yunqu.cc.aiTraining.enums.Formats;
import com.yunqu.cc.aiTraining.util.poi.PoiUtil;
import org.apache.poi.xssf.usermodel.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public abstract class ExportBase implements ExportInf{
	public abstract List<Object[]> dealJArrData(JSONArray array);
	public void createHead(XSSFWorkbook book,XSSFCell cell,XSSFSheet sheet) {
		XSSFCellStyle headStyle = PoiUtil.getHeadStyle(book);  
		String[] titles = this.titles();    
        // 构建表头  
        XSSFRow headRow = sheet.createRow(0);  
        for (int i = 0; i < titles.length; i++)  
        {  
            cell = headRow.createCell(i);  
            cell.setCellStyle(headStyle);  
            cell.setCellValue(titles[i]);  
        }  
		
	}

	public XSSFWorkbook exportExcel(JSONArray sourceData) {  
		List<Object[]> list = this.dealJArrData(sourceData);
        // 创建一个workbook 对应一个excel应用文件  
        XSSFWorkbook workBook = new XSSFWorkbook();  
        PoiUtil.initStyleMap(workBook);
        Formats[] df = this.dataFormat();
        //在workbook中添加一个sheet,对应Excel文件中的sheet  
        XSSFSheet sheet = workBook.createSheet("sheet1");
       // Map<String,XSSFCellStyle> styleMap = new PoiCellStyle(workBook).getStyleMap(); 
        XSSFCell cell = null;
        createHead(workBook,cell,sheet);
        // 构建表体数据  
        if (list != null && list.size() > 0)  
        {  
            for (int j = 0; j < list.size(); j++)  
            {  
                XSSFRow bodyRow = sheet.createRow(j + 1);  
                Object[] arr = list.get(j);
                for(int i = 0;i< df.length;i++){
                	cell = bodyRow.createCell(i);  
                	if(arr[i] == null){
                		continue;
                	}
                	PoiUtil.setMyCellValue(cell,df[i],arr[i].toString()); 
                }
            }  
        }  
        return workBook;
    }
	
	public List<Object[]> dealStrData(List<Map<String, String>> list) {
		List<Object[]> r = new ArrayList<Object[]>();
		if(list!=null && list.size()>0){
			for (int j = 0; j < list.size(); j++) {
				Map<String,String> map = list.get(j);
				for(String key : map.keySet()){
					String value =map.get(key);
					if(value==null||"".equals(value)){
						map.put(key, "");
					}
				}
				Object[] obj = map.values().toArray();
				r.add(obj);
		    }
		}
		return r;
	}
}
