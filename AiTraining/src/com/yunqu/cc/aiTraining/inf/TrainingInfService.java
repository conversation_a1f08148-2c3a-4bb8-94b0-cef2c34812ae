package com.yunqu.cc.aiTraining.inf;

import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.aiTraining.base.CommonLogger;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yunqu.cc.aiTraining.base.Constants;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * ai培训模块统一处理接口
 */
public class TrainingInfService extends IService {
    private Logger logger = LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME);

    private EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);

    @Override
    public JSONObject invoke(JSONObject json) throws ServiceException {
        String command = json.getString("command");
        if (command.equals("updateUserInfo")) {
            return updateUserInfo(json);
        } else if (command.equals("sendReminderMessages")) {
            sendReminderMessages();
        }
        JSONObject result = new JSONObject();
        result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
        result.put("respDesc", "不存在的command,请检查！");
        return result;
    }

    private JSONObject updateUserInfo(JSONObject json) {
        logger.info("AI培训人员数据同步开始>>>>>>>>");
        JSONObject data = json.getJSONObject("data");
        logger.info("AI培训人员数据入参： " + JSONObject.toJSONString(data));
        JSONObject result = new JSONObject();
        String taskId = data.getString("taskId");
        String studentMip = data.getString("studentMip");
        String completionStatus = data.getString("completionStatus");
        String trainingResult = data.getString("trainingResult");
        String score = data.getString("score");
        String finishTime = data.getString("finishTime");
        EasySQL sql = new EasySQL();
        try {
            sql.append("UPDATE C_AI_TRAINING_USER SET ");
            sql.append(completionStatus, "TASK_STATUS =? ");
            sql.append(trainingResult, " , TRAINING_RESULT =? ");
            sql.append(score, " , TRAINING_SCORE =? ");
            sql.append(finishTime, " , TASK_FINISH_TIME =? ");
            sql.append("WHERE 1=1 ");
            sql.append(taskId, "ANd TASK_ID =? ");
            sql.append(studentMip, " AND TRAINING_USER_ID =? ");
            logger.info("updateUserInfo中: sql语句：" + sql.getSQL() + "  , sql参数：" + Arrays.toString(sql.getParams()));
            int update = query.executeUpdate(sql.getSQL(), sql.getParams());
            if (update > 0) {
                result.put("respCode", GWConstants.RET_CODE_SUCCESS);
                result.put("respDesc", "更新成功");
                logger.info("updateUserInfo更新成功");
            } else {
                result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                result.put("respDesc", "更新失败");
                logger.info("updateUserInfo更新失败");
            }
        } catch (SQLException e) {
            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
            result.put("respDesc", "更新异常");
            logger.info("updateUserInfo更新异常");
            throw new RuntimeException(e);
        }
        return result;
    }

    private void sendReminderMessages() {
        Logger jobsLogger = LogEngine.getLogger(Constants.APP_NAME, "AiTraining_jobs");
        jobsLogger.info("检索数据库开始>>>>>>>>");
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String startTime = sdf.format(date) + ":00";
        String endTime = sdf.format(date) + ":59";
        EasySQL sql = new EasySQL("select T1.TRAINING_USER_ID FROM C_AI_TRAINING_USER T1 INNER JOIN C_AI_TRAINING_RECORD T2 ON T2.TASK_ID = T1.TASK_ID" +
                " WHERE T2.ENABLE_STATUS = 'ENABLE' ");
        sql.append(startTime, " AND T2.TASK_REMINDER_TIME >= ? ");
        sql.append(endTime, " AND T2.TASK_REMINDER_TIME <= ? ");
        try {
            List<EasyRow> list = query.queryForList(sql.getSQL(), sql.getParams());
            jobsLogger.info("检索数据库结束>>>>>>>>");
            jobsLogger.info("数据库返回值：");
            List<String> userIds = new ArrayList<>();
            for (EasyRow row : list) {
                jobsLogger.info(row.toJSONObject().toString());
                String userId = row.toJSONObject().getString("TRAINING_USER_ID");
                userIds.add(userId);
            }
            if (userIds.isEmpty()) {
                return;
            }
            String targetUserAcc = String.join(";", userIds);
            IService service = ServiceContext.getService(ServiceID.NOTICE_INTERFACE);
            JSONObject obj= new JSONObject();
            obj.put("sender", "notice");
            obj.put("password", "YQ_85521717");
            obj.put("serialId", IDGenerator.getDefaultNUMID());
            obj.put("command", ServiceCommand.NOTICE_ADD_USER_NOTICE); // serviceId:  ServiceID.NOTICE_INTERFACE;
            obj.put("receiverType", "01");//接收类型 01-个人  02-	部门   03-	所有人
            obj.put("userAcc", targetUserAcc);//接收通知的人员，如有多个，用;隔开
            obj.put("deptCode", "000");//
            obj.put("createUserAcc", "admin@mars");//
            obj.put("createUserDeptCode", "000");//
            obj.put("type", "你有一条AI陪练任务即将开始，请提早做好准备");
            obj.put("module", Constants.APP_NAME);
            obj.put("title", "提醒通知");
            obj.put("content", "您有一条提醒通知:你有一条AI陪练任务即将开始，请提早做好准备");
            obj.put("url", "-");
            obj.put("method", "03");
            jobsLogger.info("AI陪练提醒消息：" + obj.toJSONString());
            JSONObject result = service.invoke(obj);
            jobsLogger.info("AI陪练提醒消息返回值:"+ result.toJSONString());
        } catch (SQLException | ServiceException e) {
            throw new RuntimeException(e);
        }
    }
}
