<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.yunqu.midea</groupId>
        <artifactId>cc</artifactId>
        <version>2.5.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>AiTraining</artifactId>
    <packaging>war</packaging>
    <version>${cc-media.version}</version>

    <dependencies>
    </dependencies>

    <build>
        <sourceDirectory>src</sourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven-war-plugin.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <webResources>
                        <!--                        <resource>-->
                        <!--                            <directory>src</directory>-->
                        <!--                            <targetPath>WEB-INF/src</targetPath>-->
                        <!--                            <includes>-->
                        <!--                                <include>**/*.java</include>-->
                        <!--                            </includes>-->
                        <!--                        </resource>-->
                        <resource>
                            <directory>WebContent</directory>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>
            <!-- 注释掉无法找到的插件 -->

            <plugin>
                <groupId>com.yunqu.mars.plugin</groupId>
                <artifactId>console-upgrade</artifactId>
                <version>1.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-version</goal>
                            <goal>push-mars</goal>
                        </goals>
                        <configuration>
                            <cleanLibDirectory>false</cleanLibDirectory>
                            <enableWarPushMars>false</enableWarPushMars>
                            <primaryVersion>2.5.3</primaryVersion>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>
</project>
