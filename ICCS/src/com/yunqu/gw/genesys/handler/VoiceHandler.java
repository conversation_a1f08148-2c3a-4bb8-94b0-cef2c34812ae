package com.yunqu.gw.genesys.handler;

import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.genesyslab.platform.commons.collections.KeyValueCollection;
import com.genesyslab.platform.commons.protocol.Message;
import com.genesyslab.platform.voice.protocol.tserver.CallType;
import com.genesyslab.platform.voice.protocol.tserver.DNRole;
import com.genesyslab.platform.voice.protocol.tserver.events.EventAgentLogin;
import com.genesyslab.platform.voice.protocol.tserver.events.EventAgentLogout;
import com.genesyslab.platform.voice.protocol.tserver.events.EventAgentNotReady;
import com.genesyslab.platform.voice.protocol.tserver.events.EventAgentReady;
import com.genesyslab.platform.voice.protocol.tserver.events.EventDNBackInService;
import com.genesyslab.platform.voice.protocol.tserver.events.EventDNOutOfService;
import com.genesyslab.platform.voice.protocol.tserver.events.EventDialing;
import com.genesyslab.platform.voice.protocol.tserver.events.EventError;
import com.genesyslab.platform.voice.protocol.tserver.events.EventEstablished;
import com.genesyslab.platform.voice.protocol.tserver.events.EventHeld;
import com.genesyslab.platform.voice.protocol.tserver.events.EventLinkConnected;
import com.genesyslab.platform.voice.protocol.tserver.events.EventMonitoringCancelled;
import com.genesyslab.platform.voice.protocol.tserver.events.EventMonitoringNextCall;
import com.genesyslab.platform.voice.protocol.tserver.events.EventPartyAdded;
import com.genesyslab.platform.voice.protocol.tserver.events.EventPartyDeleted;
import com.genesyslab.platform.voice.protocol.tserver.events.EventRegistered;
import com.genesyslab.platform.voice.protocol.tserver.events.EventReleased;
import com.genesyslab.platform.voice.protocol.tserver.events.EventRetrieved;
import com.genesyslab.platform.voice.protocol.tserver.events.EventRinging;
import com.genesyslab.platform.voice.protocol.tserver.events.EventUnregistered;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.gw.genesys.bean.GenesysAgentBean;
import com.yunqu.gw.genesys.bean.GenesysMessage;
import com.yunqu.gw.genesys.service.GenesysVoiceService;
import com.yunqu.gw.iccs.bean.Agent;
import com.yunqu.gw.iccs.bean.CallInfo;
import com.yunqu.gw.iccs.log.CommonLogger;
import com.yunqu.gw.iccs.tcp.ICCSTCPServer;
import com.yunqu.gw.iccs.util.Const;
import com.yunqu.gw.iccs.util.ICCSCallEvent;
import com.yunqu.gw.iccs.util.ICCSStatusEvent;
import com.yunqu.gw.iccs.util.TimeUtil;

/**
 * <AUTHOR>
 * 类名称：VoiceHandler   
 * 创建时间：2018年5月7日 下午7:51:19   
 * @version   
 * 
 * 
 * 登录 ： connect -> register -> logon
 * 登出 ： logoff  -> unregister -> disconnect
 */
public class VoiceHandler extends Handler{
	public static String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

	@Override
	public void handle(GenesysMessage msg) {
		Message message = msg.getMessage();
		Agent agent = getAgent(msg.getId());
		GenesysAgentBean Gagent = agent.getGenesysBean();
		CallInfo callInfo;
		CallInfo secondCallInfo;
		switch (message.messageId()) {
			case EventLinkConnected.ID://连接建立
				new GenesysVoiceService(Gagent).register();
				break;
			case EventRinging.ID://呼入振铃事件
				EventRinging eventRinging = (EventRinging) message;
				if(eventRinging.getCallState() == 31){//监听
					Gagent.setConnID(eventRinging.getConnID());
					new GenesysVoiceService(Gagent).answerCall();//如果是监听则自动接听
				}else{
					Gagent.setConnID(eventRinging.getConnID());
					callInfo = getNotNullCallInfo(agent);
					callInfo.setConnId(eventRinging.getConnID().toString());
					callInfo.setCallId(eventRinging.getCallID().toString());
					callInfo.setCallUuid(eventRinging.getCallUuid());
					callInfo.setAgentAlertingTime(TimeUtil.getCurrentTime("YYYYMMDDhhmmss"));
					callInfo.setAgentAlertingTime(TimeUtil.getCurrentTime(TIME_FORMAT));
					callInfo.setCaller(eventRinging.getOtherDN());
					callInfo.setCalled(eventRinging.getThisDN());
					callInfo.setCreateCause("inbound");
//					callInfo.setAgentNo(eventRinging.getAgentID());
					callInfo.setAgentNo(agent.getAgentID());
					if (StringUtils.isBlank(callInfo.getInType())) {
						callInfo.setInType("01");
					}
					KeyValueCollection kvc = eventRinging.getUserData();
					if (kvc!=null) {
						callInfo.setOrigCalled(kvc.getString("ORIGCALLED"));
						callInfo.setCreateTime(kvc.getString("CREATETIME"));
						callInfo.setIvrBeginTime(kvc.getString("ALLOCTIME"));
						callInfo.setIvrAgentTime(kvc.getString("CREATETIME"));//ivr获取坐席时间（这个不清楚对应哪一个字段）
						callInfo.setIvrQueueTime(kvc.getString("STARTTIME"));
						callInfo.setIvrEndTime(kvc.getString("ENDTIME"));
						callInfo.setAgentAnswerTime(kvc.getString("ALLOCTIME"));
						callInfo.setEndTime(kvc.getString("DEALLOCTIME"));
						callInfo.setClearCause(kvc.getString("CLEARCAUSE"));
						callInfo.setServiceGroupId(kvc.getString("GROUPID"));
					}
					Gagent.setSecondConnID(null);
					ICCSTCPServer.send(ICCSCallEvent.evtAltering.getReq(agent));
					ICCSTCPServer.send(ICCSStatusEvent.ALERTING.getReq(agent));
				}
				break;
			case EventDialing.ID://外呼振铃事件
				EventDialing eventDialing = (EventDialing)message;
				if (eventDialing.getCallType() == CallType.Consult){
					Gagent.setSecondConnID(eventDialing.getConnID());
                	secondCallInfo = getNotNullSecondCallInfo(agent);
                	secondCallInfo.setConnId(eventDialing.getConnID().toString());
                	secondCallInfo.setCallId(eventDialing.getCallID().toString());
                	secondCallInfo.setCallUuid(eventDialing.getCallUuid());
                	secondCallInfo.setAgentAlertingTime(TimeUtil.getCurrentTime(TIME_FORMAT));
                	secondCallInfo.setCaller(eventDialing.getThisDN());
                	secondCallInfo.setCalled(eventDialing.getOtherDN());
                	secondCallInfo.setCreateCause("outbound");
                	secondCallInfo.setAgentNo(eventDialing.getAgentID());
                	if (StringUtils.isBlank(secondCallInfo.getInType())) {
                		secondCallInfo.setInType("02");
                	}
					KeyValueCollection kvc = eventDialing.getUserData();
					if (kvc!=null) {
						secondCallInfo.setOrigCalled(kvc.getString("ORIGCALLED"));
						secondCallInfo.setCreateTime(kvc.getString("CREATETIME"));
						secondCallInfo.setIvrBeginTime(kvc.getString("ALLOCTIME"));
						secondCallInfo.setIvrAgentTime(kvc.getString("CREATETIME"));//ivr获取坐席时间（这个不清楚对应哪一个字段）
						secondCallInfo.setIvrQueueTime(kvc.getString("STARTTIME"));
						secondCallInfo.setIvrEndTime(kvc.getString("ENDTIME"));
						secondCallInfo.setAgentAnswerTime(kvc.getString("ALLOCTIME"));
						secondCallInfo.setEndTime(kvc.getString("DEALLOCTIME"));
						secondCallInfo.setClearCause(kvc.getString("CLEARCAUSE"));
						secondCallInfo.setServiceGroupId(kvc.getString("GROUPID"));
					}
				}
                else{
                	Gagent.setConnID(eventDialing.getConnID());
					callInfo = getNotNullCallInfo(agent);
					callInfo.setConnId(eventDialing.getConnID().toString());
                	callInfo.setCallId(eventDialing.getCallID().toString());
                	callInfo.setCallUuid(eventDialing.getCallUuid());
					callInfo.setAgentAlertingTime(TimeUtil.getCurrentTime(TIME_FORMAT));
					callInfo.setCaller(eventDialing.getThisDN());
					callInfo.setCalled(eventDialing.getOtherDN());
					callInfo.setCreateCause("outbound");
					callInfo.setAgentNo(eventDialing.getAgentID());
					if (StringUtils.isBlank(callInfo.getInType())) {
						callInfo.setInType("02");
					}
					KeyValueCollection kvc = eventDialing.getUserData();
					if (kvc!=null) {
						callInfo.setOrigCalled(kvc.getString("ORIGCALLED"));
						callInfo.setCreateTime(kvc.getString("CREATETIME"));
						callInfo.setIvrBeginTime(kvc.getString("ALLOCTIME"));
						callInfo.setIvrAgentTime(kvc.getString("CREATETIME"));//ivr获取坐席时间（这个不清楚对应哪一个字段）
						callInfo.setIvrQueueTime(kvc.getString("STARTTIME"));
						callInfo.setIvrEndTime(kvc.getString("ENDTIME"));
						callInfo.setAgentAnswerTime(kvc.getString("ALLOCTIME"));
						callInfo.setEndTime(kvc.getString("DEALLOCTIME"));
						callInfo.setClearCause(kvc.getString("CLEARCAUSE"));
						callInfo.setServiceGroupId(kvc.getString("GROUPID"));
					}
					ICCSTCPServer.send(ICCSCallEvent.evtAltering.getReq(agent));
					ICCSTCPServer.send(ICCSStatusEvent.ALERTING.getReq(agent));
                }
				break;

			case EventEstablished.ID: //呼叫接通事件
                EventEstablished eventEstablished = (EventEstablished)message;
                if (eventEstablished.getCallType() == CallType.Consult){//咨询
                    Gagent.setSecondConnID(eventEstablished.getConnID());
                	secondCallInfo = getNotNullSecondCallInfo(agent);
                	secondCallInfo.setAgentAnswerTime(TimeUtil.getCurrentTime(TIME_FORMAT));
                	ICCSTCPServer.send(ICCSCallEvent.evtConsultedBegin.getReq(agent));
                	ICCSTCPServer.send(ICCSStatusEvent.CONSULTED.getReq(agent));
                }else if (eventEstablished.getCallState()==31){//监听
					
				}
                else{
                	Gagent.setConnID(eventEstablished.getConnID());
                	callInfo = getNotNullCallInfo(agent);
                	callInfo.setAgentAnswerTime(TimeUtil.getCurrentTime(TIME_FORMAT));
                	ICCSTCPServer.send(ICCSCallEvent.evtConnected.getReq(agent));
                    ICCSTCPServer.send(ICCSStatusEvent.TALK.getReq(agent));
                    this.saveCallRecord(callInfo);
                }
				break;
			case EventReleased.ID://呼叫挂断事件
				EventReleased eventReleased = (EventReleased)message;
				
				CommonLogger.Logger().info(CommonUtil.getClassNameAndMethod(this)+" 开始进行挂断:"+eventReleased.toString());
				
                if (eventReleased.getCallType() == CallType.Consult){//咨询
                	CommonLogger.Logger().info(CommonUtil.getClassNameAndMethod(this)+" 开始进行挂断:咨询");
                	secondCallInfo = getNotNullSecondCallInfo(agent);
                	secondCallInfo.setEndTime(TimeUtil.getCurrentTime(TIME_FORMAT));
                	ICCSTCPServer.send(ICCSCallEvent.evtConsultedEnd.getReq(agent));
                	ICCSTCPServer.send(ICCSStatusEvent.valueOf(agent.getLastStatus()).getReq(agent));
                	Gagent.setSecondConnID(null);
                	agent.setSecondCallInfo(null);
                }else if(eventReleased.getThisDNRole() == DNRole.RoleConferenceMember){//三方
                	CommonLogger.Logger().info(CommonUtil.getClassNameAndMethod(this)+" 开始进行挂断:三方");
                	ICCSStatusEvent status = !agent.getIsReady()?ICCSStatusEvent.BUSY:ICCSStatusEvent.IDLE;
                	callInfo = getNotNullCallInfo(agent);
                	callInfo.setEndTime(TimeUtil.getCurrentTime(TIME_FORMAT));
                	ICCSTCPServer.send(ICCSCallEvent.evtConferencedEnd.getReq(agent));
                    ICCSTCPServer.send(status.getReq(agent));
                	Gagent.setConnID(null);
                	agent.setCallInfo(null);
                }else if(eventReleased.getCallState() == 31){//监听
                	CommonLogger.Logger().info(CommonUtil.getClassNameAndMethod(this)+" 开始进行挂断:监听");
				}else{
					CommonLogger.Logger().info(CommonUtil.getClassNameAndMethod(this)+" 开始进行挂断:通话");
                	ICCSStatusEvent status = !agent.getIsReady()?ICCSStatusEvent.BUSY:ICCSStatusEvent.IDLE;
                	callInfo = getNotNullCallInfo(agent);
                	callInfo.setEndTime(TimeUtil.getCurrentTime(TIME_FORMAT));
                	ICCSTCPServer.send(ICCSCallEvent.evtDisConnected.getReq(agent));
                    ICCSTCPServer.send(status.getReq(agent));
                    this.saveCallRecord(callInfo);
                	Gagent.setConnID(null);
                	agent.setCallInfo(null);
                }
                CommonLogger.Logger().info(CommonUtil.getClassNameAndMethod(this)+" 开始进行挂断:结束");
				break;
			case EventRetrieved.ID://恢复通话事件
				ICCSTCPServer.send(ICCSCallEvent.evtRetrieved.getReq(agent));
				ICCSTCPServer.send(ICCSStatusEvent.TALK.getReq(agent));
				break;
			case EventHeld.ID://保持通话事件
				ICCSTCPServer.send(ICCSCallEvent.evtHeld.getReq(agent));
				ICCSTCPServer.send(ICCSStatusEvent.HELD.getReq(agent));
				break;
			case EventAgentLogin.ID://登录事件
				break;
			case EventAgentLogout.ID://登出事件
				new GenesysVoiceService(Gagent).unRegister();
				ICCSTCPServer.send(ICCSStatusEvent.LOGOFF.getReq(agent));
				break;
			case EventAgentReady.ID://就绪事件
				agent.setIsReady(true);
				ICCSTCPServer.send(ICCSStatusEvent.IDLE.getReq(agent));
				break;
			case EventAgentNotReady.ID://未就绪事件
				agent.setIsReady(false);
				ICCSTCPServer.send(ICCSStatusEvent.BUSY.getReq(agent));
				break;
			case EventRegistered.ID://注册事件
				new GenesysVoiceService(Gagent).login();
				break;
			case EventUnregistered.ID://注销事件
				Gagent.disconnect();
				removeAgent(agent.getAgentID());
				break;
			case EventPartyAdded.ID:
				ICCSTCPServer.send(ICCSCallEvent.evtConferencedBegin.getReq(agent));
				ICCSTCPServer.send(ICCSStatusEvent.CONFERENCED.getReq(agent));
				break;
			case EventPartyDeleted.ID:
				break;
			case EventMonitoringNextCall.ID://监听开始
				ICCSTCPServer.send(ICCSCallEvent.evtMonitoredBegin.getReq(agent));
				ICCSTCPServer.send(ICCSStatusEvent.MONITORED.getReq(agent));
				break;
			case EventMonitoringCancelled.ID://监听结束
				ICCSStatusEvent status = !agent.getIsReady()?ICCSStatusEvent.BUSY:ICCSStatusEvent.IDLE;
            	ICCSTCPServer.send(ICCSCallEvent.evtMonitoredEnd.getReq(agent));
                ICCSTCPServer.send(status.getReq(agent));
				break;
			case EventDNOutOfService.ID:
				break;
			case EventDNBackInService.ID:
				break;
			case EventError.ID:
				EventError error = (EventError)message;
//				ICCSTCPServer.send(ICCSStatusEvent.getReq(agent));
				break;
		}
	}
	
	/**
	 * 保存语音通话记录
	 * @param callInfo
	 */
	private void saveCallRecord(CallInfo callInfo) {
		try {
			IService service =  (IService)ServiceContext.getService("RECORDGW_INTEFACE");
			String callInfoStr = JSONObject.toJSONString(callInfo);
			CommonLogger.Logger().debug("[GenesysVoiceService.saveCallRecord] 调用service[RECORDGW_INTEFACE]请求数据:"+ callInfoStr);
			JSONObject json = new JSONObject();
			json.put("callInfo", callInfo);
			JSONObject result = service.invoke(json);
			CommonLogger.Logger().debug("[GenesysVoiceService.saveCallRecord] 调用service[RECORDGW_INTEFACE]接收数据:"+ result.toJSONString());
		} catch (ServiceException e) {
			// TODO Auto-generated catch block
			CommonLogger.Logger().error("[GenesysVoiceService.saveCallRecord] 调用service[RECORDGW_INTEFACE]异常:"+ e.getMessage(), e);
		}
	}
}
