package com.yunqu.gw.genesys.handler;

import com.yunqu.gw.genesys.bean.GenesysMessage;
import com.yunqu.gw.genesys.cache.AgentCache;
import com.yunqu.gw.iccs.bean.Agent;
import com.yunqu.gw.iccs.bean.CallInfo;
import com.yunqu.gw.iccs.factory.CacheFactory;
import com.yunqu.gw.iccs.util.Const;

public abstract class Handler {
	public abstract void handle(GenesysMessage msg);
	private AgentCache cache;
	
	protected Agent getAgent(String agentNo){
		cache = new CacheFactory().getCache(Const.CACHE_TYPE);
		return cache.getAgentByAgentID(agentNo);
	}
	
	protected void removeAgent(String agentID){
		if(cache==null){
			cache = new CacheFactory().getCache(Const.CACHE_TYPE);
		}
		cache.removeVoiceAgentByAgentID(agentID);
	}
	
	protected CallInfo getNotNullCallInfo(Agent agent){
		if(agent.getCallInfo()==null){
			agent.setCallInfo(new CallInfo());
		}
		return agent.getCallInfo();
	}
	
	protected CallInfo getNotNullSecondCallInfo(Agent agent){
		if(agent.getSecondCallInfo()==null){
			agent.setSecondCallInfo((new CallInfo()));
		}
		return agent.getSecondCallInfo();
	}
}
