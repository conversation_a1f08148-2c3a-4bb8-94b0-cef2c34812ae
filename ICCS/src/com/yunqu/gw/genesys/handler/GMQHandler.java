package com.yunqu.gw.genesys.handler;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import com.yunqu.gw.genesys.bean.GenesysMessage;
import com.yunqu.gw.genesys.cache.GenesysMessageQueue;
import com.yunqu.gw.iccs.util.Const;
import com.yunqu.gw.iccs.listener.ContextListener;



/**
 * <AUTHOR>
 * 类名称：GMQHandler   
 * 创建时间：2018年5月7日 下午7:51:26   
 * @version   
 */
public class GMQHandler {
	private GMQHandler(){}
	private static GMQHandler instance = new GMQHandler();
	private ScheduledExecutorService threadPool = Executors.newScheduledThreadPool(Const.CPU_CORE_NUM);
	public static GMQHandler getInstance(){
		return instance;
	}
	private  static Boolean isStarted = false;
	
	
	public  void handle(){
		if(!isStarted){
			isStarted = true;
			new Thread(()-> {
					while(ContextListener.isRunning){
						GenesysMessage gm = GenesysMessageQueue.pop();
						threadPool.execute(new GenesysMessageHandler(gm));
					}
			}).start();
		}
	}
	
	public void shutdown(){
		if(!threadPool.isShutdown()){
			threadPool.shutdown();
		}
	}
	
	private class GenesysMessageHandler implements Runnable{
		GenesysMessage gm;
		public GenesysMessageHandler(GenesysMessage gm){
			this.gm = gm;
		}
		@Override
		public void run() {
			Handler handler;
			if(gm.getType()==GenesysMessage.TYPE_TSERVER){
				handler = new VoiceHandler();
			}else if(gm.getType()==GenesysMessage.TYPE_STATSERVER){
				handler = new StatHandler();
			}else if(gm.getType()==GenesysMessage.TYPE_CONFIGSERVER){
				handler = new VoiceHandler();
			}else if(gm.getType()==GenesysMessage.TYPE_INSERVER){
				handler = new VoiceHandler();
			}else{
				handler = new VoiceHandler();
			}
			handler.handle(gm);
		}

	}}
