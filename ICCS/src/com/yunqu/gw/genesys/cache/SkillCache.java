package com.yunqu.gw.genesys.cache;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.yunqu.gw.iccs.bean.Skill;

public class SkillCache {
	private static Map<String, Skill> cache = new ConcurrentHashMap<String, Skill>();
	
	public static Skill getSkillBySkillId(String skillId){
		return cache.get(skillId);
	}
	
	public static void putSkill(Skill skill){
		cache.put(skill.getSkillId(), skill);
	}
}
