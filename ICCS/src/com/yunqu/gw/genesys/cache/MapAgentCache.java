package com.yunqu.gw.genesys.cache;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.yunqu.gw.iccs.bean.Agent;

public class MapAgentCache implements AgentCache{
	private static Map<String,Agent> cache = new ConcurrentHashMap<String,Agent>();

	@Override 
	public Agent getAgentByAgentID(String agentID) {
		return cache.get(agentID);
	}

	@Override
	public void removeVoiceAgentByAgentID(String agentID) {
		cache.remove(agentID);
	}

	@Override
	public void setAgent(Agent agent) throws Exception{
		if(agent.getAgentID()==null){
			throw new Exception("agentID is null or agentNo is null ");
		}
		cache.put(agent.getAgentID(), agent);
	}

	@Override
	public Set<String> getAllAgentID() {
		return cache.keySet();
	}
}
