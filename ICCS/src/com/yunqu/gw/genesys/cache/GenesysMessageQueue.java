package com.yunqu.gw.genesys.cache;

import java.util.concurrent.ArrayBlockingQueue;

import com.yunqu.gw.genesys.bean.GenesysMessage;
import com.yunqu.gw.iccs.util.Const;

public class GenesysMessageQueue {
private static ArrayBlockingQueue<GenesysMessage> queue = new ArrayBlockingQueue<GenesysMessage>(Const.MESSAGE_QUEUE_LENGTH); 
	
	public static GenesysMessage pop(){
		try {
			return queue.take();
		} catch (InterruptedException e) {
		}
		return null;
	}
	
	public static Boolean push(GenesysMessage msg){
		return queue.add(msg);
	}
	
	
	public static int size(){
		return queue.size();
	}
	
	public void removeAll(){
		queue.clear();
	}
}
