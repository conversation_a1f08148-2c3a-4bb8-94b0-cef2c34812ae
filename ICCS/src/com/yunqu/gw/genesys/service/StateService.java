package com.yunqu.gw.genesys.service;

import com.yunqu.gw.genesys.bean.StateBean;

public class StateService {
	private static StateBean bean = new StateBean();
	private static StateService service = new StateService();
	
	private StateService(){}
	
	public static StateService getInstance(String channel){
		bean.connect();
		if(channel!=null&&channel.length()>0) bean.setChannel(channel);
		return service;
	}
	
	
}
