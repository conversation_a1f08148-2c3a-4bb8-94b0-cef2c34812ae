package com.yunqu.gw.genesys.service;


import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.genesyslab.platform.applicationblocks.com.ICfgObject;
import com.genesyslab.platform.applicationblocks.com.objects.CfgPerson;
import com.genesyslab.platform.applicationblocks.com.objects.CfgSkill;
import com.genesyslab.platform.applicationblocks.com.objects.CfgSkillLevel;
import com.genesyslab.platform.applicationblocks.com.queries.CfgPersonQuery;
import com.genesyslab.platform.applicationblocks.com.queries.CfgSkillQuery;
import com.genesyslab.platform.configuration.protocol.types.CfgFlag;
import com.yunqu.gw.genesys.bean.ConfigBean;
import com.yunqu.gw.iccs.log.CommonLogger;
import com.yunqu.gw.iccs.util.RespStatus;


public class ConfigService{
	private static final int TENANT_ID = 101;
	private static ConfigBean configBean = new ConfigBean();
	private static ConfigService service = new ConfigService();
	
	private ConfigService(){
		
	}
	
	public static ConfigService getInstance(){
		configBean.connect();
		return service;
	}
	
	public boolean addPerson(String agentId){
		boolean result = false;
		try {
			if (readPerson(agentId) != null) {
				CommonLogger.logRequest("Agent: " + agentId + " is existsed");
				result = true;
			} else {
				CfgPerson person = new CfgPerson(configBean.getConfService());
				person.setUserName(agentId);
				person.setTenantDBID(TENANT_ID);
				person.setEmployeeID(agentId);
				person.setIsAgent(CfgFlag.CFGTrue);
				if (!person.isSaved()) {
					person.save();
				}
				if (person.isSaved()) {
					CommonLogger.logRequest("Agent: " + agentId + " created");
					result = true;
				}
			}
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
		return result;
	}
	
	public boolean deletePerson(String agentId){
		boolean result = false;
		try {
			CfgPerson objectToDelete = readPerson(agentId);
			if (objectToDelete != null) {
				if (objectToDelete.isSaved()) {
					objectToDelete.delete();
				}
				if (!objectToDelete.isSaved()) {
					result = true;
					CommonLogger.logRequest("Agent: " + agentId + " removed");
				}
			} else {
				result = true;
				CommonLogger.logRequest("Agent: " + agentId + " is not exists");
			}
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
		return result;
	}
	
	public boolean createSkill(String skillId) {
		boolean result = false;
		try {
			if (readSkill(skillId) != null) {
				CommonLogger.logRequest("Agent: " + skillId + " is existsed");
				result = true;
			} else {
				CfgSkill skill = new CfgSkill(configBean.getConfService());
				skill.setTenantDBID(TENANT_ID);
				skill.setName(skillId);
				if (!skill.isSaved()) {
					skill.save();
				}
				if (skill.isSaved()) {
					result = true;
					CommonLogger.logRequest("Skill: " + skillId + " created");
				}
			}
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
		return result;
	}

	public void addSkillToPerson(String agentId,String skillId,int level) {
		try {
			CfgPersonQuery personQuery = new CfgPersonQuery();
			personQuery.setUserName(agentId);

			CfgPerson person = configBean.getConfService().retrieveObject(personQuery);
			CfgSkillQuery skillQuery = new CfgSkillQuery();
			skillQuery.setName(skillId);

			CfgSkill skillFromQuery = configBean.getConfService().retrieveObject(skillQuery);
            boolean found = false;

            // update?
            for (CfgSkillLevel csl : person.getAgentInfo().getSkillLevels()) {
                if (csl.getSkill().getName() == skillQuery.getName()) {
                    csl.setLevel(level);
                    found = true;
                    break;
                }
            }

            // or add?
            if (!found) {
                CfgSkillLevel cfgSkillLevel = new CfgSkillLevel(configBean.getConfService(), person);
                cfgSkillLevel.setSkill(skillFromQuery);
                cfgSkillLevel.setLevel(level);
                person.getAgentInfo().getSkillLevels().add(cfgSkillLevel);
            }
            
			person.save();
			CommonLogger.logRequest("Skill: " + + skillFromQuery.getDBID() + ", " + skillId
					+ " added to Person: " + agentId);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}

	public boolean deleteSkill(String skillId) {
		boolean result = false;
		try {
			CfgSkill objectToDelete = readSkill(skillId);
			if (objectToDelete != null) {
				if (objectToDelete.isSaved()) {
					objectToDelete.delete();
				}
				if (!objectToDelete.isSaved()) {
					result = true;
					CommonLogger.logRequest("Skill: " + skillId + " deleted");
				}
			} else {
				result = true;
				CommonLogger.logRequest("Skill: " + skillId + " is not exists");
			}
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
		return result;
	}

	public CfgPerson readPerson(String agentId) {
		try {
			CfgPersonQuery query = new CfgPersonQuery(configBean.getConfService());
			query.setUserName(agentId);
			CfgPerson person = (CfgPerson)configBean.getConfService().retrieveObject(query);
			CommonLogger.logRequest(person.toString());
			return person;
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
			return null;
		}
	}

	public CfgSkill readSkill(String skillId) {
		try {
			CfgSkillQuery query = new CfgSkillQuery(configBean.getConfService());
			query.setName(skillId);
			CfgSkill skill = (CfgSkill)configBean.getConfService().retrieveObject(query);
			CommonLogger.logRequest(skill.toString());
			return skill;
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
			return null;
		}
	}

	/**
	 * 添加一个技能组和一批坐席，并建立关系
	 * 1、删除技能组
	 * 2、添加技能组
	 * 3、添加坐席
	 * 4、建立坐席与技能组关系
	 * @param agentIds
	 * @param skillId
	 * @param level
	 * @return
	 */
	public JSONObject savePersonsAndSkill(String agentIds, String skillId, int level) {
		// TODO Auto-generated method stub
		CommonLogger.logRequest("savePersonsAndSkill===============start=================");
		JSONObject result = new JSONObject();
		result.put("resultCode", RespStatus.ERROR.getResult());
		if (StringUtils.isBlank(agentIds)) {
			result.put("resultDesc", "没有找到坐席id");
			return result;
		}
		if (StringUtils.isBlank(skillId)) {
			result.put("resultDesc", "没有找到技能组id");
			return result;
		}
		if (!deleteSkill(skillId)) {//先删除技能组
			result.put("resultDesc", "删除技能组失败");
			return result;
		}
		if (!createSkill(skillId)) {//然后添加技能组
			result.put("resultDesc", "添加技能组失败");
			return result;
		}
		
		String[] agentIdArray = agentIds.split(",");
		String agentErrorLog = "添加坐席[";
		boolean isAgentError = false;
		for (String agentId: agentIdArray) {
			if (addPerson(agentId)) {//添加坐席
				addSkillToPerson(agentId, skillId, level);//添加技能组到坐席
			} else {
				agentErrorLog += agentId+ ",";
				isAgentError = true;
			}
		}
		agentErrorLog += "]异常";
		if (isAgentError) {
			result.put("resultDesc", agentErrorLog);
		} else {
			result.put("resultCode", RespStatus.SUCCESS.getResult());
			result.put("resultDesc", RespStatus.SUCCESS.getDesc());
		}
		CommonLogger.logRequest("savePersonsAndSkill===============end=================");
		
		return result;
	}
}
