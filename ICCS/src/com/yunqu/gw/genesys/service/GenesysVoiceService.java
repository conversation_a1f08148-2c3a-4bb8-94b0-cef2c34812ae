package com.yunqu.gw.genesys.service;


import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSONObject;
import com.genesyslab.platform.commons.collections.KeyValueCollection;
import com.genesyslab.platform.voice.protocol.ConnectionId;
import com.genesyslab.platform.voice.protocol.tserver.AddressType;
import com.genesyslab.platform.voice.protocol.tserver.AgentWorkMode;
import com.genesyslab.platform.voice.protocol.tserver.ControlMode;
import com.genesyslab.platform.voice.protocol.tserver.MakeCallType;
import com.genesyslab.platform.voice.protocol.tserver.MonitorNextCallType;
import com.genesyslab.platform.voice.protocol.tserver.RegisterMode;
import com.genesyslab.platform.voice.protocol.tserver.requests.agent.RequestAgentLogin;
import com.genesyslab.platform.voice.protocol.tserver.requests.agent.RequestAgentLogout;
import com.genesyslab.platform.voice.protocol.tserver.requests.agent.RequestAgentNotReady;
import com.genesyslab.platform.voice.protocol.tserver.requests.agent.RequestAgentReady;
import com.genesyslab.platform.voice.protocol.tserver.requests.dn.RequestCancelMonitoring;
import com.genesyslab.platform.voice.protocol.tserver.requests.dn.RequestMonitorNextCall;
import com.genesyslab.platform.voice.protocol.tserver.requests.dn.RequestRegisterAddress;
import com.genesyslab.platform.voice.protocol.tserver.requests.dn.RequestUnregisterAddress;
import com.genesyslab.platform.voice.protocol.tserver.requests.party.RequestAnswerCall;
import com.genesyslab.platform.voice.protocol.tserver.requests.party.RequestCompleteConference;
import com.genesyslab.platform.voice.protocol.tserver.requests.party.RequestCompleteTransfer;
import com.genesyslab.platform.voice.protocol.tserver.requests.party.RequestHoldCall;
import com.genesyslab.platform.voice.protocol.tserver.requests.party.RequestInitiateConference;
import com.genesyslab.platform.voice.protocol.tserver.requests.party.RequestInitiateTransfer;
import com.genesyslab.platform.voice.protocol.tserver.requests.party.RequestMakeCall;
import com.genesyslab.platform.voice.protocol.tserver.requests.party.RequestReleaseCall;
import com.genesyslab.platform.voice.protocol.tserver.requests.party.RequestRetrieveCall;
import com.genesyslab.platform.voice.protocol.tserver.requests.party.RequestSingleStepTransfer;
import com.yunqu.gw.genesys.bean.GenesysAgentBean;
import com.yunqu.gw.genesys.cache.AgentCache;
import com.yunqu.gw.iccs.bean.Agent;
import com.yunqu.gw.iccs.bean.CallInfo;
import com.yunqu.gw.iccs.factory.CacheFactory;
import com.yunqu.gw.iccs.log.CommonLogger;
import com.yunqu.gw.iccs.util.Const;


/**
 * <AUTHOR>
 * 类名称：GenesysVoiceService   
 * 创建时间：2018年5月7日 下午7:51:49   
 * @version   
 */
public  class GenesysVoiceService{
	private GenesysAgentBean agent ;
	private static final String QUEUE = "";
	public GenesysVoiceService(String agentID){
		this.agent = new CacheFactory().getCache(Const.CACHE_TYPE).getAgentByAgentID(agentID)
				.getGenesysBean();
	}
	public GenesysVoiceService(GenesysAgentBean agent){
		this.agent = agent;
	}
	
	public void register() {
		try {
			RequestRegisterAddress request = RequestRegisterAddress.create(agent.getAgentDN(), 
					RegisterMode.ModeShare,
					ControlMode.RegisterDefault,
					AddressType.DN); 
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}
	
	public void unRegister(){
		try {
			RequestUnregisterAddress request = RequestUnregisterAddress.create(
					agent.getAgentDN(),
					ControlMode.RegisterDefault);
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}
	
	public void login() {
		try {
			RequestAgentLogin request = RequestAgentLogin.create(agent.getAgentDN(),
					AgentWorkMode.ManualIn);
			request.setThisQueue(QUEUE);
			request.setAgentID(agent.getAgentNo()); 
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}
	
	public void logout() {
		try {
			RequestAgentLogout request = RequestAgentLogout.create(agent.getAgentDN());
			request.setThisQueue(QUEUE);
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}
	
	public void notReady(String reasonCode) {
		try {
			RequestAgentNotReady request = RequestAgentNotReady.create(agent.getAgentDN(), 
					AgentWorkMode.Unknown);
			request.setThisQueue(QUEUE); // ACD Queue agent will use
			request.setReferenceID(agent.getReferenceID());
			KeyValueCollection kev = new KeyValueCollection();
			kev.addString("ReasonCode", reasonCode==null?"100":reasonCode);
			request.setReasons(kev);
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}

	public void ready() {
		try {
			RequestAgentReady request = RequestAgentReady.create(agent.getAgentDN(),
					AgentWorkMode.Unknown);
			request.setThisQueue(QUEUE); // ACD Queue agent will use
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}

	public void answerCall() {
		try {
			RequestAnswerCall request = RequestAnswerCall.create(agent.getAgentDN(), agent.getConnID());
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}
	
	protected Agent getAgent(String agentNo){
		AgentCache cache = new CacheFactory().getCache(Const.CACHE_TYPE);
		return cache.getAgentByAgentID(agentNo);
	}

	public void releaseCall() {
		try {
			//设置坐席挂断属性
			Agent a = getAgent(agent.getAgentNo());
			CallInfo callInfo = a.getCallInfo();
			callInfo.setHangupType("02");
			
			ConnectionId connID = agent.getSecondConnID()==null?agent.getConnID():agent.getSecondConnID();
			RequestReleaseCall request = RequestReleaseCall.create(agent.getAgentDN(),connID);
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}

	public void dialCall(String targetID) {
		try {
			RequestMakeCall request = RequestMakeCall.create(agent.getAgentDN(),
					targetID,
					MakeCallType.Regular);
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}

	public void holdCall() {
		try {
			RequestHoldCall request = RequestHoldCall.create(agent.getAgentDN(), agent.getConnID());
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}

	public void retreiveCall() {
		try {
			RequestRetrieveCall request = RequestRetrieveCall.create(agent.getAgentDN(), agent.getConnID());
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}
	//转移
	public void singleStepTransfer(String targetID) {
		try {
			//设置坐席转移
			Agent a = getAgent(agent.getAgentNo());
			CallInfo callInfo = a.getCallInfo();
			callInfo.setIsTranfer("Y");
			
			RequestSingleStepTransfer request = RequestSingleStepTransfer.create(
					agent.getAgentDN(),
                    agent.getConnID(),                             
                    targetID);    	
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}
	//咨询
	public void initiateTransfer(String targetID) {
		try {
			RequestInitiateTransfer request = RequestInitiateTransfer.create(
					agent.getAgentDN(),                             	
                    agent.getConnID(),                            
                    targetID);    
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}

	public void completeTransfer() {
		try {
			//设置坐席转移
			Agent a = getAgent(agent.getAgentNo());
			CallInfo callInfo = a.getCallInfo();
			callInfo.setIsTranfer("Y");
			
			RequestCompleteTransfer request = RequestCompleteTransfer.create(
					agent.getAgentDN(),                           
                    agent.getConnID(),                           
                    agent.getSecondConnID());                     
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}
	
	public void initiateConference(String target){
		try {
			RequestInitiateConference request = RequestInitiateConference.create(
					agent.getAgentDN(),		//用户话机号
					agent.getConnID(),	//通话ID
					target); //要展开三方的话机号
			request.setReferenceID(agent.getReferenceID());//对于一个连接而言，需要每个都不同（最好递增）
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}
	
	public void completeConference(){
		try {
			RequestCompleteConference request = RequestCompleteConference.create(
					agent.getAgentDN(),    //用户话机号
					agent.getConnID(),	//通话ID
					agent.getSecondConnID());//RequestInitiateConference带来的另一个connID
			request.setReferenceID(agent.getReferenceID());//对于一个连接而言，需要每个都不同（最好递增）
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}
	
	public void monitorNextCall(String target){
		try {
			RequestMonitorNextCall request = RequestMonitorNextCall.create(
					agent.getAgentDN(), 
					target, 
					MonitorNextCallType.OneCall);
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}
	
	
	public void cancelMonitoring(String target){
		try {
			RequestCancelMonitoring request = RequestCancelMonitoring.create(
					agent.getAgentDN(), 
					target);
			request.setReferenceID(agent.getReferenceID());
			CommonLogger.logRequest(request.toString());
			agent.getProtocol().send(request);
		} catch (Exception e) {
			CommonLogger.logException(e.getMessage());
		}
	}
}
