package com.yunqu.gw.genesys.bean;


import com.genesyslab.platform.commons.protocol.Message;

/**
 * <AUTHOR>
 * 类名称：GenesysMessage   
 * 创建时间：2018年5月7日 下午7:50:44   
 * @version   
 */
public class GenesysMessage {
	public static final int TYPE_TSERVER = 0;
	public static final int TYPE_STATSERVER = 1;
	public static final int TYPE_CONFIGSERVER = 2;
	public static final int TYPE_INSERVER = 3;
	
	private int Type;
	private Message message;
	private String id;
	public int getType() {
		return Type;
	}
	public void setType(int type) {
		Type = type;
	}
	public Message getMessage() {
		return message;
	}
	public void setMessage(Message message) {
		this.message = message;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	
	
}
