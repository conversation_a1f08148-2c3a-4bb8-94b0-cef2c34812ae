package com.yunqu.gw.genesys.bean;

import java.util.EventObject;

import com.genesyslab.platform.applicationblocks.com.ConfEvent;
import com.genesyslab.platform.applicationblocks.com.ConfServiceFactory;
import com.genesyslab.platform.applicationblocks.com.IConfService;
import com.genesyslab.platform.applicationblocks.com.objects.CfgDeltaApplication;
import com.genesyslab.platform.applicationblocks.commons.Predicate;
import com.genesyslab.platform.applicationblocks.commons.broker.Subscriber;
import com.genesyslab.platform.commons.collections.KeyValueCollection;
import com.genesyslab.platform.commons.collections.KeyValuePair;
import com.genesyslab.platform.commons.protocol.ChannelClosedEvent;
import com.genesyslab.platform.commons.protocol.ChannelErrorEvent;
import com.genesyslab.platform.commons.protocol.ChannelListener;
import com.genesyslab.platform.commons.protocol.Endpoint;
import com.genesyslab.platform.configuration.protocol.ConfServerProtocol;
import com.genesyslab.platform.configuration.protocol.types.CfgAppType;
import com.yunqu.gw.iccs.log.CommonLogger;

/**
 * <AUTHOR>
 * 类名称：ConfigBean   
 * 创建时间：2018年5月7日 下午7:50:30   
 * @version   
 */
public class ConfigBean extends GenesysOperBean<ConfServerProtocol>{
	private static final String host = "***********";
	private static final int port = 2020;
	private static final String backUpHost = "***********";
	private static final int backUpPort = 2020;
	private static final String CLIENT_NAME = "default";
	private static final String USER_NAME = "default";
	private static final String PASSWORD = "password";
	private IConfService confService;
	
	public ConfigBean(){
		super(host, port, backUpHost, backUpPort);
	}

	@Override
	public ConfServerProtocol initProtocol(Endpoint endpoint) {
		ConfServerProtocol protocol = new ConfServerProtocol(endpoint);
		protocol.setClientName(CLIENT_NAME);
		protocol.setUserName(USER_NAME);
		protocol.setUserPassword(PASSWORD);
		protocol.setClientApplicationType(CfgAppType.CFGSCE.ordinal());
		protocol.addChannelListener(new ChannelListener() {

			public void onChannelClosed(ChannelClosedEvent arg0) {
				CommonLogger.logMessage("Channel Closed: " + arg0.toString());
			}

			public void onChannelError(ChannelErrorEvent arg0) {
				CommonLogger.logMessage("Channel Error: " + arg0.toString());
			}

			public void onChannelOpened(EventObject arg0) {
				CommonLogger.logMessage("Channel Opened: " + arg0.toString());
			}
		});
		protocol.setMessageHandler(arg0->{
			CommonLogger.logMessage("CONFIGSERVER : " + arg0);
		});
		confService = ConfServiceFactory.createConfService(protocol);
		confService.register(new ConfServerEventsHandler());
		return protocol;
	}

	
	
	public IConfService getConfService() {
		return confService;
	}



	private class ConfServerEventsHandler implements Subscriber<ConfEvent> {
		
		public void handle(ConfEvent ev) {
			CommonLogger.logMessage("Incoming Message: " + ev.toString());
			
			if (ev.getCfgObject() instanceof CfgDeltaApplication) {
				CfgDeltaApplication cfgApplication = (CfgDeltaApplication)ev.getCfgObject();
				KeyValueCollection kvc = cfgApplication.getChangedOptions();
				
				if (kvc != null) {
					for (Object o : kvc) {
						KeyValuePair kvp = (KeyValuePair)o;
						CommonLogger.logMessage("key " + kvp.getStringKey() + "=" + kvp.getValue());
					}
				}
			}
		}

		public Predicate<ConfEvent> getFilter() {
			return null;
		}
	}
}
