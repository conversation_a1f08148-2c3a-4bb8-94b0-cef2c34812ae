package com.yunqu.gw.genesys.bean;


import com.genesyslab.platform.applicationblocks.warmstandby.WarmStandbyConfiguration;
import com.genesyslab.platform.applicationblocks.warmstandby.WarmStandbyService;
import com.genesyslab.platform.commons.connection.configuration.PropertyConfiguration;
import com.genesyslab.platform.commons.connection.configuration.ClientADDPOptions.AddpTraceMode;
import com.genesyslab.platform.commons.protocol.ChannelState;
import com.genesyslab.platform.commons.protocol.Endpoint;
import com.genesyslab.platform.commons.protocol.Protocol;
import com.genesyslab.platform.commons.protocol.ProtocolException;


/**
 * <AUTHOR>
 * 类名称：GenesysOperBean   
 * 创建时间：2018年5月7日 下午7:50:54   
 * @param <T>
 * @version   
 */
public abstract class GenesysOperBean<T extends Protocol>{
	private String host;
	private int port;
	private String backUpHost;
	private int backUpPort;
	private T protocol;
	private String fromAddress;//形如127.0.0.1:8090
	
	public GenesysOperBean(){}
	
	public GenesysOperBean(String host,int port,String backUpHost
			,int backUpPort){
		this.host = host;
		this.port = port;
		this.backUpHost = backUpHost;
		this.backUpPort = backUpPort;
		this.warmStandbyService  = buildWarmStandbyService();
		this.warmStandbyService.start();
		connect();
	}
	
	private WarmStandbyService warmStandbyService;
	
	
	public String getFromAddress() {
		return fromAddress;
	}

	public void setFromAddress(String fromAddress) {
		this.fromAddress = fromAddress;
	}

	public T getProtocol() {
		return protocol;
	}
	public WarmStandbyService getWarmStandbyService() {
		return warmStandbyService;
	}
	public void setWarmStandbyService(WarmStandbyService warmStandbyService) {
		this.warmStandbyService = warmStandbyService;
	}
	
	public abstract T initProtocol(Endpoint endpoint);
	
	public Boolean connect(){
		try {
			if (protocol.getState() == ChannelState.Closed){
				protocol.beginOpen();
				return true;
			}
		} catch (ProtocolException e) {
		} catch (IllegalStateException e) {
		}
		return false;
	}
	
	public void disconnect(){
		try {
			this.warmStandbyService.stop();
			// Close if protocol not already closed
			if (protocol.getState() == ChannelState.Opened) {
				// Close the connection asynchronously
				protocol.beginClose();
			}
		} catch (Exception e) {
		}
	}
	
	private WarmStandbyService buildWarmStandbyService(){
		PropertyConfiguration config = new PropertyConfiguration();
		config.setUseAddp(true);//心跳检测
		config.setAddpClientTimeout(10);
		config.setAddpServerTimeout(20);
		config.setAddpTraceMode(AddpTraceMode.Both);
		Endpoint endpoint = new Endpoint(host, port, config);
		Endpoint backupEndpoint = new Endpoint(backUpHost, backUpPort, config);
		this.protocol = initProtocol(endpoint);
		WarmStandbyConfiguration warmStandbyConfig = new WarmStandbyConfiguration(
				endpoint, backupEndpoint);
		warmStandbyConfig.setTimeout(5000);
		warmStandbyConfig.setAttempts((short) 2);
		warmStandbyService = new WarmStandbyService(protocol);
		warmStandbyService.applyConfiguration(warmStandbyConfig);
		return warmStandbyService;
	}
	
	
}
