package com.yunqu.gw.genesys.bean;


import java.util.EventObject;

import com.genesyslab.platform.commons.protocol.ChannelClosedEvent;
import com.genesyslab.platform.commons.protocol.ChannelErrorEvent;
import com.genesyslab.platform.commons.protocol.ChannelListener;
import com.genesyslab.platform.commons.protocol.Endpoint;
import com.genesyslab.platform.voice.protocol.ConnectionId;
import com.genesyslab.platform.voice.protocol.TServerProtocol;
import com.yunqu.gw.genesys.cache.GenesysMessageQueue;
import com.yunqu.gw.iccs.log.CommonLogger;



/**
 * <AUTHOR>
 * 类名称：VoiceAgent   
 * 创建时间：2018年4月18日 下午5:47:11   
 * @version   
 */
public class GenesysAgentBean extends GenesysOperBean<TServerProtocol>{
	private static final String host = "***********";
	private static final int port = 3000;
	private static final String backUpHost = "***********";
	private static final int backUpPort = 3000;
	private static final String CLIENT_NAME = "AgentDesktopForVoice";

	private static final String PASSWORD = "";
	public GenesysAgentBean(String agentNo,String agentDN) {
		super(host, port, backUpHost, backUpPort);
		this.agentNo = agentNo;
		this.agentDN = agentDN;
	}

	private String agentNo;//用户报账号需要使用的工号
	private String agentDN;//话机号
	private int referenceID;//缓存当前用户的操作流水id
	
	private ConnectionId connID;//缓存用户当前通话ID，当前端界面卡死时取这里的connID
	private ConnectionId secondConnID;//缓存用户当前通话ID，当前端界面卡死时取这里的secondConnID

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getAgentDN() {
		return agentDN;
	}

	public void setAgentDN(String agentDN) {
		this.agentDN = agentDN;
	}

	public int getReferenceID() {
		return referenceID++;
	}
	public void setReferenceID(int referenceID) {
		this.referenceID = referenceID;
	}
	
	public ConnectionId getConnID() {
		return connID;
	}

	public void setConnID(ConnectionId connID) {
		this.connID = connID;
	}

	public ConnectionId getSecondConnID() {
		return secondConnID;
	}

	public void setSecondConnID(ConnectionId secondConnID) {
		this.secondConnID = secondConnID;
	}

	@Override
	public TServerProtocol initProtocol(Endpoint endpoint) {
		TServerProtocol protocol = new TServerProtocol(endpoint);
		protocol = new TServerProtocol(endpoint);
		protocol.setClientName(CLIENT_NAME);
		protocol.setClientPassword(PASSWORD);
		protocol.setMessageHandler(arg0->{
			CommonLogger.logMessage("TSERVER : " + arg0);
			GenesysMessage msg = new GenesysMessage();
			msg.setType(GenesysMessage.TYPE_TSERVER);
			msg.setMessage(arg0);
			msg.setId(agentNo);
			GenesysMessageQueue.push(msg);
		});
		protocol.addChannelListener(new ChannelListener() {
			public void onChannelClosed(ChannelClosedEvent arg0) {
				CommonLogger.logMessage("Channel Closed: " + arg0.toString());
			}

			public void onChannelError(ChannelErrorEvent arg0) {
				CommonLogger.logMessage("Channel Error: " + arg0.toString());
			}

			public void onChannelOpened(EventObject arg0) {
				CommonLogger.logMessage("Channel Opened: " + arg0.toString());
			}
		});
		return protocol;
	}

	
	
	
}
