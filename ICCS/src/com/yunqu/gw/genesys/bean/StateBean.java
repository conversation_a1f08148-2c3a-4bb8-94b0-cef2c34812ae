package com.yunqu.gw.genesys.bean;

import java.util.EventObject;

import com.genesyslab.platform.commons.protocol.ChannelClosedEvent;
import com.genesyslab.platform.commons.protocol.ChannelErrorEvent;
import com.genesyslab.platform.commons.protocol.ChannelListener;
import com.genesyslab.platform.commons.protocol.Endpoint;
import com.genesyslab.platform.reporting.protocol.StatServerProtocol;
import com.yunqu.gw.genesys.cache.GenesysMessageQueue;
import com.yunqu.gw.iccs.log.CommonLogger;

/**
 * <AUTHOR>
 * 类名称：StateBean   
 * 创建时间：2018年5月7日 下午7:51:03   
 * @version   
 */
public class StateBean extends GenesysOperBean<StatServerProtocol>{
	
	private static final String host = "***********";
	private static final int port = 5660;
	private static final String CLIENT_NAME = "StatServerClient";
	private static final String backUpHost = host;
	private static final int backUpPort = port;
	
	private String channel;//单例模式做请求，所以一般只允许一个TCP client进行连接，如果多路请求配置信息，返回的state信息将会从最新的client中写入
	
	public StateBean(){
		super(host, port, backUpHost, backUpPort);
	}

	
	
	public String getChannel() {
		return channel;
	}



	public void setChannel(String channel) {
		this.channel = channel;
	}



	@Override
	public StatServerProtocol initProtocol(Endpoint endpoint) {
		StatServerProtocol protocol = new StatServerProtocol(endpoint);
		protocol.setClientName(CLIENT_NAME);
		protocol.addChannelListener(new ChannelListener() {

			public void onChannelClosed(ChannelClosedEvent arg0) {
				CommonLogger.Logger().info("Channel Closed: " + arg0.toString());
			}

			public void onChannelError(ChannelErrorEvent arg0) {
				CommonLogger.Logger().info("Channel Error: " + arg0.toString());
			}

			public void onChannelOpened(EventObject arg0) {
				CommonLogger.Logger().info("Channel Opened: " + arg0.toString());
			}
		});
		protocol.setMessageHandler(arg0->{
			CommonLogger.logMessage("STATSERVER :"+arg0.toString());
			GenesysMessage msg = new GenesysMessage();
			msg.setType(GenesysMessage.TYPE_STATSERVER);
			msg.setMessage(arg0);
			msg.setId(channel);
			GenesysMessageQueue.push(msg);
		});
		return protocol;
	}

}
