package com.yunqu.gw.iccs.bean;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.gw.genesys.bean.GenesysAgentBean;

public class Agent {
	private String agentID;
	private String oldAgentID;
	private Boolean isReady;
	private String entId;
	private String bizSessionId;
	private String channel;
	private String phone;
	private String oldPhone;
	private String agentStatus;
	private String lastStatus;
	private String currentStatusTime;
	private Boolean isCheckKeepAlive;
	private Boolean voiceSwitch;
	private Boolean multimediaSwitch;
	private String skillId;
	private String password;
	private String operMask;
	private String userData;
	private long lastTime;
	private String ipAddress;
	private String workMode;
	private boolean isWithBiz;
	private boolean isMannual;
	private String workReadyTimeout;
	
	private GenesysAgentBean genesysBean;
	private CallInfo callInfo;
	private CallInfo secondCallInfo;

	
	public Agent(JSONObject event){
		this.oldAgentID = event.getString("agentId");
		String agentId = oldAgentID==null?
				"":oldAgentID.split("@")[0];
		this.agentID = agentId;
		this.entId = event.getString("entId");
		this.channel = event.getString("channel");
		this.isCheckKeepAlive = !"off".equals(event.getString("keepAlive"));
		this.voiceSwitch = !"off".equals(event.getString("voiceSwitch"));
		this.multimediaSwitch = "on".equals(event.getString("multimediaSwitch"));
		this.skillId = event.getString("skillId");
		this.oldPhone = event.getString("telephone");
		this.phone = this.oldPhone==null?
					"":this.oldPhone.length()>7+this.entId.length()?
							this.oldPhone.substring(7+this.entId.length()):this.oldPhone;
		this.password = event.getString("password");
		this.bizSessionId = event.getString("bizSessionId");
		this.userData = event.getString("userData");
		this.genesysBean = new GenesysAgentBean(agentID,phone);
		this.lastTime = System.currentTimeMillis();
		this.ipAddress = event.getString("ipAddress");
		this.isWithBiz = "withBiz".equals(event.getString("bizType"));
		this.isMannual = "mannual".equals(event.getString("mannual"));
		this.workReadyTimeout = StringUtils.isNotBlank(event.getString("workReadyTimeout"))?
				event.getString("workReadyTimeout"):"0";
		this.workMode = event.getString("workMode");
	}

	
	
	public String getOldAgentID() {
		return oldAgentID;
	}



	public void setOldAgentID(String oldAgentID) {
		this.oldAgentID = oldAgentID;
	}



	public String getOldPhone() {
		return oldPhone;
	}



	public void setOldPhone(String oldPhone) {
		this.oldPhone = oldPhone;
	}



	public String getAgentID() {
		return agentID;
	}
	
	public void setAgentID(String agentID) {
		this.agentID = agentID;
	}
	
	public String getEntId() {
		return entId;
	}

	public void setEntId(String entId) {
		this.entId = entId;
	}

	public String getBizSessionId() {
		return bizSessionId;
	}

	public void setBizSessionId(String bizSessionId) {
		this.bizSessionId = bizSessionId;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getAgentStatus() {
		return agentStatus;
	}

	public void setAgentStatus(String agentStatus) {
		this.agentStatus = agentStatus;
	}

	
	public String getLastStatus() {
		return lastStatus;
	}

	public void setLastStatus(String lastStatus) {
		this.lastStatus = lastStatus;
	}

	public Boolean getIsCheckKeepAlive() {
		return isCheckKeepAlive;
	}

	public void setIsCheckKeepAlive(Boolean isCheckKeepAlive) {
		this.isCheckKeepAlive = isCheckKeepAlive;
	}

	public Boolean getVoiceSwitch() {
		return voiceSwitch;
	}

	public void setVoiceSwitch(Boolean voiceSwitch) {
		this.voiceSwitch = voiceSwitch;
	}

	public Boolean getMultimediaSwitch() {
		return multimediaSwitch;
	}

	public long getLastTime() {
		return lastTime;
	}

	public void setLastTime(long lastTime) {
		this.lastTime = lastTime;
	}

	public void setMultimediaSwitch(Boolean multimediaSwitch) {
		this.multimediaSwitch = multimediaSwitch;
	}

	public String getSkillId() {
		return skillId;
	}

	public void setSkillId(String skillId) {
		this.skillId = skillId;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getCurrentStatusTime() {
		return currentStatusTime;
	}

	public void setCurrentStatusTime(String currentStatusTime) {
		this.currentStatusTime = currentStatusTime;
	}

	public String getUserData() {
		return userData;
	}

	public void setUserData(String userData) {
		this.userData = userData;
	}

	public Boolean getIsReady() {
		return isReady;
	}

	public void setIsReady(Boolean isReady) {
		this.isReady = isReady;
	}


	public String getIpAddress() {
		return ipAddress;
	}



	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}



	public String getOperMask() {
		return operMask;
	}

	public void setOperMask(String operMask) {
		this.operMask = operMask;
	}

	public void setGenesysBean(GenesysAgentBean genesysBean) {
		this.genesysBean = genesysBean;
	}

	public GenesysAgentBean getGenesysBean() {
		return genesysBean;
	}

	public CallInfo getCallInfo() {
		return callInfo;
	}

	public void setCallInfo(CallInfo callInfo) {
		this.callInfo = callInfo;
	}

	public CallInfo getSecondCallInfo() {
		return secondCallInfo;
	}

	public void setSecondCallInfo(CallInfo secondCallInfo) {
		this.secondCallInfo = secondCallInfo;
	}



	public boolean isWithBiz() {
		return isWithBiz;
	}



	public void setWithBiz(boolean isWithBiz) {
		this.isWithBiz = isWithBiz;
	}



	public boolean isMannual() {
		return isMannual;
	}



	public void setMannual(boolean isMannual) {
		this.isMannual = isMannual;
	}



	public String getWorkReadyTimeout() {
		return workReadyTimeout;
	}



	public void setWorkReadyTimeout(String workReadyTimeout) {
		this.workReadyTimeout = workReadyTimeout;
	}



	public String getWorkMode() {
		return workMode;
	}



	public void setWorkMode(String workMode) {
		this.workMode = workMode;
	}
	
	
}
