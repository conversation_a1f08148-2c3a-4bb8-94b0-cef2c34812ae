package com.yunqu.gw.iccs.bean;

public class CallInfo {
	private String connId;
	private String callId;
	private String callUuid; //用于和ami对接转写文本
	private String caller;
	private String called;
	private String agentNo;
	private String origCaller;
	private String origCalled;
	private String createTime;
	private String ivrBeginTime;
	private String ivrAgentTime;
	private String ivrQueueTime;
	private String ivrEndTime;
	private String agentAlertingTime;
	private String agentAnswerTime;
	private String endTime;
	private String createCause;
	private String clearCause;
	private String recordId;
	private String recordFileName;
	private String serviceGroupId;//技能组id
	private String hangupType;//挂断类型
	private String isTranfer = "N";//是否转移
	private String inType;//接入类型
	public String getCallId() {
		return callId;
	}
	public void setCallId(String callId) {
		this.callId = callId;
	}
	public String getCaller() {
		return caller;
	}
	public void setCaller(String caller) {
		this.caller = caller;
	}
	public String getCalled() {
		return called;
	}
	public void setCalled(String called) {
		this.called = called;
	}
	public String getOrigCaller() {
		return origCaller;
	}
	public void setOrigCaller(String origCaller) {
		this.origCaller = origCaller;
	}
	public String getOrigCalled() {
		return origCalled;
	}
	public void setOrigCalled(String origCalled) {
		this.origCalled = origCalled;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getIvrBeginTime() {
		return ivrBeginTime;
	}
	public void setIvrBeginTime(String ivrBeginTime) {
		this.ivrBeginTime = ivrBeginTime;
	}
	public String getIvrAgentTime() {
		return ivrAgentTime;
	}
	public void setIvrAgentTime(String ivrAgentTime) {
		this.ivrAgentTime = ivrAgentTime;
	}
	public String getIvrQueueTime() {
		return ivrQueueTime;
	}
	public void setIvrQueueTime(String ivrQueueTime) {
		this.ivrQueueTime = ivrQueueTime;
	}
	public String getIvrEndTime() {
		return ivrEndTime;
	}
	public void setIvrEndTime(String ivrEndTime) {
		this.ivrEndTime = ivrEndTime;
	}
	public String getAgentAlertingTime() {
		return agentAlertingTime;
	}
	public void setAgentAlertingTime(String agentAlertingTime) {
		this.agentAlertingTime = agentAlertingTime;
	}
	public String getAgentAnswerTime() {
		return agentAnswerTime;
	}
	public void setAgentAnswerTime(String agentAnswerTime) {
		this.agentAnswerTime = agentAnswerTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getCreateCause() {
		return createCause;
	}
	public void setCreateCause(String createCause) {
		this.createCause = createCause;
	}
	public String getClearCause() {
		return clearCause;
	}
	public void setClearCause(String clearCause) {
		this.clearCause = clearCause;
	}
	public String getRecordId() {
		return recordId;
	}
	public void setRecordId(String recordId) {
		this.recordId = recordId;
	}
	public String getRecordFileName() {
		return recordFileName;
	}
	public void setRecordFileName(String recordFileName) {
		this.recordFileName = recordFileName;
	}
	public String getServiceGroupId() {
		return serviceGroupId;
	}
	public void setServiceGroupId(String serviceGroupId) {
		this.serviceGroupId = serviceGroupId;
	}
	public String getCallUuid() {
		return callUuid;
	}
	public void setCallUuid(String callUuid) {
		this.callUuid = callUuid;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getHangupType() {
		return hangupType;
	}
	public void setHangupType(String hangupType) {
		this.hangupType = hangupType;
	}
	public String getConnId() {
		return connId;
	}
	public void setConnId(String connId) {
		this.connId = connId;
	}
	public String getInType() {
		return inType;
	}
	public void setInType(String inType) {
		this.inType = inType;
	}
	public String getIsTranfer() {
		return isTranfer;
	}
	public void setIsTranfer(String isTranfer) {
		this.isTranfer = isTranfer;
	}
	
}
