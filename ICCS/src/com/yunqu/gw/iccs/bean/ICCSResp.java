package com.yunqu.gw.iccs.bean;

import com.alibaba.fastjson.JSON;
import com.yunqu.gw.iccs.util.RespStatus;




public class ICCSResp {
	private String messageId;
	private String serialId;
	private String timestamp;
	private String entId;
	private String agentId;
	private String bizSessionId;
	private String result;
	private String desc;
	public String getMessageId() {
		return messageId;
	}
	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}
	public String getSerialId() {
		return serialId;
	}
	public void setSerialId(String serialId) {
		this.serialId = serialId;
	}
	public String getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}
	public String getEntId() {
		return entId;
	}
	public void setEntId(String entId) {
		this.entId = entId;
	}
	public String getAgentId() {
		return agentId;
	}
	public void setAgentId(String agentId) {
		this.agentId = agentId;
	}
	public String getBizSessionId() {
		return bizSessionId;
	}
	public void setBizSessionId(String bizSessionId) {
		this.bizSessionId = bizSessionId;
	}
	public String getResult() {
		return result;
	}
	public void setResult(RespStatus result) {
		this.result = result.getResultCode();
		this.desc = result.getDesc();
	}
	public String getDesc() {
		return desc;
	}
	public void setDesc(RespStatus result) {
		this.result = result.getResultCode();
		this.desc = result.getDesc();
	}
	
	public String toJSON(){
		return JSON.toJSONString(this);
	}
}
