package com.yunqu.gw.iccs.bean;




public class Skill {
	private String skillId;
	private String skillName;
	private String logonAgentCount;
	private String idleAgentCount;
	private String busyAgentCount;
	private String aleringAgentCount;
	private String talkAgentCount;
	private String workNotReadyAgentCount;
	private String queueCallCount;
	private String[] agents;
	public String getSkillId() {
		return skillId;
	}
	public void setSkillId(String skillId) {
		this.skillId = skillId;
	}
	public String getSkillName() {
		return skillName;
	}
	public void setSkillName(String skillName) {
		this.skillName = skillName;
	}
	public String getLogonAgentCount() {
		return logonAgentCount;
	}
	public void setLogonAgentCount(String logonAgentCount) {
		this.logonAgentCount = logonAgentCount;
	}
	public String getIdleAgentCount() {
		return idleAgentCount;
	}
	public void setIdleAgentCount(String idleAgentCount) {
		this.idleAgentCount = idleAgentCount;
	}
	public String getBusyAgentCount() {
		return busyAgentCount;
	}
	public void setBusyAgentCount(String busyAgentCount) {
		this.busyAgentCount = busyAgentCount;
	}
	public String getAleringAgentCount() {
		return aleringAgentCount;
	}
	public void setAleringAgentCount(String aleringAgentCount) {
		this.aleringAgentCount = aleringAgentCount;
	}
	public String getTalkAgentCount() {
		return talkAgentCount;
	}
	public void setTalkAgentCount(String talkAgentCount) {
		this.talkAgentCount = talkAgentCount;
	}
	public String getWorkNotReadyAgentCount() {
		return workNotReadyAgentCount;
	}
	public void setWorkNotReadyAgentCount(String workNotReadyAgentCount) {
		this.workNotReadyAgentCount = workNotReadyAgentCount;
	}
	public String getQueueCallCount() {
		return queueCallCount;
	}
	public void setQueueCallCount(String queueCallCount) {
		this.queueCallCount = queueCallCount;
	}
	public String[] getAgents() {
		return agents;
	}
	public void setAgents(String[] agents) {
		this.agents = agents;
	}
	
	
	
	
}
