package com.yunqu.gw.iccs.tcp;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Timer;
import java.util.TimerTask;
import java.util.UUID;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.gw.iccs.handler.AgentGWMessageHandler;
import com.yunqu.gw.iccs.log.CommonLogger;
import com.yunqu.gw.iccs.util.Const;


/**
 * <AUTHOR>
 * 类名称：ICCSTCPServer   
 * 创建时间：2018年5月7日 下午7:52:28   
 * @version   
 */
public class ICCSTCPServer {
	private ServerSocket serverSocket ;
	private Map<String, ICCSTCPClient> map;//缓存客户端，支持多路客户端同时连接
	private static ArrayBlockingQueue<JSONObject> recvQueue;
	private static ArrayBlockingQueue<JSONObject> sendQueue;
	private Thread writeThread;
	private Thread handleReadThread;
	private static final int port = Const.ICCS_TCP_SERVER_PORT;
	private static Boolean  isRunning = false;//是否运行标志位，将此标志位再次置否时终止全部循环，释放资源
	private Timer checkHeartBeatTimer;//检验服务器级别心跳
	private ScheduledExecutorService threadPool = Executors.newScheduledThreadPool(Runtime.getRuntime().availableProcessors());
	
	public  ICCSTCPServer(){
		try {
			isRunning = true;
			CommonLogger.Logger().info("开始初始化ICCSTCP 服务端...");
			serverSocket = new ServerSocket(port);
			CommonLogger.Logger().info("初始化服务端完成，端口为："+port);
			map = new ConcurrentHashMap<String, ICCSTCPClient>();
			writeThread = new Thread(new Write());
			handleReadThread = new Thread(new HandleRead());
			recvQueue = new ArrayBlockingQueue<JSONObject>(Const.MESSAGE_QUEUE_LENGTH);
			sendQueue =  new ArrayBlockingQueue<JSONObject>(Const.MESSAGE_QUEUE_LENGTH);
			checkHeartBeatTimer = new Timer();
			writeThread.start();
			CommonLogger.Logger().info("启动发送队列线程");
			handleReadThread.start();
			CommonLogger.Logger().info("启动处理队列线程");
			checkHeartBeatTimer.schedule(new CheckClientBeat(), 0, Const.HEARTBEAT_TIMER_PERIOD);
		} catch (IOException e) {
			e.printStackTrace();
		}  
	}
	
	public static void send(JSONObject obj){
		if(isRunning) sendQueue.add(obj);
	}
	
	public void startTCPServer(){
		int errorTime =0;
		while(isRunning){
			Socket socket = null;
			try {
				socket = serverSocket.accept();
				CommonLogger.Logger().info("ICCS接入tcp 客户端 ："+socket.getInetAddress()+"_"+socket.getPort());
				ICCSTCPClient client = new ICCSTCPClient(socket,recvQueue,sendQueue);//将队列实例传入client，使client共享队列实例
				String channel = UUID.randomUUID().toString();//生成uuid用作clientid,在消息体中加入id，方便找回client
				client.setChannel(channel);
				map.put(channel, client);
			} catch (Exception e) {
				CommonLogger.Logger().error("ICCS接入tcp 客户端第"+errorTime+"次失败 ："+e.getMessage());
				if(serverSocket.isClosed()){
					if(isRunning){
						CommonLogger.Logger().error("ICCS TCP服务端连接已关闭,尝试重新初始化连接");
						try {
							serverSocket = new ServerSocket(port);
						} catch (IOException e1) {
							CommonLogger.Logger().error("初始化连接失败："+e1.getMessage());
							e1.printStackTrace();
						}
						CommonLogger.Logger().info("休眠5s，尝试重新接入客户端...");
						try {
							this.wait(5000);
						} catch (Exception e2) {
						}
					}
				}
			}
		}
			  
	}
	
	public void shutDown(){
		isRunning = false;
		try {
			CommonLogger.Logger().info("停止心跳检测");
			checkHeartBeatTimer.cancel();
		} catch (Exception e) {
			CommonLogger.Logger().error("[ICCSTCPServer].shutDown ->停止心跳检测失败:"+e.getMessage());
		}
		try {
			Iterator<String> it = map.keySet().iterator();
			CommonLogger.Logger().info("逐个释放客户端");
			while(it.hasNext()){
				releaseClient(it.next());
			}
		} catch (Exception e) {
			CommonLogger.Logger().error("[ICCSTCPServer].shutDown ->释放tcp客户端失败:"+e.getMessage());
		}
		try{
			CommonLogger.Logger().info("正在关闭服务器tcp连接");
			if(serverSocket!=null) serverSocket.close();
			CommonLogger.Logger().info("关闭服务器tcp连接完毕");
		}catch(Exception e){
			CommonLogger.Logger().error("[ICCSTCPServer].shutDown ->关闭服务器tcp失败:"+e.getMessage());
		}
		try {
			recvQueue.clear();
			sendQueue.clear();
			CommonLogger.Logger().info("清空消息队列");
			if(!threadPool.isShutdown()){
				threadPool.shutdown();
				CommonLogger.Logger().info("关闭服务端写队列处理线程池");
			}
		} catch (Exception e) {
			// TODO: handle exception
		}
	}
	
	private class HandleRead implements Runnable{
		@Override
		public void run() {
			while(isRunning){
				try {
					JSONObject obj = recvQueue.take();
					threadPool.execute(()-> new AgentGWMessageHandler().handle(obj));
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			} 
		}
		
	}
	
	private class Write implements Runnable{
		@Override
		public void run() {
			while(isRunning){
				try {
					JSONObject msgObject = sendQueue.take();
					if(msgObject.containsKey("channel")&&map.containsKey(msgObject.get("channel"))){
						map.get(msgObject.get("channel")).send(msgObject);
					}
				} catch (Exception ex) {
					CommonLogger.Logger().error(" socket >> write error,cause: "+ ex.getMessage(),ex);
				}
			}
		}
		
	}
	
	private class CheckClientBeat extends TimerTask{
		@Override
		public void run() {
			Iterator<Entry<String, ICCSTCPClient>> it = map.entrySet().iterator();
			while(it.hasNext()){
				Entry<String, ICCSTCPClient> entry = it.next();
				ICCSTCPClient client = entry.getValue();
				if(client.lastTime<System.currentTimeMillis()-(Const.HEARTBEAT_TIMER_PERIOD*1000*(client.beatLoseCount+1))){
					client.beatLoseCount ++;
					CommonLogger.Logger().info(client+"心跳遗失，目前次数:"+client.beatLoseCount+"次");
					if(client.beatLoseCount>=Const.MAX_HEARTBEAT_LOSE_COUNT){
						CommonLogger.Logger().info(client+"心跳遗失次数达到上限:"+Const.MAX_HEARTBEAT_LOSE_COUNT+"次，准备释放该客户端");
						releaseClient(client);
					}
				}
			}
		}
		
	}
	
	private void releaseClient(String key){
		ICCSTCPClient client = map.get(key);
		releaseClient(client);
	}
	
	private void releaseClient(ICCSTCPClient client){
		if(client!=null){
			String key = client.getChannel();
			CommonLogger.Logger().info("释放客户端："+client);
			client.release();
			map.remove(key);
		}
	}
}
