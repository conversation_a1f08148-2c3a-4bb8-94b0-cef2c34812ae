package com.yunqu.gw.iccs.tcp;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.Socket;
import java.net.SocketException;
import java.util.concurrent.ArrayBlockingQueue;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.gw.iccs.bean.ICCSResp;
import com.yunqu.gw.iccs.log.CommonLogger;
import com.yunqu.gw.iccs.util.RespStatus;


/**
 * <AUTHOR>
 * 类名称：ICCSTCPClient   
 * 创建时间：2018年5月7日 下午7:52:21   
 * @version   
 */
public class ICCSTCPClient {
	private Socket socket;
	private String channel;
	private BufferedReader reader = null;
	private BufferedWriter writer = null;
	private ArrayBlockingQueue<JSONObject> recvQueue;
	private ArrayBlockingQueue<JSONObject> sendQueue;
	public long lastTime = System.currentTimeMillis();
	private Thread readThread;
	private Boolean isRunning = false;
	public int beatLoseCount = 0;
	
	public ICCSTCPClient(Socket socket,ArrayBlockingQueue<JSONObject> recvQueue,
			ArrayBlockingQueue<JSONObject> sendQueue){
		isRunning = true;
		this.socket = socket;
		this.recvQueue = recvQueue;
		this.sendQueue = sendQueue;
		try {
			reader = new BufferedReader(new InputStreamReader(socket.getInputStream(),"GBK"));
			writer = new BufferedWriter(new OutputStreamWriter(socket.getOutputStream(),"GBK"));
			readThread = new Thread(new Read());
			readThread.start();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	
	
	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public void send(JSONObject obj){
		if(isRunning&&socketIsAlive()){
			obj.remove("channel");
			String line = obj.toJSONString();
			try {
				CommonLogger.Logger().info(obj.getString("messageId")+" >> "+line);
				writer.write(line+"\r\n");
				writer.flush();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	
	public void release(){
		isRunning = false;
		CommonLogger.Logger().info("尝试关闭与客户端"+socket.getInetAddress()+"_"+socket.getPort()+"的连接");
		try {
			if(reader!=null){
				reader.close();
			}
			if(writer!=null){
				writer.close();
			}
		} catch (Exception e) {
			CommonLogger.Logger().error("[ICCSTCPClient].release ->释放客户端输入输出流报错："+e.getMessage());
		}
		try {
			if(socket!=null){
				socket.close();
			}
		} catch (Exception e) {
			CommonLogger.Logger().error("[ICCSTCPClient].release ->释放客户端连接报错："+e.getMessage());
		}
	}
	
	private class Read implements Runnable{
		@Override
		public void run() {
			while (isRunning) {
				try {
					String line = reader.readLine();
					try {
						lastTime = System.currentTimeMillis();
						beatLoseCount = 0;
						JSONObject msgObject = JSONObject.parseObject(line);
						CommonLogger.Logger().info(msgObject.getString("messageId") + " << " + line
								+" ---------from "+socket.getInetAddress()+":"+socket.getPort());
						msgObject.put("channel", channel);
						recvQueue.add(msgObject);
					} catch (Exception ex) {
						CommonLogger.Logger()
								.error(" << ccbar package error,cause: " + ex.getMessage() + " content:" + line
										+" ---------from "+socket.getInetAddress()+":"+socket.getPort());
						if(line==null) {
							CommonLogger.Logger().info("reader 异常，出现空值，停止监听");
							break;
						}
						ICCSResp resp = new ICCSResp();
						resp.setResult(RespStatus.PACKAGE_FORMAT_ERROR);
						JSONObject r = JSONObject.parseObject(resp.toJSON());
						r.put("channel", channel);
						sendQueue.add(r);
					}
				} catch (Exception ex) {
					CommonLogger.Logger().error(" Socket reader error,cause: " + ex.getMessage());
					try {
						if(!socketIsAlive()||!socket.getKeepAlive()){
							break;
						}
					} catch (SocketException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		}
		
	}
	public String toString(){
		return socket.getInetAddress()+":"+socket.getPort();
	}
	
	private Boolean socketIsAlive(){
		return !socket.isClosed()&&socket.isConnected();
	}
}
