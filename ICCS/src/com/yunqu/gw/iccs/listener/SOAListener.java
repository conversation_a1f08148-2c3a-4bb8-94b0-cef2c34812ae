package com.yunqu.gw.iccs.listener;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebListener;

import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import com.yunqu.gw.iccs.util.Const;



@WebListener
public class SOAListener extends ServiceContextListener{

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		List<ServiceResource> list = new ArrayList<ServiceResource>();
		ServiceResource resource = new ServiceResource();
		resource.appName = Const.APP_NAME;
		resource.description = "业务模块调用genesys平台配置接口";
		resource.serviceId = "MD_CONFIG_03_02_O_01";
		resource.serviceName = "业务模块调用genesys平台配置接口";
		resource.className = "com.yunqu.gw.iccs.service.RecvConfigService";
		list.add(resource);
		return list;
	}

}
