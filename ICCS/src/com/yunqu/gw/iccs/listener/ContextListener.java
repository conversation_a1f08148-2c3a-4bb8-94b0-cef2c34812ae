package com.yunqu.gw.iccs.listener;

import java.util.Timer;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;








import com.yunqu.gw.genesys.handler.GMQHandler;
import com.yunqu.gw.iccs.log.CommonLogger;
import com.yunqu.gw.iccs.tcp.ICCSTCPServer;
import com.yunqu.gw.iccs.thread.CheckAgentBeatTimer;
import com.yunqu.gw.iccs.util.Const;

public class ContextListener implements ServletContextListener{
	private ICCSTCPServer server;
	private Timer checkAgentBeatTimer;
	public static Boolean isRunning = false;

	@Override
	public void contextDestroyed(ServletContextEvent arg0) {
		isRunning = false;
		if(server!=null){
			server.shutDown();
		}
		CommonLogger.Logger().info("关闭坐席心跳检测线程");
		GMQHandler.getInstance().shutdown();
		checkAgentBeatTimer.cancel();
	}

	@Override
	public void contextInitialized(ServletContextEvent arg0) {
		isRunning = true;
		try {
			server = new ICCSTCPServer();
			new Thread(()->server.startTCPServer()).start();
			GMQHandler.getInstance().handle();
			checkAgentBeatTimer = new Timer();
			CommonLogger.Logger().info("启动坐席心跳检测线程");
			checkAgentBeatTimer.schedule(new CheckAgentBeatTimer(), 0, Const.HEARTBEAT_TIMER_PERIOD);
		} catch (Exception e) {
			CommonLogger.Logger().error("ICCS启动模块失败:"+e.getMessage());
		}
	}

}
