package com.yunqu.gw.iccs.util;




public class Const {
	public static final String CACHE_TYPE = "self";
	public static final String APP_NAME = "ICCS";
	public static final int MESSAGE_QUEUE_LENGTH = 2000;
	public static final int CPU_CORE_NUM = Runtime.getRuntime().availableProcessors();
	public static final int HEARTBEAT_TIMER_PERIOD = Config.getInt("HEARTBEAT_TIMER_PERIOD", 10);
	public static final int MAX_HEARTBEAT_LOSE_COUNT = Config.getInt("MAX_HEARTBEAT_LOSE_COUNT", 3);
	public static final int ICCS_TCP_SERVER_PORT = Config.getInt("ICCS_TCP_SERVER_PORT", 7770);
}
