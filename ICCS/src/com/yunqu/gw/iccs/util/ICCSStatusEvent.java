package com.yunqu.gw.iccs.util;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.gw.iccs.bean.Agent;
import com.yunqu.gw.iccs.bean.CallInfo;

public enum ICCSStatusEvent {
	
	IDLE("IDLE"){
		String getMask(Agent agent) {
			return getMask(logoff,callOut,notReady,monitor,intervent);
		}
		
	},
	ALERTING("ALERTING"){
		String getMask(Agent agent) {
			CallInfo callInfo = agent.getCallInfo();
			if("inbound".equals(callInfo.getCreateCause())){
				return getMask(clearCall,answerCall);
			}else if("outbound".equals(callInfo.getCreateCause())){
				return getMask(clearCall);
			}
			return getMask();
		}
		
	},
	TALK("TALK"){
		String getMask(Agent agent) {
			return getMask(clearCall,transfer,hold,consult);
		}
		
	},
	BUSY("BUSY"){
		String getMask(Agent agent) {
			return getMask(callOut,ready,logoff);
		}
		
	},
	LOGOFF("LOGOFF"){
		String getMask(Agent agent) {
			return getMask(logon);
		}
		
	},
	OCCUPY("OCCUPY"){
		String getMask(Agent agent) {
			return getMask();
		}
		
	},
	WORKNOTREADY("WORKNOTREADY"){
		String getMask(Agent agent) {
			return getMask(ready);
		}
		
	},
	HELD("HELD"){
		String getMask(Agent agent) {
			return getMask(clearCall,retrieve);
		}
		
	},
	CONFERENCED("CONFERENCED"){
		String getMask(Agent agent) {
			return getMask(clearCall);
		}
		
	},
	CONSULTED("CONSULTED"){
		String getMask(Agent agent) {
			return getMask(startConference,clearCall);
		}
		
	},
	MONITORED("MONITORED"){
		String getMask(Agent agent) {
			return getMask(stopMonitor);
		}
		
	},
	INTERVENTED("INTERVENTED"){
		String getMask(Agent agent) {
			// TODO Auto-generated method stub
			return getMask(stopIntervent);
		}
	},
	TEST("TEST"){
		String getMask(Agent agent) {
			return getMask(logon,logoff,acw,callOut,clearCall,answerCall,monitor,transfer,
					ready,notReady,startACW,endACW,hold,retrieve,consult,startConference,
					clearConference,all);
		}
	};
	
	
	private String statusName;
	private static final int logon = 0x00000001;
	private static final int logoff = 0x00000002;
	private static final int acw = 0x00000004;
	private static final int callOut = 0x00000008;
	private static final int clearCall = 0x00000010;
	private static final int answerCall = 0x00000040;
	private static final int monitor = 0x00000080;
	private static final int transfer = 0x00000100;
	private static final int ready = 0x00002000;
	private static final int notReady = 0x00004000;
	private static final int startACW = 0x00008000;
	private static final int endACW = 0x00010000;
	private static final int hold = 0x00100000;
	private static final int retrieve = 0x00200000;
	private static final int consult = 0x01000000;
	private static final int startConference = 0x10000000;
	private static final int clearConference = 0x20000000;
	private static final int stopMonitor = 0x08000000;
	private static final int intervent = 0x00020000;
	private static final int stopIntervent = 0x00800000;
	private static final int all = 0xffffffff;
	
	
	private ICCSStatusEvent(String name){
		this.statusName = name;
	}
	
	public JSONObject getReq(Agent agent){
		agent.setCurrentStatusTime(TimeUtil.getCurrentTime("YYYYMMDDhhmmss"));
		agent.setLastStatus(agent.getAgentStatus());
		agent.setAgentStatus(statusName);
		agent.setOperMask(getMask(agent));
		JSONObject obj = new JSONObject();
		obj.put("messageId", "evtAgentState");
		obj.put("operatorMask", agent.getOperMask());
		obj.put("serialId", IDGenerator.getIDByCurrentTime(30));
		obj.put("timestamp", agent.getCurrentStatusTime());
		obj.put("entId", agent.getEntId());
		obj.put("entId", agent.getEntId());
		obj.put("skillId", agent.getSkillId());
		obj.put("agentId", agent.getOldAgentID());
		obj.put("telephone", agent.getOldPhone());
		obj.put("agentStatus", agent.getAgentStatus());
		obj.put("userData", agent.getUserData());
		obj.put("bizSessionId", agent.getBizSessionId());
		obj.put("currentStatusTime", agent.getCurrentStatusTime());
		obj.put("channel", agent.getChannel());
		return obj;
	}
	
	abstract String getMask(Agent agent);
	
	public static String getMask(int...args){
		int def = 0x00000000;
		for(int i = 0,len=args.length;i<len;i++){
			def += args[i];
		}
		/*String hexString = Integer.toHexString(def);
		StringBuffer result = new StringBuffer("0x");
		for(int i=0,len=8-hexString.length();i<len;i++){result.append("0");}
		result.append(hexString);*/
		return String.valueOf(def);
	}
}
