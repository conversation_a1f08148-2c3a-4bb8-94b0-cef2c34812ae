package com.yunqu.gw.iccs.util;

import org.easitline.common.core.context.AppContext;

import com.yunqu.gw.iccs.log.CommonLogger;

public class Config {
	public static String getString(String configKey,String defaultValue){
		return AppContext.getContext(Const.APP_NAME).getProperty(configKey, defaultValue);
	}
	
	public static String getString(String configKey){
		return getString(configKey,"");
	}
	
	public static int getInt(String configKey,int defaultValue){
		try {
			return Integer.parseInt(getString(configKey, defaultValue+""));
		} catch (Exception e) {
			CommonLogger.Logger().error("[Config].getInt ->获取配置出错："+e.getMessage()+"，configKey:"+configKey);
			return defaultValue;
		}
	}
}
