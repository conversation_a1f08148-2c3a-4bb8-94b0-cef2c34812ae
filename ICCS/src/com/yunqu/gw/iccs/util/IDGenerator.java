package com.yunqu.gw.iccs.util;


public class IDGenerator {
	
	
	private static int counter = 0;

	
	private static synchronized int add() {
		if (counter == 99999)
			counter = -1;
		return ++counter;
	}

	
	private static synchronized String getStrOfAdd() {
		int tempAdd = add();
		if (tempAdd < 10)
			return "0000" + String.valueOf(tempAdd);
		else if(tempAdd<100)
			return "000" + String.valueOf(tempAdd);
		else if(tempAdd<1000)
			return "00" + String.valueOf(tempAdd);
		else if(tempAdd<10000)
			return "0" + String.valueOf(tempAdd);
		else
			return String.valueOf(tempAdd);
	}

	
	public static String getIDByCurrentTime(int length) {
		String currentTime = String.valueOf(System.currentTimeMillis());
		String tempCount = getStrOfAdd();
		if (length > 13) { 
			int extra = length - 13;
			switch (extra) {
			case 1: //length=14
				return currentTime + tempCount.substring(tempCount.length()-1);
			case 2: //length=15
				return currentTime + tempCount.substring(tempCount.length()-2);
			case 3: //length=16
				return currentTime + tempCount.substring(tempCount.length()-3);
			case 4: //length=17
				return currentTime + tempCount.substring(tempCount.length()-4);
			case 5: //length=18
				return currentTime + tempCount;
			default://length>=19
				String zero = "";
				for (int i = 0; i < length - 18; i++)
					zero += "0";
				return currentTime + zero + tempCount;
			}
		} else {
			return currentTime.substring(13 - length);
		}
	}

}
