package com.yunqu.gw.iccs.util;

public enum RespStatus {
	
	SUCCESS("000","成功"),
	PACKAGE_FORMAT_ERROR("001","包文格式错误"),
	INVALID_REQUEST("002","无效的操作请求"),
	INVALID_GROUP("100","无效的技能组"),
	INVALID_AGENT_ID("101","无效的坐席工号"),
	INVALID_AGENT_PASSWORD("102","无效的坐席密码"),
	INVALID_AGENT_STATUS("103","无效的坐席状态"),
	INVALID_CALL_STATUS("104","无效的呼叫状态"),
	AGENT_ALREADY_LOGIN("105","坐席工号已登陆"),
	PHONE_ALREADY_USED("106","话机已被使用"),
	EMPTY_CALLOUT_NUM("107","外呼主显号码为空"),
	INVALID_TELEPHONE("108","无效的话机号码"),
	UNCONFIG_TRANSFER("109","未配置席间转移字冠"),
	ERROR("500","操作失败"),
	UNDEFINED_ERROR("999","未定义错误");
	
	private String result;
	private String desc;
	
	public String getResultCode(){
		return result;
	}
	
	public String getDesc(){
		return desc;
	}
	
	private RespStatus(String result,String desc){
		this.result = result;
		this.desc = desc;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}
}
