package com.yunqu.gw.iccs.util;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.gw.iccs.bean.Agent;
import com.yunqu.gw.iccs.factory.CacheFactory;

public enum IAgentGWEvent {
	cmdHeartBeat("respHeartBeat",false),
	cmdLogin("respLogin",false),
	cmdLogout("respLogout",false),
	cmdNotReady("respNotReady",false),
	cmdReady("respReady",false),
	cmdChangeWorkMode("respChangeWorkMode",false),
	cmdWorkNotReady("respWorkNotReady",false),
	cmdWorkReady("respWorkReady",false),
	cmdMakeCall("respMakeCall",false),
	cmdAnswerCall("respAnswerCall",false),
	cmdClearCall("respClearCall",false),
	cmdHoldCall("respHoldCall",false),
	cmdRetrieveCall("respRetrieveCall",false),
	cmdTransferCall("respTransferCall",false),
	cmdConsultCall("respConsultCall",false),
	cmdConferenceCall("respConferenceCall",false),
	cmdDeleteConferenceCall("cmdDeleteConferenceCall",false),
	cmdMonitorCall("respMonitorCall",false),
	cmdStopMonitorCall("respStopMonitorCall",false),
	cmdInterventCall("respInterventCall",false),
	cmdStopInterventCall("respStopInterventCall", false),
	cmdInterceptCall("respInterceptCall",false),
	queryEnterpriseInfo("respEnterpriseInfo",true),
	queryAgentInfo("respAgentInfo",true),
	querySkillGroupInfoS("respSkillGroupInfo",true),
	queryAllAgent("respQueryAllAgent",true),
	queryLogonAgent("respQueryLogonAgent", true);
	
	private String respID;
	private Boolean isQuery;
	
	private IAgentGWEvent(String respID,Boolean isQuery){
		this.respID = respID;
		this.isQuery = isQuery;
	}
	
	public String getRespId(){
		return this.respID;
	}
	
	public JSONObject getResp(JSONObject obj,RespStatus rStatus){
		if(!this.isQuery){
			return this.buildNormalResp(obj,rStatus);
		}else{
			return this.buildQueryResp(obj,rStatus);
		}
	}
	
	private JSONObject buildNormalResp(JSONObject obj,RespStatus rStatus){
		JSONObject resp = new JSONObject();
		resp.put("messageId", respID);
		resp.put("timestamp", TimeUtil.getCurrentTime("YYYYMMDDhhmmss"));
		resp.put("result",rStatus.getResultCode());
		resp.put("desc", rStatus.getDesc());
		resp.put("serialId", obj.get("serialId"));
		resp.put("entId", obj.get("entId"));
		resp.put("agentId", obj.get("agentId"));
		resp.put("serialId", obj.get("serialId"));
		try {
			String agentId = obj.get("agentId")==null?
					"":obj.getString("agentId").split("@")[0];
			Agent agent = this.getAgent(agentId);
			resp.put("bizSessionId", agent.getBizSessionId());
			resp.put("channel", agent.getChannel());
		} catch (Exception e) {
		}
		return resp;
	}
	
	private JSONObject buildQueryResp(JSONObject obj,RespStatus rStatus){
		obj.put("messageId", respID);
		obj.put("timestamp", TimeUtil.getCurrentTime("YYYYMMDDhhmmss"));
		obj.put("result",rStatus.getResultCode());
		obj.put("desc", rStatus.getDesc());
		try {
			Agent agent = this.getAgent(obj.get("agentId").toString());
			obj.put("bizSessionId", agent.getBizSessionId());
			obj.put("channel", agent.getChannel());
		} catch (Exception e) {
		}
		return obj;
	}
	
	private Agent getAgent(String agentID){
		return new CacheFactory().getCache(Const.CACHE_TYPE).getAgentByAgentID(agentID);
	}
}
