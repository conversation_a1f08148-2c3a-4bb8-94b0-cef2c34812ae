package com.yunqu.gw.iccs.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.gw.iccs.bean.Agent;
import com.yunqu.gw.iccs.bean.CallInfo;

public enum ICCSCallEvent {
	evtAltering("evtAltering",false),
	evtConnected("evtConnected",false),
	evtDisConnected("evtDisConnected",false),
	evtHeld("evtHeld",false),
	evtRetrieved("evtRetrieved",false),
	evtConsultedBegin("evtConsultedBegin",true),
	evtConsultedEnd("evtConsultedEnd",true),
	evtConferencedBegin("evtConferencedBegin",false),
	evtConferencedEnd("evtConferencedEnd",false),
	evtMonitoredBegin("evtMonitoredBegin",false),
	evtMonitoredEnd("evtMonitoredEnd",false);
	
	private String value;
	private Boolean useSecond;
	
	private ICCSCallEvent(String value,Boolean useSecond){
		this.value = value;
		this.useSecond = useSecond;
	}
	
	public JSONObject getReq(Agent agent){
		CallInfo callInfo;
		if(!useSecond){
			callInfo = agent.getCallInfo();
		}else{
			callInfo = agent.getSecondCallInfo();
		}
		JSONObject obj = JSONObject.parseObject(JSON.toJSONString(callInfo));
		obj.put("messageId", value);
		obj.put("channel", agent.getChannel());
		obj.put("serialId", IDGenerator.getIDByCurrentTime(30));
		obj.put("timestamp", TimeUtil.getCurrentTime("YYYYMMDDhhmmss"));
		obj.put("entId", agent.getEntId());
		obj.put("agentId", agent.getOldAgentID());
		obj.put("userData", agent.getUserData());
		obj.put("bizSessionId", agent.getBizSessionId());
		return obj;
	}
}
