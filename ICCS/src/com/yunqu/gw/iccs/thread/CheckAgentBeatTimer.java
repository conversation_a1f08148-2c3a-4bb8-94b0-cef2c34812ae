package com.yunqu.gw.iccs.thread;

import java.util.Iterator;
import java.util.TimerTask;

import com.yunqu.gw.genesys.cache.AgentCache;
import com.yunqu.gw.iccs.bean.Agent;
import com.yunqu.gw.iccs.factory.CacheFactory;
import com.yunqu.gw.iccs.log.CommonLogger;
import com.yunqu.gw.iccs.util.Const;

public class CheckAgentBeatTimer extends TimerTask{
	private AgentCache cache = new CacheFactory().getCache(Const.CACHE_TYPE);
	private int sub = Const.HEARTBEAT_TIMER_PERIOD*1000*Const.MAX_HEARTBEAT_LOSE_COUNT;
	
	@Override
	public void run() {
		Iterator<String> it = cache.getAllAgentID().iterator();
		while(it.hasNext()){
			Agent agent = cache.getAgentByAgentID(it.next());
			if(agent!=null&&agent.getIsCheckKeepAlive()&&agent.getLastTime()<System.currentTimeMillis()-sub){
				CommonLogger.Logger().info("[CheckAgentBeatTimer] ->账号"+agent.getAgentID()+"心跳过期，将强制下线");
				agent.getGenesysBean().disconnect();
				cache.removeVoiceAgentByAgentID(agent.getAgentID());
			}
		}
	}

}
