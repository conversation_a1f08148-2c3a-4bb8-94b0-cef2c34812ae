package com.yunqu.gw.iccs.servlet;

import java.io.IOException;
import java.io.PrintWriter;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.gw.genesys.service.ConfigService;
import com.yunqu.gw.iccs.util.ConfigOperType;
import com.yunqu.gw.iccs.util.RespStatus;


@WebServlet(urlPatterns = {"/config/put"})
public class ConfigServlet extends HttpServlet{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		doPost(req, resp);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		req.setCharacterEncoding("utf-8");
		resp.setCharacterEncoding("utf-8");
		PrintWriter pw = resp.getWriter();
		ConfigOperType oper = ConfigOperType.valueOf(req.getParameter("operType"));
		RespStatus respStatus = RespStatus.SUCCESS;
		ConfigService service = ConfigService.getInstance();
		boolean result = true;
		switch (oper) {
			case addAgent:
				result = service.addPerson(req.getParameter("agentId"));
				if (!result) respStatus = RespStatus.ERROR;
				break;
			case removeAgent:
				result = service.deletePerson(req.getParameter("agentId"));
				if (!result) respStatus = RespStatus.ERROR;
				break;
			case addSkill:
				result = service.createSkill(req.getParameter("skillId"));
				if (!result) respStatus = RespStatus.ERROR;
				break;
			case removeSkill:
				result = service.deleteSkill(req.getParameter("skillId"));
				if (!result) respStatus = RespStatus.ERROR;
				break;
			case addSkillToAgent:
				int level = req.getParameter("level")==null?0:
					Integer.parseInt(req.getParameter("level"));
				service.addSkillToPerson(req.getParameter("agentId"), req.getParameter("skillId")
						, level);
				break;
			case addAgentsAndSkill:
				String agentIds = req.getParameter("agentIds");
				String skillId = req.getParameter("skillId");
				int level2 = req.getParameter("level")==null?0:
					Integer.parseInt(req.getParameter("level"));
				JSONObject obj = service.savePersonsAndSkill(agentIds, skillId, level2);
				pw.write(obj.toJSONString());
				pw.flush();
				pw.close();
				return ;
			default:
				respStatus = RespStatus.INVALID_REQUEST;
				break;
		}
		JSONObject obj = new JSONObject();
		obj.put("resultCode", respStatus.getResultCode());
		obj.put("resultDesc", respStatus.getDesc());
		pw.write(obj.toJSONString());
		pw.flush();
		pw.close();
	}

	
	
}
