package com.yunqu.gw.iccs.log;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import com.yunqu.gw.iccs.util.Const;

public class CommonLogger {
	public static Logger Logger(){
		return LogEngine.getLogger(Const.APP_NAME,Const.APP_NAME);
	}
	
	public static void logRequest(String text){
		Logger().info("<< OUTGOING \n\n"+text);
	}
	
	public static void logMessage(String text){
		Logger().info("<< INCOMING \n\n"+text);	
	}
	
	public static void logException(String text){
		Logger().info("<< EXCEPTION \n\n"+text);
	}
}
