package com.yunqu.gw.iccs.handler;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.gw.genesys.cache.AgentCache;
import com.yunqu.gw.genesys.cache.SkillCache;
import com.yunqu.gw.genesys.service.GenesysVoiceService;
import com.yunqu.gw.iccs.bean.Agent;
import com.yunqu.gw.iccs.bean.CallInfo;
import com.yunqu.gw.iccs.bean.Skill;
import com.yunqu.gw.iccs.factory.CacheFactory;
import com.yunqu.gw.iccs.log.CommonLogger;
import com.yunqu.gw.iccs.tcp.ICCSTCPServer;
import com.yunqu.gw.iccs.util.Const;
import com.yunqu.gw.iccs.util.IAgentGWEvent;
import com.yunqu.gw.iccs.util.RespStatus;

/**
 * <AUTHOR>
 * 类名称：AgentGWMessageHandler   
 * 创建时间：2018年5月7日 下午7:52:12   
 * @version   
 */
public class AgentGWMessageHandler{
	private AgentCache cache = new CacheFactory().getCache(Const.CACHE_TYPE);
	
	public void handle(JSONObject event){
		IAgentGWEvent iAgentGWEvent = IAgentGWEvent.valueOf(event.getString("messageId"));
		RespStatus status = RespStatus.SUCCESS;//默认为success
		String agentId = event.getString("agentId")==null?
						"":event.getString("agentId").split("@")[0];
		Agent agent = cache.getAgentByAgentID(agentId);
		GenesysVoiceService service = null;
		if(agent!=null){
			service = new GenesysVoiceService(agent.getGenesysBean());
			agent.setLastTime(System.currentTimeMillis());
		}
		switch (iAgentGWEvent) {
			case cmdHeartBeat:
				if(agent==null) {
					event.put("messageId", iAgentGWEvent.getRespId());
					event.put("channel", event.getString("channel"));
					ICCSTCPServer.send(event);
				}
				break;
			case cmdLogin:
				if(agent==null){
					agent = new Agent(event);//实例化的时候自动发送connect请求，在connect回调中进行登录操作。
					try {
						cache.setAgent(agent);
					} catch (Exception e) {
						CommonLogger.Logger().error("[AgentGWMessageHandler].handle ->初始化agent缓存错误："+e.getMessage());
						e.printStackTrace();
					}
				}else{
					if(!agent.getGenesysBean().connect()){
						service.register();
					}
				}
				break;
			case cmdLogout:
				service.logout();
				break;
			case cmdNotReady:
				service.notReady(event.getString("reason"));
				break;
			case cmdReady:
				service.ready();
				break;
			case cmdChangeWorkMode:
				agent.setWorkMode(event.getString("workMode"));
				break;
			case cmdWorkNotReady:
				service.notReady(event.getString("reason"));
				break;
			case cmdWorkReady:
				service.ready();
				break;
			case cmdMakeCall:
				agent.setUserData(event.getString("userData"));
				String callType = event.getString("callType");
				callType = StringUtils.isNotBlank(callType)?callType: "1";
				
				if(StringUtils.isNotBlank(event.getString("customPhone"))){
					String phone = event.getString("customPhone");
					if ("2".equals(callType)) {// 如果类型为2(席间呼叫)，则phone为坐席工号，通过坐席工号获取坐席号码
						Agent targetAgent = cache.getAgentByAgentID(phone);
						if(targetAgent != null ){
							phone = targetAgent.getPhone();
						}else{
							CommonLogger.Logger().error(CommonUtil.getClassNameAndMethod(this)+" 外呼席间坐席时,根据号码查询席间坐席时失败:"+phone);
						}
						
					}
					service.dialCall(phone);
				}else{
					status = RespStatus.EMPTY_CALLOUT_NUM;
				}
				break;
			case cmdAnswerCall:
				agent.setUserData(event.getString("userData"));
				service.answerCall();
				break;
			case cmdClearCall:
				service.releaseCall();
				break;
			case cmdHoldCall:// 保持
				service.holdCall();
				break;
			case cmdRetrieveCall:// 恢复
				service.retreiveCall();
				break;
			case cmdTransferCall:// 转移
				agent.setUserData(event.getString("userData"));
				if(!StringUtils.isBlank(event.getString("displayNumber"))){
					if("consult".equals(agent.getAgentStatus())){//咨询后转移
						service.completeTransfer();
					}else{//直接转走，单步转
//						service.singleStepTransfer(event.getString("displayNumber"));
						String displayNumber = event.getString("displayNumber");
						if (StringUtils.isNotBlank(displayNumber) && !"null".equals(displayNumber)) {
							service.singleStepTransfer(displayNumber);
						} else {
							if ("1".equals(event.getString("callType"))) {
								Agent targetAgent = cache.getAgentByAgentID(event.getString("called"));
								CallInfo targetCallInfo = targetAgent.getCallInfo();
								targetCallInfo.setInType("03");
								service.singleStepTransfer(targetAgent.getPhone());
							} else if ("3".equals(event.getString("callType"))) {
								service.singleStepTransfer(event.getString("called"));
							} else {
								status = RespStatus.INVALID_AGENT_ID;
							}
						}
					}
				}else{
					status = RespStatus.EMPTY_CALLOUT_NUM;
				}
				break;
			case cmdConsultCall:// 咨询
				agent.setUserData(event.getString("userData"));
				if(!StringUtils.isBlank(event.getString("displayNumber"))){
//					service.initiateTransfer(event.getString("displayNumber"));
					String displayNumber = event.getString("displayNumber");
					if (StringUtils.isNotBlank(displayNumber) && !"null".equals(displayNumber)) {
						service.initiateTransfer(displayNumber);
					} else {
						if ("1".equals(event.getString("callType"))) {
							Agent targetAgent = cache.getAgentByAgentID(event.getString("called"));
							CallInfo targetCallInfo = targetAgent.getCallInfo();
							targetCallInfo.setInType("03");
							service.initiateTransfer(targetAgent.getPhone());
						} else if ("3".equals(event.getString("callType"))) {
							service.initiateTransfer(event.getString("called"));
						} else {
							status = RespStatus.INVALID_AGENT_ID;
						}
					}
				}else{
					status = RespStatus.EMPTY_CALLOUT_NUM;
				}
				break;
			case cmdConferenceCall:// 三方会议
				agent.setUserData(event.getString("userData"));
				if ("1".equals(event.getString("callType"))) {
					Agent targetAgent = cache.getAgentByAgentID(event.getString("called"));
					CallInfo targetCallInfo = targetAgent.getCallInfo();
					targetCallInfo.setInType("03");
				}
				service.completeConference();
				break;
			case cmdDeleteConferenceCall:
				service.releaseCall();
				break;
			case cmdMonitorCall:
				agent.setUserData(event.getString("userData"));
				service.monitorNextCall(event.getString("called"));
				break;
			case cmdStopMonitorCall:
				service.cancelMonitoring(event.getString("called"));
				break;
			case cmdInterventCall:
				break;
			case cmdInterceptCall:
				break;
			case queryEnterpriseInfo:
				break;
			case queryAgentInfo:
				event.put("skillId", agent.getSkillId());
				event.put("telephone", agent.getPhone());
				event.put("operatorMask", agent.getOperMask());
				event.put("agentStatus", agent.getAgentStatus());
				event.put("currentStatusTiem", agent.getCurrentStatusTime());
				break;
			case querySkillGroupInfoS:
				Skill skill = SkillCache.getSkillBySkillId(event.getString("skillId"));
				if(skill!=null){
					event.put("logonAgentCount", skill.getLogonAgentCount());
					event.put("idleAgentCount", skill.getIdleAgentCount());
					event.put("busyAgentCount", skill.getBusyAgentCount());
					event.put("aleringAgentCount", skill.getAleringAgentCount());
					event.put("talkAgentCount", skill.getTalkAgentCount());
					event.put("workNotReadyAgentCount", skill.getWorkNotReadyAgentCount());
					event.put("queueCallCount", skill.getQueueCallCount());
					String[] agents = skill.getAgents();
					if(agents!=null&&agents.length>0){
						List<JSONObject> agentList = new ArrayList<JSONObject>(agents.length);
						for(int i=0,len=agents.length;i<len;i++){
							Agent tmp = cache.getAgentByAgentID(agents[i]);
							JSONObject obj = new JSONObject();
							obj.put("agentID", tmp.getAgentID());
							obj.put("telephone", tmp.getPhone());
							obj.put("operatorMask", tmp.getOperMask());
							obj.put("agentStatus", tmp.getAgentStatus());
							obj.put("currentStatusTiem", tmp.getCurrentStatusTime());
							agentList.add(obj);
						}
						event.put("agentInfo", agentList);
					}
				}
				break;
			case queryLogonAgent:
				Set<String> allAgentId = cache.getAllAgentID();
				JSONObject agentObj;JSONArray data = new JSONArray();
				for(String id : allAgentId){
					agent = cache.getAgentByAgentID(id);
					agentObj = new JSONObject();
					if(agent!=null){
						agentObj.put("ipAddress", agent.getIpAddress());
						agentObj.put("status", agent.getAgentStatus());
						agentObj.put("agentId", agent.getAgentID());
						agentObj.put("currentStatusTime", agent.getCurrentStatusTime());
						agentObj.put("phone", agent.getPhone());
						if(agent.getCallInfo()!=null){
							agentObj.put("caller", agent.getCallInfo().getCaller());
							agentObj.put("called", agent.getCallInfo().getCalled());
						}
					}else{
						agentObj.put("status", "LOGOFF");
						agentObj.put("agentId", id);
					}
					data.add(agentObj);
				}
				event.put("data", data);
				ICCSTCPServer.send(event);
				return;
			case queryAllAgent:
				if(event.getString("ids")!=null){
					String[] ids = event.getString("ids").split(",");
					JSONObject obj;JSONArray arr = new JSONArray();
					for(int i=0,len=ids.length;i<len;++i){
						agent = cache.getAgentByAgentID(ids[i]);
						obj = new JSONObject();
						if(agent!=null){
							obj.put("ipAddress", agent.getIpAddress());
							obj.put("status", agent.getAgentStatus());
							obj.put("agentId", agent.getAgentID());
							obj.put("currentStatusTime", agent.getCurrentStatusTime());
							obj.put("phone", agent.getPhone());
							if(agent.getCallInfo()!=null){
								obj.put("caller", agent.getCallInfo().getCaller());
								obj.put("called", agent.getCallInfo().getCalled());
							}
						}else{
							obj.put("status", "LOGOFF");
							obj.put("agentId", ids[i]);
						}
						arr.add(obj);
					}
					event.put("data", arr);
					ICCSTCPServer.send(event);
				}
				return;
			default:
				status = RespStatus.INVALID_REQUEST;
				break;
			}
		agent.setChannel(event.getString("channel"));//设置渠道，用作返回来时的客户端
		ICCSTCPServer.send(iAgentGWEvent.getResp(event, status));
	}
}
