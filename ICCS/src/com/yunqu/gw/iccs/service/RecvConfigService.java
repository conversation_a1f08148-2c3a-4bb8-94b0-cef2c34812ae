package com.yunqu.gw.iccs.service;

import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.gw.genesys.service.ConfigService;
import com.yunqu.gw.iccs.log.CommonLogger;
import com.yunqu.gw.iccs.util.ConfigOperType;
import com.yunqu.gw.iccs.util.RespStatus;

public class RecvConfigService extends IService{

	@Override
	public JSONObject invoke(JSONObject arg0) throws ServiceException {
		CommonLogger.Logger().info("收到其他来自其他模块的配置信息："+arg0.toJSONString());
		ConfigOperType oper = ConfigOperType.valueOf(arg0.getString("operType"));
		RespStatus resp = RespStatus.SUCCESS;
		ConfigService service = ConfigService.getInstance();
		boolean result = true;
		switch (oper) {
			case addAgent:
				result = service.addPerson(arg0.getString("agentId"));
				if (!result) resp = RespStatus.ERROR;
				break;
			case removeAgent:
				result = service.deletePerson(arg0.getString("agentId"));
				if (!result) resp = RespStatus.ERROR;
				break;
			case addSkill:
				result = service.createSkill(arg0.getString("skillId"));
				if (!result) resp = RespStatus.ERROR;
				break;
			case removeSkill:
				result = service.deleteSkill(arg0.getString("skillId"));
				if (!result) resp = RespStatus.ERROR;
				break;
			case addSkillToAgent:
				service.addSkillToPerson(arg0.getString("agentId"), arg0.getString("skillId")
						,arg0.getIntValue("level"));
				break;
			case addAgentsAndSkill:
				String agentIds = arg0.getString("agentIds");
				String skillId = arg0.getString("skillId");
				int level = arg0.getIntValue("level");
				return service.savePersonsAndSkill(agentIds, skillId, level);
			default:
				resp = RespStatus.INVALID_REQUEST;
				break;
		}
		JSONObject obj = new JSONObject();
		obj.put("resultCode", resp.getResultCode());
		obj.put("resultDesc", resp.getDesc());
		return obj;
	}

}
