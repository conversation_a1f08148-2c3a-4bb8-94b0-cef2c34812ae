package com.yunqu.gw.iccs.test;


import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.Socket;
import java.net.UnknownHostException;
import java.util.Scanner;
import java.util.Timer;
import java.util.TimerTask;

import com.alibaba.fastjson.JSONObject;

public class Test {
	static Socket socket;
	static BufferedReader reader;
	static BufferedWriter writer;
	static final String agentId = "60001@1000";
	static final String telephone = "50001";
	static final String bizSessionId = "001";
	static final String target = "50000";
	static final String secondTarget = "50002";
	static final String entId = "001";
	static Boolean isRunning = false;
	
	
	public static void main(String[] args){
		try {
			socket = new Socket("localhost",7770);
			isRunning = true;
			reader = new BufferedReader(new InputStreamReader(socket.getInputStream(),"GBK"));
			writer = new BufferedWriter(new OutputStreamWriter(socket.getOutputStream(),"GBK"));
			new Thread(new Write()).start();
			new Thread(new Read()).start();
			new Timer().schedule(new TimerTask() {
				@Override
				public void run() {
					JSONObject obj = new JSONObject();
					obj.put("messageId", "cmdHeartBeat");
					obj.put("agentId", agentId);
					try {
						writer.write(obj.toJSONString()+"\r\n");
						writer.flush();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}, 0, 10000);
		} catch (UnknownHostException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	private static class Write implements Runnable{
		@Override
		public void run() {
			Scanner scan = new Scanner(System.in);
			while(isRunning){
				try {
					if(scan.hasNextLine()){
						String line = scan.nextLine();
						JSONObject obj = new JSONObject();
						obj.put("agentId", agentId);
						obj.put("telephone", telephone);
						obj.put("bizSessionId", bizSessionId);
						obj.put("entId", entId);
						if("logon".equals(line)){
							obj.put("keepAlive", "on");
							obj.put("messageId", "cmdLogin");
						}else if("logoff".equals(line)){
							obj.put("messageId", "cmdLogout");
						}else if("notReady".equals(line)){
							obj.put("messageId", "cmdNotReady");
							obj.put("reason", "cmdNotReady");
						}else if("ready".equals(line)){
							obj.put("messageId", "cmdReady");
						}else if("makeCall".equals(line)){
							obj.put("messageId", "cmdMakeCall");
							obj.put("customPhone", target);
						}else if("answerCall".equals(line)){
							obj.put("messageId", "cmdAnswerCall");
						}else if("clearCall".equals(line)){
							obj.put("messageId", "cmdClearCall");
						}else if("hold".equals(line)){
							obj.put("messageId", "cmdHoldCall");
						}else if("retrieve".equals(line)){
							obj.put("messageId", "cmdRetrieveCall");
						}else if("transfer".equals(line)){
							obj.put("messageId", "cmdTransferCall");
							obj.put("displayNumber", secondTarget);
						}else if("consult".equals(line)){
							obj.put("messageId", "cmdConsultCall");
							obj.put("displayNumber", secondTarget);
						}else if("conference".equals(line)){
							obj.put("messageId", "cmdConferenceCall");
							obj.put("displayNumber", secondTarget);
						}else if("doConsult".equals(line)){
							obj.put("messageId", "cmdNotReady");
							obj.put("displayNumber", secondTarget);
						}else if("monitor".equals(line)){
							obj.put("messageId", "cmdMonitorCall");
							obj.put("called", secondTarget);
						}else if("agent".equals(line)){
							obj.put("messageId", "queryAgentInfo");
						}else if("skill".equals(line)){
							obj.put("messageId", "querySkillGroupInfoS");
						}else if("ent".equals(line)){
							obj.put("messageId", "queryEnterpriseInfo");
						}
						writer.write(obj.toJSONString()+"\r\n");
						writer.flush();
					}
				} catch (Exception e) {
					System.out.println("write:"+e.getMessage());
					try {
						scan.close();
					} catch (Exception e2) {
						// TODO: handle exception
					}
				}
			}
		}
		
	}
	
	private static class Read implements Runnable{

		@Override
		public void run() {
			while(isRunning){
				try {
					String line = reader.readLine();
					if(line.indexOf("respHeartBeat")>0){
						continue;
					}
					System.out.println(line);
				} catch (Exception e) {
					System.out.println("read:"+e.getMessage());
					isRunning = false;
				}
			}
		}
		
	}
}
