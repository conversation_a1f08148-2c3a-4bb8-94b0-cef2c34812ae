<?xml version="1.0" encoding="UTF-8"?>
<resources>
   <!-- <resource id = "testapp_item1" name  ="分类导航"  url = ""  type = "2" portal = ""  state = "0"  order = "1" >
   		<resource id = "testapp_item1_new" name  ="新闻类"  url = ""  type = "2" portal = ""  state = "0"  order = "1" >
   		  <resource id = "testapp_item1_new_sina" name  ="新浪新闻"  url = "http://www.sina.com.cn"  type = "2" portal = ""  state = "0"  index = "1" />
   		  <resource id = "testapp_item1_news_baidu" name  ="百度新闻"  url = "http://news.baidu.com"  type = "2" portal = ""  state = "0"  index = "2"/>
   		  <resource id = "testapp_item1_news_sohu" name  ="搜狐新闻"  url = "http://www.sohu.com"  type = "2" portal = ""  state = "0"  index = "3" />
   		  <resource id = "testapp_item1_new_163" name  ="网易新闻"  url = "http://www.163.com"  type = "2" portal = ""  state = "0"  index = "4"/>
   		</resource>
   		<resource id = "testapp_item1_wh" name  ="新闻维护管理"  url = ""  type = "2" portal = ""  state = "0"  index = "2" >
   			<resource id = "testapp_item1_wh_c" name  ="新闻目录管理"  url = "/servlet/new?action=catolog" type = "2" portal = ""  state = "0"  index = "1" ></resource>
   			<resource id = "testapp_item1_wh_f" name  ="新闻发布管理"  url = "/servlet/new?action=catolog" type = "2" portal = ""  state = "0"  index = "2" >
   			     <resource id = "testapp_item1_wh_f_delete" name  ="删除新闻"  url = ""  type = "3" portal = ""  state = "0"  index = "1" />
   			     <resource id = "testapp_item1_wh_f_update" name  ="编辑新闻"  url = ""  type = "3" portal = ""  state = "0"  index = "2" />
   			</resource>
   		</resource>
   		<resource id = "testapp_item2" name  ="QQ邮箱"  url = "http://mail.qq.com" type = "2" portal = ""  state = "0"  index = "3" />
   		<resource id = "testapp_item3" name  ="189邮箱"  url = "http://mail.189.cn" type = "2"  portal = ""  state = "0"  index = "4" />
   </resource> -->
</resources>
